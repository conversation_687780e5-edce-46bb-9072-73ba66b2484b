"use client";
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.DatabaseArrowUpRegular = exports.DatabaseArrowUpFilled = exports.DatabaseArrowRightRegular = exports.DatabaseArrowRightFilled = exports.DatabaseArrowDownRegular = exports.DatabaseArrowDownFilled = exports.DatabaseRegular = exports.DatabaseFilled = exports.DataWhiskerRegular = exports.DataWhiskerFilled = exports.DataWaterfallRegular = exports.DataWaterfallFilled = exports.DataUsageToolboxRegular = exports.DataUsageToolboxFilled = exports.DataUsageSparkleRegular = exports.DataUsageSparkleFilled = exports.DataUsageSettingsRegular = exports.DataUsageSettingsFilled = exports.DataUsageEditRegular = exports.DataUsageEditFilled = exports.DataUsageCheckmarkRegular = exports.DataUsageCheckmarkFilled = exports.DataUsageRegular = exports.DataUsageFilled = exports.DataTrendingRegular = exports.DataTrendingFilled = exports.DataTreemapRegular = exports.DataTreemapFilled = exports.DataSunburstRegular = exports.DataSunburstFilled = exports.DataScatterRegular = exports.DataScatterFilled = exports.DataPieRegular = exports.DataPieFilled = exports.DataLineRegular = exports.DataLineFilled = exports.DataHistogramRegular = exports.DataHistogramFilled = exports.DataFunnelRegular = exports.DataFunnelFilled = exports.DataBarVerticalStarRegular = exports.DataBarVerticalStarFilled = exports.DataBarVerticalEditRegular = exports.DataBarVerticalEditFilled = exports.DataBarVerticalAscendingRegular = exports.DataBarVerticalAscendingFilled = exports.DataBarVerticalArrowDownRegular = exports.DataBarVerticalArrowDownFilled = exports.DataBarVerticalAddRegular = exports.DataBarVerticalAddFilled = void 0;
exports.DesktopArrowDownOffRegular = exports.DesktopArrowDownOffFilled = exports.DesktopArrowDownRegular = exports.DesktopArrowDownFilled = exports.DesktopRegular = exports.DesktopFilled = exports.DeskSparkleRegular = exports.DeskSparkleFilled = exports.DeskMultipleRegular = exports.DeskMultipleFilled = exports.DeskRegular = exports.DeskFilled = exports.DesignIdeasRegular = exports.DesignIdeasFilled = exports.DentistRegular = exports.DentistFilled = exports.DeleteOffRegular = exports.DeleteOffFilled = exports.DeleteLinesRegular = exports.DeleteLinesFilled = exports.DeleteDismissRegular = exports.DeleteDismissFilled = exports.DeleteArrowBackRegular = exports.DeleteArrowBackFilled = exports.DeleteRegular = exports.DeleteFilled = exports.DecimalArrowRightRegular = exports.DecimalArrowRightFilled = exports.DecimalArrowLeftRegular = exports.DecimalArrowLeftFilled = exports.DatabaseWindowRegular = exports.DatabaseWindowFilled = exports.DatabaseWarningRegular = exports.DatabaseWarningFilled = exports.DatabaseSwitchRegular = exports.DatabaseSwitchFilled = exports.DatabaseSearchRegular = exports.DatabaseSearchFilled = exports.DatabasePlugConnectedRegular = exports.DatabasePlugConnectedFilled = exports.DatabasePersonRegular = exports.DatabasePersonFilled = exports.DatabaseMultipleRegular = exports.DatabaseMultipleFilled = exports.DatabaseLinkRegular = exports.DatabaseLinkFilled = exports.DatabaseLightningRegular = exports.DatabaseLightningFilled = exports.DatabaseCheckmarkRegular = exports.DatabaseCheckmarkFilled = void 0;
exports.DialpadOffRegular = exports.DialpadOffFilled = exports.DialpadRegular = exports.DialpadFilled = exports.DiagramRegular = exports.DiagramFilled = exports.DeviceMeetingRoomRemoteRegular = exports.DeviceMeetingRoomRemoteFilled = exports.DeviceMeetingRoomRegular = exports.DeviceMeetingRoomFilled = exports.DeviceEqRegular = exports.DeviceEqFilled = exports.DeveloperBoardSearchRegular = exports.DeveloperBoardSearchFilled = exports.DeveloperBoardLightningToolboxRegular = exports.DeveloperBoardLightningToolboxFilled = exports.DeveloperBoardLightningRegular = exports.DeveloperBoardLightningFilled = exports.DeveloperBoardRegular = exports.DeveloperBoardFilled = exports.DesktopTowerRegular = exports.DesktopTowerFilled = exports.DesktopToolboxRegular = exports.DesktopToolboxFilled = exports.DesktopSyncRegular = exports.DesktopSyncFilled = exports.DesktopSpeakerOffRegular = exports.DesktopSpeakerOffFilled = exports.DesktopSpeakerRegular = exports.DesktopSpeakerFilled = exports.DesktopSignalRegular = exports.DesktopSignalFilled = exports.DesktopPulseRegular = exports.DesktopPulseFilled = exports.DesktopOffRegular = exports.DesktopOffFilled = exports.DesktopMacRegular = exports.DesktopMacFilled = exports.DesktopKeyboardRegular = exports.DesktopKeyboardFilled = exports.DesktopFlowRegular = exports.DesktopFlowFilled = exports.DesktopEditRegular = exports.DesktopEditFilled = exports.DesktopCursorRegular = exports.DesktopCursorFilled = exports.DesktopCheckmarkRegular = exports.DesktopCheckmarkFilled = exports.DesktopArrowRightRegular = exports.DesktopArrowRightFilled = void 0;
exports.DocumentBriefcaseRegular = exports.DocumentBriefcaseFilled = exports.DocumentBorderPrintRegular = exports.DocumentBorderPrintFilled = exports.DocumentBorderRegular = exports.DocumentBorderFilled = exports.DocumentArrowUpRegular = exports.DocumentArrowUpFilled = exports.DocumentArrowRightRegular = exports.DocumentArrowRightFilled = exports.DocumentArrowLeftRegular = exports.DocumentArrowLeftFilled = exports.DocumentArrowDownRegular = exports.DocumentArrowDownFilled = exports.DocumentAddRegular = exports.DocumentAddFilled = exports.DocumentRegular = exports.DocumentFilled = exports.Document100Regular = exports.Document100Filled = exports.DoctorRegular = exports.DoctorFilled = exports.DockRowRegular = exports.DockRowFilled = exports.DockRegular = exports.DockFilled = exports.DividerTallRegular = exports.DividerTallFilled = exports.DividerShortRegular = exports.DividerShortFilled = exports.DiversityRegular = exports.DiversityFilled = exports.DismissSquareMultipleRegular = exports.DismissSquareMultipleFilled = exports.DismissSquareRegular = exports.DismissSquareFilled = exports.DismissCircleRegular = exports.DismissCircleFilled = exports.DismissRegular = exports.DismissFilled = exports.DishwasherRegular = exports.DishwasherFilled = exports.DirectionsRegular = exports.DirectionsFilled = exports.DiamondDismissRegular = exports.DiamondDismissFilled = exports.DiamondRegular = exports.DiamondFilled = exports.DialpadQuestionMarkRegular = exports.DialpadQuestionMarkFilled = void 0;
exports.DocumentFooterDismissRegular = exports.DocumentFooterDismissFilled = exports.DocumentFooterRegular = exports.DocumentFooterFilled = exports.DocumentFolderRegular = exports.DocumentFolderFilled = exports.DocumentFlowchartRegular = exports.DocumentFlowchartFilled = exports.DocumentFitRegular = exports.DocumentFitFilled = exports.DocumentErrorRegular = exports.DocumentErrorFilled = exports.DocumentEndnoteRegular = exports.DocumentEndnoteFilled = exports.DocumentEditRegular = exports.DocumentEditFilled = exports.DocumentDismissRegular = exports.DocumentDismissFilled = exports.DocumentDatabaseRegular = exports.DocumentDatabaseFilled = exports.DocumentDataLockRegular = exports.DocumentDataLockFilled = exports.DocumentDataLinkRegular = exports.DocumentDataLinkFilled = exports.DocumentDataRegular = exports.DocumentDataFilled = exports.DocumentCubeRegular = exports.DocumentCubeFilled = exports.DocumentCssRegular = exports.DocumentCssFilled = exports.DocumentCopyRegular = exports.DocumentCopyFilled = exports.DocumentChevronDoubleRegular = exports.DocumentChevronDoubleFilled = exports.DocumentCheckmarkRegular = exports.DocumentCheckmarkFilled = exports.DocumentCatchUpRegular = exports.DocumentCatchUpFilled = exports.DocumentBulletListOffRegular = exports.DocumentBulletListOffFilled = exports.DocumentBulletListMultipleRegular = exports.DocumentBulletListMultipleFilled = exports.DocumentBulletListCubeRegular = exports.DocumentBulletListCubeFilled = exports.DocumentBulletListClockRegular = exports.DocumentBulletListClockFilled = exports.DocumentBulletListArrowLeftRegular = exports.DocumentBulletListArrowLeftFilled = exports.DocumentBulletListRegular = exports.DocumentBulletListFilled = void 0;
exports.DocumentOnePageRegular = exports.DocumentOnePageFilled = exports.DocumentMultipleSyncRegular = exports.DocumentMultipleSyncFilled = exports.DocumentMultipleProhibitedRegular = exports.DocumentMultipleProhibitedFilled = exports.DocumentMultiplePercentRegular = exports.DocumentMultiplePercentFilled = exports.DocumentMultipleRegular = exports.DocumentMultipleFilled = exports.DocumentMentionRegular = exports.DocumentMentionFilled = exports.DocumentMarginsRegular = exports.DocumentMarginsFilled = exports.DocumentLockRegular = exports.DocumentLockFilled = exports.DocumentLinkRegular = exports.DocumentLinkFilled = exports.DocumentLightningRegular = exports.DocumentLightningFilled = exports.DocumentLandscapeSplitHintRegular = exports.DocumentLandscapeSplitHintFilled = exports.DocumentLandscapeSplitRegular = exports.DocumentLandscapeSplitFilled = exports.DocumentLandscapeDataRegular = exports.DocumentLandscapeDataFilled = exports.DocumentLandscapeRegular = exports.DocumentLandscapeFilled = exports.DocumentKeyRegular = exports.DocumentKeyFilled = exports.DocumentJavascriptRegular = exports.DocumentJavascriptFilled = exports.DocumentJavaRegular = exports.DocumentJavaFilled = exports.DocumentImageRegular = exports.DocumentImageFilled = exports.DocumentHeartPulseRegular = exports.DocumentHeartPulseFilled = exports.DocumentHeartRegular = exports.DocumentHeartFilled = exports.DocumentHeaderFooterRegular = exports.DocumentHeaderFooterFilled = exports.DocumentHeaderDismissRegular = exports.DocumentHeaderDismissFilled = exports.DocumentHeaderArrowDownRegular = exports.DocumentHeaderArrowDownFilled = exports.DocumentHeaderRegular = exports.DocumentHeaderFilled = exports.DocumentGlobeRegular = exports.DocumentGlobeFilled = void 0;
exports.DocumentRibbonRegular = exports.DocumentRibbonFilled = exports.DocumentQueueMultipleRegular = exports.DocumentQueueMultipleFilled = exports.DocumentQueueAddRegular = exports.DocumentQueueAddFilled = exports.DocumentQueueRegular = exports.DocumentQueueFilled = exports.DocumentQuestionMarkRegular = exports.DocumentQuestionMarkFilled = exports.DocumentProhibitedRegular = exports.DocumentProhibitedFilled = exports.DocumentPrintRegular = exports.DocumentPrintFilled = exports.DocumentPillRegular = exports.DocumentPillFilled = exports.DocumentPersonRegular = exports.DocumentPersonFilled = exports.DocumentPercentRegular = exports.DocumentPercentFilled = exports.DocumentPdfRegular = exports.DocumentPdfFilled = exports.DocumentPageTopRightRegular = exports.DocumentPageTopRightFilled = exports.DocumentPageTopLeftRegular = exports.DocumentPageTopLeftFilled = exports.DocumentPageTopCenterRegular = exports.DocumentPageTopCenterFilled = exports.DocumentPageNumberRegular = exports.DocumentPageNumberFilled = exports.DocumentPageBreakRegular = exports.DocumentPageBreakFilled = exports.DocumentPageBottomRightRegular = exports.DocumentPageBottomRightFilled = exports.DocumentPageBottomLeftRegular = exports.DocumentPageBottomLeftFilled = exports.DocumentPageBottomCenterRegular = exports.DocumentPageBottomCenterFilled = exports.DocumentOnePageSparkleRegular = exports.DocumentOnePageSparkleFilled = exports.DocumentOnePageMultipleSparkleRegular = exports.DocumentOnePageMultipleSparkleFilled = exports.DocumentOnePageMultipleRegular = exports.DocumentOnePageMultipleFilled = exports.DocumentOnePageLinkRegular = exports.DocumentOnePageLinkFilled = exports.DocumentOnePageColumnsRegular = exports.DocumentOnePageColumnsFilled = exports.DocumentOnePageAddRegular = exports.DocumentOnePageAddFilled = void 0;
exports.DocumentYmlRegular = exports.DocumentYmlFilled = exports.DocumentWidthRegular = exports.DocumentWidthFilled = exports.DocumentToolboxRegular = exports.DocumentToolboxFilled = exports.DocumentTextToolboxRegular = exports.DocumentTextToolboxFilled = exports.DocumentTextLinkRegular = exports.DocumentTextLinkFilled = exports.DocumentTextExtractRegular = exports.DocumentTextExtractFilled = exports.DocumentTextClockRegular = exports.DocumentTextClockFilled = exports.DocumentTextRegular = exports.DocumentTextFilled = exports.DocumentTargetRegular = exports.DocumentTargetFilled = exports.DocumentTableTruckRegular = exports.DocumentTableTruckFilled = exports.DocumentTableSearchRegular = exports.DocumentTableSearchFilled = exports.DocumentTableCubeRegular = exports.DocumentTableCubeFilled = exports.DocumentTableCheckmarkRegular = exports.DocumentTableCheckmarkFilled = exports.DocumentTableArrowRightRegular = exports.DocumentTableArrowRightFilled = exports.DocumentTableRegular = exports.DocumentTableFilled = exports.DocumentSyncRegular = exports.DocumentSyncFilled = exports.DocumentSquareRegular = exports.DocumentSquareFilled = exports.DocumentSplitHintOffRegular = exports.DocumentSplitHintOffFilled = exports.DocumentSplitHintRegular = exports.DocumentSplitHintFilled = exports.DocumentSparkleRegular = exports.DocumentSparkleFilled = exports.DocumentSignatureRegular = exports.DocumentSignatureFilled = exports.DocumentSettingsRegular = exports.DocumentSettingsFilled = exports.DocumentSearchRegular = exports.DocumentSearchFilled = exports.DocumentSaveRegular = exports.DocumentSaveFilled = exports.DocumentSassRegular = exports.DocumentSassFilled = void 0;
exports.DrinkToGoRegular = exports.DrinkToGoFilled = exports.DrinkMargaritaRegular = exports.DrinkMargaritaFilled = exports.DrinkCoffeeRegular = exports.DrinkCoffeeFilled = exports.DrinkBottleOffRegular = exports.DrinkBottleOffFilled = exports.DrinkBottleRegular = exports.DrinkBottleFilled = exports.DrinkBeerRegular = exports.DrinkBeerFilled = exports.DrawerSubtractRegular = exports.DrawerSubtractFilled = exports.DrawerPlayRegular = exports.DrawerPlayFilled = exports.DrawerDismissRegular = exports.DrawerDismissFilled = exports.DrawerArrowDownloadRegular = exports.DrawerArrowDownloadFilled = exports.DrawerAddRegular = exports.DrawerAddFilled = exports.DrawerRegular = exports.DrawerFilled = exports.DrawTextRegular = exports.DrawTextFilled = exports.DrawShapeRegular = exports.DrawShapeFilled = exports.DrawImageRegular = exports.DrawImageFilled = exports.DragRegular = exports.DragFilled = exports.DraftsRegular = exports.DraftsFilled = exports.DoubleTapSwipeUpRegular = exports.DoubleTapSwipeUpFilled = exports.DoubleTapSwipeDownRegular = exports.DoubleTapSwipeDownFilled = exports.DoubleSwipeUpRegular = exports.DoubleSwipeUpFilled = exports.DoubleSwipeDownRegular = exports.DoubleSwipeDownFilled = exports.DoorTagRegular = exports.DoorTagFilled = exports.DoorArrowRightRegular = exports.DoorArrowRightFilled = exports.DoorArrowLeftRegular = exports.DoorArrowLeftFilled = exports.DoorRegular = exports.DoorFilled = void 0;
exports.DumbbellRegular = exports.DumbbellFilled = exports.DualScreenVibrateRegular = exports.DualScreenVibrateFilled = exports.DualScreenVerticalScrollRegular = exports.DualScreenVerticalScrollFilled = exports.DualScreenUpdateRegular = exports.DualScreenUpdateFilled = exports.DualScreenTabletRegular = exports.DualScreenTabletFilled = exports.DualScreenStatusBarRegular = exports.DualScreenStatusBarFilled = exports.DualScreenSpeakerRegular = exports.DualScreenSpeakerFilled = exports.DualScreenSpanRegular = exports.DualScreenSpanFilled = exports.DualScreenSettingsRegular = exports.DualScreenSettingsFilled = exports.DualScreenPaginationRegular = exports.DualScreenPaginationFilled = exports.DualScreenMirrorRegular = exports.DualScreenMirrorFilled = exports.DualScreenLockRegular = exports.DualScreenLockFilled = exports.DualScreenHeaderRegular = exports.DualScreenHeaderFilled = exports.DualScreenGroupRegular = exports.DualScreenGroupFilled = exports.DualScreenDismissRegular = exports.DualScreenDismissFilled = exports.DualScreenDesktopRegular = exports.DualScreenDesktopFilled = exports.DualScreenClosedAlertRegular = exports.DualScreenClosedAlertFilled = exports.DualScreenClockRegular = exports.DualScreenClockFilled = exports.DualScreenArrowUpRegular = exports.DualScreenArrowUpFilled = exports.DualScreenArrowRightRegular = exports.DualScreenArrowRightFilled = exports.DualScreenAddRegular = exports.DualScreenAddFilled = exports.DualScreenRegular = exports.DualScreenFilled = exports.DropRegular = exports.DropFilled = exports.DriveTrainRegular = exports.DriveTrainFilled = exports.DrinkWineRegular = exports.DrinkWineFilled = void 0;
const createFluentFontIcon_1 = require("../../utils/fonts/createFluentFontIcon");
exports.DataBarVerticalAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataBarVerticalAddFilled", "", 2, undefined));
exports.DataBarVerticalAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataBarVerticalAddRegular", "", 2, undefined));
exports.DataBarVerticalArrowDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataBarVerticalArrowDownFilled", "", 2, undefined));
exports.DataBarVerticalArrowDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataBarVerticalArrowDownRegular", "", 2, undefined));
exports.DataBarVerticalAscendingFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataBarVerticalAscendingFilled", "", 2, undefined));
exports.DataBarVerticalAscendingRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataBarVerticalAscendingRegular", "", 2, undefined));
exports.DataBarVerticalEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataBarVerticalEditFilled", "", 2, undefined));
exports.DataBarVerticalEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataBarVerticalEditRegular", "", 2, undefined));
exports.DataBarVerticalStarFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataBarVerticalStarFilled", "", 2, undefined));
exports.DataBarVerticalStarRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataBarVerticalStarRegular", "", 2, undefined));
exports.DataFunnelFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataFunnelFilled", "", 2, undefined));
exports.DataFunnelRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataFunnelRegular", "", 2, undefined));
exports.DataHistogramFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataHistogramFilled", "", 2, undefined));
exports.DataHistogramRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataHistogramRegular", "", 2, undefined));
exports.DataLineFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataLineFilled", "", 2, undefined));
exports.DataLineRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataLineRegular", "", 2, undefined));
exports.DataPieFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataPieFilled", "", 2, undefined));
exports.DataPieRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataPieRegular", "", 2, undefined));
exports.DataScatterFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataScatterFilled", "", 2, undefined));
exports.DataScatterRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataScatterRegular", "", 2, undefined));
exports.DataSunburstFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataSunburstFilled", "", 2, undefined));
exports.DataSunburstRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataSunburstRegular", "", 2, undefined));
exports.DataTreemapFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataTreemapFilled", "", 2, undefined));
exports.DataTreemapRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataTreemapRegular", "", 2, undefined));
exports.DataTrendingFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataTrendingFilled", "", 2, undefined));
exports.DataTrendingRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataTrendingRegular", "", 2, undefined));
exports.DataUsageFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataUsageFilled", "", 2, undefined));
exports.DataUsageRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataUsageRegular", "", 2, undefined));
exports.DataUsageCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataUsageCheckmarkFilled", "", 2, undefined));
exports.DataUsageCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataUsageCheckmarkRegular", "", 2, undefined));
exports.DataUsageEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataUsageEditFilled", "", 2, undefined));
exports.DataUsageEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataUsageEditRegular", "", 2, undefined));
exports.DataUsageSettingsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataUsageSettingsFilled", "", 2, undefined));
exports.DataUsageSettingsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataUsageSettingsRegular", "", 2, undefined));
exports.DataUsageSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataUsageSparkleFilled", "", 2, undefined));
exports.DataUsageSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataUsageSparkleRegular", "", 2, undefined));
exports.DataUsageToolboxFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataUsageToolboxFilled", "", 2, undefined));
exports.DataUsageToolboxRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataUsageToolboxRegular", "", 2, undefined));
exports.DataWaterfallFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataWaterfallFilled", "", 2, undefined));
exports.DataWaterfallRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataWaterfallRegular", "", 2, undefined));
exports.DataWhiskerFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataWhiskerFilled", "", 2, undefined));
exports.DataWhiskerRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataWhiskerRegular", "", 2, undefined));
exports.DatabaseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseFilled", "", 2, undefined));
exports.DatabaseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseRegular", "", 2, undefined));
exports.DatabaseArrowDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseArrowDownFilled", "", 2, undefined));
exports.DatabaseArrowDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseArrowDownRegular", "", 2, undefined));
exports.DatabaseArrowRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseArrowRightFilled", "", 2, undefined));
exports.DatabaseArrowRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseArrowRightRegular", "", 2, undefined));
exports.DatabaseArrowUpFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseArrowUpFilled", "", 2, undefined));
exports.DatabaseArrowUpRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseArrowUpRegular", "", 2, undefined));
exports.DatabaseCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseCheckmarkFilled", "", 2, undefined));
exports.DatabaseCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseCheckmarkRegular", "", 2, undefined));
exports.DatabaseLightningFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseLightningFilled", "", 2, undefined));
exports.DatabaseLightningRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseLightningRegular", "", 2, undefined));
exports.DatabaseLinkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseLinkFilled", "", 2, undefined));
exports.DatabaseLinkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseLinkRegular", "", 2, undefined));
exports.DatabaseMultipleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseMultipleFilled", "", 2, undefined));
exports.DatabaseMultipleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseMultipleRegular", "", 2, undefined));
exports.DatabasePersonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabasePersonFilled", "", 2, undefined));
exports.DatabasePersonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabasePersonRegular", "", 2, undefined));
exports.DatabasePlugConnectedFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabasePlugConnectedFilled", "", 2, undefined));
exports.DatabasePlugConnectedRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabasePlugConnectedRegular", "", 2, undefined));
exports.DatabaseSearchFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseSearchFilled", "", 2, undefined));
exports.DatabaseSearchRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseSearchRegular", "", 2, undefined));
exports.DatabaseSwitchFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseSwitchFilled", "", 2, undefined));
exports.DatabaseSwitchRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseSwitchRegular", "", 2, undefined));
exports.DatabaseWarningFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseWarningFilled", "", 2, undefined));
exports.DatabaseWarningRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseWarningRegular", "", 2, undefined));
exports.DatabaseWindowFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseWindowFilled", "", 2, undefined));
exports.DatabaseWindowRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DatabaseWindowRegular", "", 2, undefined));
exports.DecimalArrowLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DecimalArrowLeftFilled", "", 2, undefined));
exports.DecimalArrowLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DecimalArrowLeftRegular", "", 2, undefined));
exports.DecimalArrowRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DecimalArrowRightFilled", "", 2, undefined));
exports.DecimalArrowRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DecimalArrowRightRegular", "", 2, undefined));
exports.DeleteFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeleteFilled", "", 2, undefined));
exports.DeleteRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeleteRegular", "", 2, undefined));
exports.DeleteArrowBackFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeleteArrowBackFilled", "", 2, undefined));
exports.DeleteArrowBackRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeleteArrowBackRegular", "", 2, undefined));
exports.DeleteDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeleteDismissFilled", "", 2, undefined));
exports.DeleteDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeleteDismissRegular", "", 2, undefined));
exports.DeleteLinesFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeleteLinesFilled", "", 2, undefined));
exports.DeleteLinesRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeleteLinesRegular", "", 2, undefined));
exports.DeleteOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeleteOffFilled", "", 2, undefined));
exports.DeleteOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeleteOffRegular", "", 2, undefined));
exports.DentistFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DentistFilled", "", 2, undefined));
exports.DentistRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DentistRegular", "", 2, undefined));
exports.DesignIdeasFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesignIdeasFilled", "", 2, undefined));
exports.DesignIdeasRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesignIdeasRegular", "", 2, undefined));
exports.DeskFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeskFilled", "", 2, undefined));
exports.DeskRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeskRegular", "", 2, undefined));
exports.DeskMultipleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeskMultipleFilled", "", 2, undefined));
exports.DeskMultipleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeskMultipleRegular", "", 2, undefined));
exports.DeskSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeskSparkleFilled", "", 2, undefined));
exports.DeskSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeskSparkleRegular", "", 2, undefined));
exports.DesktopFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopFilled", "", 2, undefined));
exports.DesktopRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopRegular", "", 2, undefined));
exports.DesktopArrowDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopArrowDownFilled", "", 2, undefined));
exports.DesktopArrowDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopArrowDownRegular", "", 2, undefined));
exports.DesktopArrowDownOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopArrowDownOffFilled", "", 2, undefined));
exports.DesktopArrowDownOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopArrowDownOffRegular", "", 2, undefined));
exports.DesktopArrowRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopArrowRightFilled", "", 2, undefined));
exports.DesktopArrowRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopArrowRightRegular", "", 2, undefined));
exports.DesktopCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopCheckmarkFilled", "", 2, undefined));
exports.DesktopCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopCheckmarkRegular", "", 2, undefined));
exports.DesktopCursorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopCursorFilled", "", 2, undefined));
exports.DesktopCursorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopCursorRegular", "", 2, undefined));
exports.DesktopEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopEditFilled", "", 2, undefined));
exports.DesktopEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopEditRegular", "", 2, undefined));
exports.DesktopFlowFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopFlowFilled", "", 2, undefined));
exports.DesktopFlowRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopFlowRegular", "", 2, undefined));
exports.DesktopKeyboardFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopKeyboardFilled", "", 2, undefined));
exports.DesktopKeyboardRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopKeyboardRegular", "", 2, undefined));
exports.DesktopMacFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopMacFilled", "", 2, undefined));
exports.DesktopMacRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopMacRegular", "", 2, undefined));
exports.DesktopOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopOffFilled", "", 2, undefined));
exports.DesktopOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopOffRegular", "", 2, undefined));
exports.DesktopPulseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopPulseFilled", "", 2, undefined));
exports.DesktopPulseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopPulseRegular", "", 2, undefined));
exports.DesktopSignalFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopSignalFilled", "", 2, undefined));
exports.DesktopSignalRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopSignalRegular", "", 2, undefined));
exports.DesktopSpeakerFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopSpeakerFilled", "", 2, undefined));
exports.DesktopSpeakerRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopSpeakerRegular", "", 2, undefined));
exports.DesktopSpeakerOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopSpeakerOffFilled", "", 2, undefined));
exports.DesktopSpeakerOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopSpeakerOffRegular", "", 2, undefined));
exports.DesktopSyncFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopSyncFilled", "", 2, undefined));
exports.DesktopSyncRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopSyncRegular", "", 2, undefined));
exports.DesktopToolboxFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopToolboxFilled", "", 2, undefined));
exports.DesktopToolboxRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopToolboxRegular", "", 2, undefined));
exports.DesktopTowerFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopTowerFilled", "", 2, undefined));
exports.DesktopTowerRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DesktopTowerRegular", "", 2, undefined));
exports.DeveloperBoardFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeveloperBoardFilled", "", 2, undefined));
exports.DeveloperBoardRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeveloperBoardRegular", "", 2, undefined));
exports.DeveloperBoardLightningFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeveloperBoardLightningFilled", "", 2, undefined));
exports.DeveloperBoardLightningRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeveloperBoardLightningRegular", "", 2, undefined));
exports.DeveloperBoardLightningToolboxFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeveloperBoardLightningToolboxFilled", "", 2, undefined));
exports.DeveloperBoardLightningToolboxRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeveloperBoardLightningToolboxRegular", "", 2, undefined));
exports.DeveloperBoardSearchFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeveloperBoardSearchFilled", "", 2, undefined));
exports.DeveloperBoardSearchRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeveloperBoardSearchRegular", "", 2, undefined));
exports.DeviceEqFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeviceEqFilled", "", 2, undefined));
exports.DeviceEqRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeviceEqRegular", "", 2, undefined));
exports.DeviceMeetingRoomFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeviceMeetingRoomFilled", "", 2, undefined));
exports.DeviceMeetingRoomRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeviceMeetingRoomRegular", "", 2, undefined));
exports.DeviceMeetingRoomRemoteFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeviceMeetingRoomRemoteFilled", "", 2, undefined));
exports.DeviceMeetingRoomRemoteRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DeviceMeetingRoomRemoteRegular", "", 2, undefined));
exports.DiagramFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DiagramFilled", "", 2, undefined));
exports.DiagramRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DiagramRegular", "", 2, undefined));
exports.DialpadFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DialpadFilled", "", 2, undefined));
exports.DialpadRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DialpadRegular", "", 2, undefined));
exports.DialpadOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DialpadOffFilled", "", 2, undefined));
exports.DialpadOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DialpadOffRegular", "", 2, undefined));
exports.DialpadQuestionMarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DialpadQuestionMarkFilled", "", 2, undefined));
exports.DialpadQuestionMarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DialpadQuestionMarkRegular", "", 2, undefined));
exports.DiamondFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DiamondFilled", "", 2, undefined));
exports.DiamondRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DiamondRegular", "", 2, undefined));
exports.DiamondDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DiamondDismissFilled", "", 2, undefined));
exports.DiamondDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DiamondDismissRegular", "", 2, undefined));
exports.DirectionsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DirectionsFilled", "", 2, undefined));
exports.DirectionsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DirectionsRegular", "", 2, undefined));
exports.DishwasherFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DishwasherFilled", "", 2, undefined));
exports.DishwasherRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DishwasherRegular", "", 2, undefined));
exports.DismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DismissFilled", "", 2, undefined));
exports.DismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DismissRegular", "", 2, undefined));
exports.DismissCircleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DismissCircleFilled", "", 2, undefined));
exports.DismissCircleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DismissCircleRegular", "", 2, undefined));
exports.DismissSquareFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DismissSquareFilled", "", 2, undefined));
exports.DismissSquareRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DismissSquareRegular", "", 2, undefined));
exports.DismissSquareMultipleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DismissSquareMultipleFilled", "", 2, undefined));
exports.DismissSquareMultipleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DismissSquareMultipleRegular", "", 2, undefined));
exports.DiversityFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DiversityFilled", "", 2, undefined));
exports.DiversityRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DiversityRegular", "", 2, undefined));
exports.DividerShortFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DividerShortFilled", "", 2, undefined));
exports.DividerShortRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DividerShortRegular", "", 2, undefined));
exports.DividerTallFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DividerTallFilled", "", 2, undefined));
exports.DividerTallRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DividerTallRegular", "", 2, undefined));
exports.DockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DockFilled", "", 2, undefined));
exports.DockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DockRegular", "", 2, undefined));
exports.DockRowFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DockRowFilled", "", 2, undefined));
exports.DockRowRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DockRowRegular", "", 2, undefined));
exports.DoctorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DoctorFilled", "", 2, undefined));
exports.DoctorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DoctorRegular", "", 2, undefined));
exports.Document100Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Document100Filled", "", 2, undefined));
exports.Document100Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Document100Regular", "", 2, undefined));
exports.DocumentFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentFilled", "", 2, undefined));
exports.DocumentRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentRegular", "", 2, undefined));
exports.DocumentAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentAddFilled", "", 2, undefined));
exports.DocumentAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentAddRegular", "", 2, undefined));
exports.DocumentArrowDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentArrowDownFilled", "", 2, undefined));
exports.DocumentArrowDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentArrowDownRegular", "", 2, undefined));
exports.DocumentArrowLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentArrowLeftFilled", "", 2, undefined));
exports.DocumentArrowLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentArrowLeftRegular", "", 2, undefined));
exports.DocumentArrowRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentArrowRightFilled", "", 2, undefined));
exports.DocumentArrowRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentArrowRightRegular", "", 2, undefined));
exports.DocumentArrowUpFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentArrowUpFilled", "", 2, undefined));
exports.DocumentArrowUpRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentArrowUpRegular", "", 2, undefined));
exports.DocumentBorderFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentBorderFilled", "", 2, undefined));
exports.DocumentBorderRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentBorderRegular", "", 2, undefined));
exports.DocumentBorderPrintFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentBorderPrintFilled", "", 2, undefined));
exports.DocumentBorderPrintRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentBorderPrintRegular", "", 2, undefined));
exports.DocumentBriefcaseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentBriefcaseFilled", "", 2, undefined));
exports.DocumentBriefcaseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentBriefcaseRegular", "", 2, undefined));
exports.DocumentBulletListFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentBulletListFilled", "", 2, undefined));
exports.DocumentBulletListRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentBulletListRegular", "", 2, undefined));
exports.DocumentBulletListArrowLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentBulletListArrowLeftFilled", "", 2, undefined));
exports.DocumentBulletListArrowLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentBulletListArrowLeftRegular", "", 2, undefined));
exports.DocumentBulletListClockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentBulletListClockFilled", "", 2, undefined));
exports.DocumentBulletListClockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentBulletListClockRegular", "", 2, undefined));
exports.DocumentBulletListCubeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentBulletListCubeFilled", "", 2, undefined));
exports.DocumentBulletListCubeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentBulletListCubeRegular", "", 2, undefined));
exports.DocumentBulletListMultipleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentBulletListMultipleFilled", "", 2, undefined));
exports.DocumentBulletListMultipleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentBulletListMultipleRegular", "", 2, undefined));
exports.DocumentBulletListOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentBulletListOffFilled", "", 2, undefined));
exports.DocumentBulletListOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentBulletListOffRegular", "", 2, undefined));
exports.DocumentCatchUpFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentCatchUpFilled", "", 2, undefined));
exports.DocumentCatchUpRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentCatchUpRegular", "", 2, undefined));
exports.DocumentCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentCheckmarkFilled", "", 2, undefined));
exports.DocumentCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentCheckmarkRegular", "", 2, undefined));
exports.DocumentChevronDoubleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentChevronDoubleFilled", "", 2, undefined));
exports.DocumentChevronDoubleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentChevronDoubleRegular", "", 2, undefined));
exports.DocumentCopyFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentCopyFilled", "", 2, undefined));
exports.DocumentCopyRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentCopyRegular", "", 2, undefined));
exports.DocumentCssFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentCssFilled", "", 2, undefined));
exports.DocumentCssRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentCssRegular", "", 2, undefined));
exports.DocumentCubeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentCubeFilled", "", 2, undefined));
exports.DocumentCubeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentCubeRegular", "", 2, undefined));
exports.DocumentDataFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentDataFilled", "", 2, undefined));
exports.DocumentDataRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentDataRegular", "", 2, undefined));
exports.DocumentDataLinkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentDataLinkFilled", "", 2, undefined));
exports.DocumentDataLinkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentDataLinkRegular", "", 2, undefined));
exports.DocumentDataLockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentDataLockFilled", "", 2, undefined));
exports.DocumentDataLockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentDataLockRegular", "", 2, undefined));
exports.DocumentDatabaseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentDatabaseFilled", "", 2, undefined));
exports.DocumentDatabaseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentDatabaseRegular", "", 2, undefined));
exports.DocumentDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentDismissFilled", "", 2, undefined));
exports.DocumentDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentDismissRegular", "", 2, undefined));
exports.DocumentEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentEditFilled", "", 2, undefined));
exports.DocumentEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentEditRegular", "", 2, undefined));
exports.DocumentEndnoteFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentEndnoteFilled", "", 2, undefined));
exports.DocumentEndnoteRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentEndnoteRegular", "", 2, undefined));
exports.DocumentErrorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentErrorFilled", "", 2, undefined));
exports.DocumentErrorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentErrorRegular", "", 2, undefined));
exports.DocumentFitFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentFitFilled", "", 2, undefined));
exports.DocumentFitRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentFitRegular", "", 2, undefined));
exports.DocumentFlowchartFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentFlowchartFilled", "", 2, undefined));
exports.DocumentFlowchartRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentFlowchartRegular", "", 2, undefined));
exports.DocumentFolderFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentFolderFilled", "", 2, undefined));
exports.DocumentFolderRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentFolderRegular", "", 2, undefined));
exports.DocumentFooterFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentFooterFilled", "", 2, undefined));
exports.DocumentFooterRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentFooterRegular", "", 2, undefined));
exports.DocumentFooterDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentFooterDismissFilled", "", 2, undefined));
exports.DocumentFooterDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentFooterDismissRegular", "", 2, undefined));
exports.DocumentGlobeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentGlobeFilled", "", 2, undefined));
exports.DocumentGlobeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentGlobeRegular", "", 2, undefined));
exports.DocumentHeaderFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentHeaderFilled", "", 2, undefined));
exports.DocumentHeaderRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentHeaderRegular", "", 2, undefined));
exports.DocumentHeaderArrowDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentHeaderArrowDownFilled", "", 2, undefined));
exports.DocumentHeaderArrowDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentHeaderArrowDownRegular", "", 2, undefined));
exports.DocumentHeaderDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentHeaderDismissFilled", "", 2, undefined));
exports.DocumentHeaderDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentHeaderDismissRegular", "", 2, undefined));
exports.DocumentHeaderFooterFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentHeaderFooterFilled", "", 2, undefined));
exports.DocumentHeaderFooterRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentHeaderFooterRegular", "", 2, undefined));
exports.DocumentHeartFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentHeartFilled", "", 2, undefined));
exports.DocumentHeartRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentHeartRegular", "", 2, undefined));
exports.DocumentHeartPulseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentHeartPulseFilled", "", 2, undefined));
exports.DocumentHeartPulseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentHeartPulseRegular", "", 2, undefined));
exports.DocumentImageFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentImageFilled", "", 2, undefined));
exports.DocumentImageRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentImageRegular", "", 2, undefined));
exports.DocumentJavaFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentJavaFilled", "", 2, undefined));
exports.DocumentJavaRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentJavaRegular", "", 2, undefined));
exports.DocumentJavascriptFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentJavascriptFilled", "", 2, undefined));
exports.DocumentJavascriptRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentJavascriptRegular", "", 2, undefined));
exports.DocumentKeyFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentKeyFilled", "", 2, undefined));
exports.DocumentKeyRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentKeyRegular", "", 2, undefined));
exports.DocumentLandscapeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentLandscapeFilled", "", 2, undefined));
exports.DocumentLandscapeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentLandscapeRegular", "", 2, undefined));
exports.DocumentLandscapeDataFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentLandscapeDataFilled", "", 2, undefined));
exports.DocumentLandscapeDataRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentLandscapeDataRegular", "", 2, undefined));
exports.DocumentLandscapeSplitFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentLandscapeSplitFilled", "", 2, undefined));
exports.DocumentLandscapeSplitRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentLandscapeSplitRegular", "", 2, undefined));
exports.DocumentLandscapeSplitHintFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentLandscapeSplitHintFilled", "", 2, undefined));
exports.DocumentLandscapeSplitHintRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentLandscapeSplitHintRegular", "", 2, undefined));
exports.DocumentLightningFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentLightningFilled", "", 2, undefined));
exports.DocumentLightningRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentLightningRegular", "", 2, undefined));
exports.DocumentLinkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentLinkFilled", "", 2, undefined));
exports.DocumentLinkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentLinkRegular", "", 2, undefined));
exports.DocumentLockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentLockFilled", "", 2, undefined));
exports.DocumentLockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentLockRegular", "", 2, undefined));
exports.DocumentMarginsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentMarginsFilled", "", 2, undefined));
exports.DocumentMarginsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentMarginsRegular", "", 2, undefined));
exports.DocumentMentionFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentMentionFilled", "", 2, undefined));
exports.DocumentMentionRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentMentionRegular", "", 2, undefined));
exports.DocumentMultipleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentMultipleFilled", "", 2, undefined));
exports.DocumentMultipleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentMultipleRegular", "", 2, undefined));
exports.DocumentMultiplePercentFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentMultiplePercentFilled", "", 2, undefined));
exports.DocumentMultiplePercentRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentMultiplePercentRegular", "", 2, undefined));
exports.DocumentMultipleProhibitedFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentMultipleProhibitedFilled", "", 2, undefined));
exports.DocumentMultipleProhibitedRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentMultipleProhibitedRegular", "", 2, undefined));
exports.DocumentMultipleSyncFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentMultipleSyncFilled", "", 2, undefined));
exports.DocumentMultipleSyncRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentMultipleSyncRegular", "", 2, undefined));
exports.DocumentOnePageFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentOnePageFilled", "", 2, undefined));
exports.DocumentOnePageRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentOnePageRegular", "", 2, undefined));
exports.DocumentOnePageAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentOnePageAddFilled", "", 2, undefined));
exports.DocumentOnePageAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentOnePageAddRegular", "", 2, undefined));
exports.DocumentOnePageColumnsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentOnePageColumnsFilled", "", 2, undefined));
exports.DocumentOnePageColumnsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentOnePageColumnsRegular", "", 2, undefined));
exports.DocumentOnePageLinkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentOnePageLinkFilled", "", 2, undefined));
exports.DocumentOnePageLinkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentOnePageLinkRegular", "", 2, undefined));
exports.DocumentOnePageMultipleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentOnePageMultipleFilled", "", 2, undefined));
exports.DocumentOnePageMultipleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentOnePageMultipleRegular", "", 2, undefined));
exports.DocumentOnePageMultipleSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentOnePageMultipleSparkleFilled", "", 2, undefined));
exports.DocumentOnePageMultipleSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentOnePageMultipleSparkleRegular", "", 2, undefined));
exports.DocumentOnePageSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentOnePageSparkleFilled", "", 2, undefined));
exports.DocumentOnePageSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentOnePageSparkleRegular", "", 2, undefined));
exports.DocumentPageBottomCenterFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPageBottomCenterFilled", "", 2, undefined));
exports.DocumentPageBottomCenterRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPageBottomCenterRegular", "", 2, undefined));
exports.DocumentPageBottomLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPageBottomLeftFilled", "", 2, undefined));
exports.DocumentPageBottomLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPageBottomLeftRegular", "", 2, undefined));
exports.DocumentPageBottomRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPageBottomRightFilled", "", 2, undefined));
exports.DocumentPageBottomRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPageBottomRightRegular", "", 2, undefined));
exports.DocumentPageBreakFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPageBreakFilled", "", 2, undefined));
exports.DocumentPageBreakRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPageBreakRegular", "", 2, undefined));
exports.DocumentPageNumberFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPageNumberFilled", "", 2, undefined));
exports.DocumentPageNumberRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPageNumberRegular", "", 2, undefined));
exports.DocumentPageTopCenterFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPageTopCenterFilled", "", 2, undefined));
exports.DocumentPageTopCenterRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPageTopCenterRegular", "", 2, undefined));
exports.DocumentPageTopLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPageTopLeftFilled", "", 2, undefined));
exports.DocumentPageTopLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPageTopLeftRegular", "", 2, undefined));
exports.DocumentPageTopRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPageTopRightFilled", "", 2, undefined));
exports.DocumentPageTopRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPageTopRightRegular", "", 2, undefined));
exports.DocumentPdfFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPdfFilled", "", 2, undefined));
exports.DocumentPdfRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPdfRegular", "", 2, undefined));
exports.DocumentPercentFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPercentFilled", "", 2, undefined));
exports.DocumentPercentRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPercentRegular", "", 2, undefined));
exports.DocumentPersonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPersonFilled", "", 2, undefined));
exports.DocumentPersonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPersonRegular", "", 2, undefined));
exports.DocumentPillFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPillFilled", "", 2, undefined));
exports.DocumentPillRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPillRegular", "", 2, undefined));
exports.DocumentPrintFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPrintFilled", "", 2, undefined));
exports.DocumentPrintRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentPrintRegular", "", 2, undefined));
exports.DocumentProhibitedFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentProhibitedFilled", "", 2, undefined));
exports.DocumentProhibitedRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentProhibitedRegular", "", 2, undefined));
exports.DocumentQuestionMarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentQuestionMarkFilled", "", 2, undefined));
exports.DocumentQuestionMarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentQuestionMarkRegular", "", 2, undefined));
exports.DocumentQueueFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentQueueFilled", "", 2, undefined));
exports.DocumentQueueRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentQueueRegular", "", 2, undefined));
exports.DocumentQueueAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentQueueAddFilled", "", 2, undefined));
exports.DocumentQueueAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentQueueAddRegular", "", 2, undefined));
exports.DocumentQueueMultipleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentQueueMultipleFilled", "", 2, undefined));
exports.DocumentQueueMultipleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentQueueMultipleRegular", "", 2, undefined));
exports.DocumentRibbonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentRibbonFilled", "", 2, undefined));
exports.DocumentRibbonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentRibbonRegular", "", 2, undefined));
exports.DocumentSassFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentSassFilled", "", 2, undefined));
exports.DocumentSassRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentSassRegular", "", 2, undefined));
exports.DocumentSaveFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentSaveFilled", "", 2, undefined));
exports.DocumentSaveRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentSaveRegular", "", 2, undefined));
exports.DocumentSearchFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentSearchFilled", "", 2, undefined));
exports.DocumentSearchRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentSearchRegular", "", 2, undefined));
exports.DocumentSettingsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentSettingsFilled", "", 2, undefined));
exports.DocumentSettingsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentSettingsRegular", "", 2, undefined));
exports.DocumentSignatureFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentSignatureFilled", "", 2, undefined));
exports.DocumentSignatureRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentSignatureRegular", "", 2, undefined));
exports.DocumentSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentSparkleFilled", "", 2, undefined));
exports.DocumentSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentSparkleRegular", "", 2, undefined));
exports.DocumentSplitHintFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentSplitHintFilled", "", 2, undefined));
exports.DocumentSplitHintRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentSplitHintRegular", "", 2, undefined));
exports.DocumentSplitHintOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentSplitHintOffFilled", "", 2, undefined));
exports.DocumentSplitHintOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentSplitHintOffRegular", "", 2, undefined));
exports.DocumentSquareFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentSquareFilled", "", 2, undefined));
exports.DocumentSquareRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentSquareRegular", "", 2, undefined));
exports.DocumentSyncFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentSyncFilled", "", 2, undefined));
exports.DocumentSyncRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentSyncRegular", "", 2, undefined));
exports.DocumentTableFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTableFilled", "", 2, undefined));
exports.DocumentTableRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTableRegular", "", 2, undefined));
exports.DocumentTableArrowRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTableArrowRightFilled", "", 2, undefined));
exports.DocumentTableArrowRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTableArrowRightRegular", "", 2, undefined));
exports.DocumentTableCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTableCheckmarkFilled", "", 2, undefined));
exports.DocumentTableCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTableCheckmarkRegular", "", 2, undefined));
exports.DocumentTableCubeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTableCubeFilled", "", 2, undefined));
exports.DocumentTableCubeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTableCubeRegular", "", 2, undefined));
exports.DocumentTableSearchFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTableSearchFilled", "", 2, undefined));
exports.DocumentTableSearchRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTableSearchRegular", "", 2, undefined));
exports.DocumentTableTruckFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTableTruckFilled", "", 2, undefined));
exports.DocumentTableTruckRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTableTruckRegular", "", 2, undefined));
exports.DocumentTargetFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTargetFilled", "", 2, undefined));
exports.DocumentTargetRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTargetRegular", "", 2, undefined));
exports.DocumentTextFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTextFilled", "", 2, undefined));
exports.DocumentTextRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTextRegular", "", 2, undefined));
exports.DocumentTextClockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTextClockFilled", "", 2, undefined));
exports.DocumentTextClockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTextClockRegular", "", 2, undefined));
exports.DocumentTextExtractFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTextExtractFilled", "", 2, undefined));
exports.DocumentTextExtractRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTextExtractRegular", "", 2, undefined));
exports.DocumentTextLinkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTextLinkFilled", "", 2, undefined));
exports.DocumentTextLinkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTextLinkRegular", "", 2, undefined));
exports.DocumentTextToolboxFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTextToolboxFilled", "", 2, undefined));
exports.DocumentTextToolboxRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentTextToolboxRegular", "", 2, undefined));
exports.DocumentToolboxFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentToolboxFilled", "", 2, undefined));
exports.DocumentToolboxRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentToolboxRegular", "", 2, undefined));
exports.DocumentWidthFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentWidthFilled", "", 2, undefined));
exports.DocumentWidthRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentWidthRegular", "", 2, undefined));
exports.DocumentYmlFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentYmlFilled", "", 2, undefined));
exports.DocumentYmlRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DocumentYmlRegular", "", 2, undefined));
exports.DoorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DoorFilled", "", 2, undefined));
exports.DoorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DoorRegular", "", 2, undefined));
exports.DoorArrowLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DoorArrowLeftFilled", "", 2, undefined));
exports.DoorArrowLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DoorArrowLeftRegular", "", 2, undefined));
exports.DoorArrowRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DoorArrowRightFilled", "", 2, undefined));
exports.DoorArrowRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DoorArrowRightRegular", "", 2, undefined));
exports.DoorTagFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DoorTagFilled", "", 2, undefined));
exports.DoorTagRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DoorTagRegular", "", 2, undefined));
exports.DoubleSwipeDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DoubleSwipeDownFilled", "", 2, undefined));
exports.DoubleSwipeDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DoubleSwipeDownRegular", "", 2, undefined));
exports.DoubleSwipeUpFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DoubleSwipeUpFilled", "", 2, undefined));
exports.DoubleSwipeUpRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DoubleSwipeUpRegular", "", 2, undefined));
exports.DoubleTapSwipeDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DoubleTapSwipeDownFilled", "", 2, undefined));
exports.DoubleTapSwipeDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DoubleTapSwipeDownRegular", "", 2, undefined));
exports.DoubleTapSwipeUpFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DoubleTapSwipeUpFilled", "", 2, undefined));
exports.DoubleTapSwipeUpRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DoubleTapSwipeUpRegular", "", 2, undefined));
exports.DraftsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DraftsFilled", "", 2, undefined));
exports.DraftsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DraftsRegular", "", 2, undefined));
exports.DragFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DragFilled", "", 2, undefined));
exports.DragRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DragRegular", "", 2, undefined));
exports.DrawImageFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrawImageFilled", "", 2, undefined));
exports.DrawImageRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrawImageRegular", "", 2, undefined));
exports.DrawShapeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrawShapeFilled", "", 2, undefined));
exports.DrawShapeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrawShapeRegular", "", 2, undefined));
exports.DrawTextFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrawTextFilled", "", 2, undefined));
exports.DrawTextRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrawTextRegular", "", 2, undefined));
exports.DrawerFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrawerFilled", "", 2, undefined));
exports.DrawerRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrawerRegular", "", 2, undefined));
exports.DrawerAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrawerAddFilled", "", 2, undefined));
exports.DrawerAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrawerAddRegular", "", 2, undefined));
exports.DrawerArrowDownloadFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrawerArrowDownloadFilled", "", 2, undefined));
exports.DrawerArrowDownloadRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrawerArrowDownloadRegular", "", 2, undefined));
exports.DrawerDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrawerDismissFilled", "", 2, undefined));
exports.DrawerDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrawerDismissRegular", "", 2, undefined));
exports.DrawerPlayFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrawerPlayFilled", "", 2, undefined));
exports.DrawerPlayRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrawerPlayRegular", "", 2, undefined));
exports.DrawerSubtractFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrawerSubtractFilled", "", 2, undefined));
exports.DrawerSubtractRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrawerSubtractRegular", "", 2, undefined));
exports.DrinkBeerFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrinkBeerFilled", "", 2, undefined));
exports.DrinkBeerRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrinkBeerRegular", "", 2, undefined));
exports.DrinkBottleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrinkBottleFilled", "", 2, undefined));
exports.DrinkBottleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrinkBottleRegular", "", 2, undefined));
exports.DrinkBottleOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrinkBottleOffFilled", "", 2, undefined));
exports.DrinkBottleOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrinkBottleOffRegular", "", 2, undefined));
exports.DrinkCoffeeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrinkCoffeeFilled", "", 2, undefined));
exports.DrinkCoffeeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrinkCoffeeRegular", "", 2, undefined));
exports.DrinkMargaritaFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrinkMargaritaFilled", "", 2, undefined));
exports.DrinkMargaritaRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrinkMargaritaRegular", "", 2, undefined));
exports.DrinkToGoFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrinkToGoFilled", "", 2, undefined));
exports.DrinkToGoRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrinkToGoRegular", "", 2, undefined));
exports.DrinkWineFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrinkWineFilled", "", 2, undefined));
exports.DrinkWineRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DrinkWineRegular", "", 2, undefined));
exports.DriveTrainFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DriveTrainFilled", "", 2, undefined));
exports.DriveTrainRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DriveTrainRegular", "", 2, undefined));
exports.DropFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DropFilled", "", 2, undefined));
exports.DropRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DropRegular", "", 2, undefined));
exports.DualScreenFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenFilled", "", 2, undefined));
exports.DualScreenRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenRegular", "", 2, undefined));
exports.DualScreenAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenAddFilled", "", 2, undefined));
exports.DualScreenAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenAddRegular", "", 2, undefined));
exports.DualScreenArrowRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenArrowRightFilled", "", 2, undefined));
exports.DualScreenArrowRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenArrowRightRegular", "", 2, undefined));
exports.DualScreenArrowUpFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenArrowUpFilled", "", 2, undefined));
exports.DualScreenArrowUpRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenArrowUpRegular", "", 2, undefined));
exports.DualScreenClockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenClockFilled", "", 2, undefined));
exports.DualScreenClockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenClockRegular", "", 2, undefined));
exports.DualScreenClosedAlertFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenClosedAlertFilled", "", 2, undefined));
exports.DualScreenClosedAlertRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenClosedAlertRegular", "", 2, undefined));
exports.DualScreenDesktopFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenDesktopFilled", "", 2, undefined));
exports.DualScreenDesktopRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenDesktopRegular", "", 2, undefined));
exports.DualScreenDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenDismissFilled", "", 2, undefined));
exports.DualScreenDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenDismissRegular", "", 2, undefined));
exports.DualScreenGroupFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenGroupFilled", "", 2, undefined));
exports.DualScreenGroupRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenGroupRegular", "", 2, undefined));
exports.DualScreenHeaderFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenHeaderFilled", "", 2, undefined));
exports.DualScreenHeaderRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenHeaderRegular", "", 2, undefined));
exports.DualScreenLockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenLockFilled", "", 2, undefined));
exports.DualScreenLockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenLockRegular", "", 2, undefined));
exports.DualScreenMirrorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenMirrorFilled", "", 2, undefined));
exports.DualScreenMirrorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenMirrorRegular", "", 2, undefined));
exports.DualScreenPaginationFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenPaginationFilled", "", 2, undefined));
exports.DualScreenPaginationRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenPaginationRegular", "", 2, undefined));
exports.DualScreenSettingsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenSettingsFilled", "", 2, undefined));
exports.DualScreenSettingsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenSettingsRegular", "", 2, undefined));
exports.DualScreenSpanFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenSpanFilled", "", 2, undefined));
exports.DualScreenSpanRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenSpanRegular", "", 2, undefined));
exports.DualScreenSpeakerFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenSpeakerFilled", "", 2, undefined));
exports.DualScreenSpeakerRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenSpeakerRegular", "", 2, undefined));
exports.DualScreenStatusBarFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenStatusBarFilled", "", 2, undefined));
exports.DualScreenStatusBarRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenStatusBarRegular", "", 2, undefined));
exports.DualScreenTabletFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenTabletFilled", "", 2, undefined));
exports.DualScreenTabletRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenTabletRegular", "", 2, undefined));
exports.DualScreenUpdateFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenUpdateFilled", "", 2, undefined));
exports.DualScreenUpdateRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenUpdateRegular", "", 2, undefined));
exports.DualScreenVerticalScrollFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenVerticalScrollFilled", "", 2, undefined));
exports.DualScreenVerticalScrollRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenVerticalScrollRegular", "", 2, undefined));
exports.DualScreenVibrateFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenVibrateFilled", "", 2, undefined));
exports.DualScreenVibrateRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DualScreenVibrateRegular", "", 2, undefined));
exports.DumbbellFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DumbbellFilled", "", 2, undefined));
exports.DumbbellRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DumbbellRegular", "", 2, undefined));
