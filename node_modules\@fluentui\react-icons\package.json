{"name": "@fluentui/react-icons", "version": "2.0.309", "sideEffects": false, "main": "lib-cjs/index.js", "module": "lib/index.js", "typings": "lib/index.d.ts", "description": "Fluent System Icons are a collection of familiar, friendly, and modern icons from Microsoft.", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/microsoft/fluentui-system-icons.git"}, "scripts": {"clean": "git clean -fXd lib/ lib-cjs/ intermediate/ src/ tmp/", "generate:assets-to-svg": "node ../../importer/generate.js --source=../../assets --dest=./intermediate --extension=svg --target=react", "convert:svg": "node convert.js --source=./intermediate --dest=./src --rtl=./intermediate/rtl.json --metadata=./tmp/metadata-svg.json", "convert:fonts": "node convert-font.js --source=./src/utils/fonts --dest=./src/fonts --codepointDest=./src/utils/fonts --rtl=./intermediate/rtl.json --metadata=./tmp/metadata-font.json", "convert:merge-metadata": "node merge-metadata.js --svgMetadata=./tmp/metadata-svg.json --fontMetadata=./tmp/metadata-font.json --output=./metadata.json", "generate:font-regular": "node ../../importer/generateFont.js --source=intermediate --dest=src/utils/fonts --iconType=Regular --codepoints=../../fonts/FluentSystemIcons-Regular.json", "generate:font-filled": "node ../../importer/generateFont.js --source=intermediate --dest=src/utils/fonts --iconType=Filled --codepoints=../../fonts/FluentSystemIcons-Filled.json", "generate:font-light": "node ../../importer/generateFont.js --source=intermediate --dest=src/utils/fonts --iconType=Light", "generate:font-resizable": "node ../../importer/generateFont.js --source=intermediate --dest=src/utils/fonts --iconType=Resizable", "generate:font": "concurrently 'npm run generate:font-regular' 'npm run generate:font-filled' 'npm run generate:font-light' 'npm run generate:font-resizable'", "generate:rtl": "node ../../importer/rtlMetadata.js --source=../../assets --dest=./intermediate/rtl.json", "optimize": "svgo --config svgo.config.js --folder=./intermediate --precision=2 --quiet", "unfill": "node unfill.js --source=./intermediate", "build:fonts-and-svg": "npm run generate:assets-to-svg && npm run generate:font && npm run generate:rtl && npm run optimize && npm run unfill", "build:generate-js-chunks": "npm run convert:svg && npm run convert:fonts && npm run convert:merge-metadata", "build:js": "node build.js", "prebuild": "npm run clean", "build": "npm run build:fonts-and-svg && npm run build:generate-js-chunks && npm run build:js", "build:verify": "vitest run build-verify.test.js", "lint": "eslint src", "test": "vitest --exclude build-verify.test.js"}, "devDependencies": {"@babel/core": "^7.16.0", "@griffel/babel-preset": "^1.0.0", "@griffel/eslint-plugin": "^2.0.0", "@types/react": "^17.0.2", "concurrently": "^9.2.0", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^5.2.0", "glob": "^7.2.0", "lodash": "^4.17.21", "mkdirp": "^1.0.4", "react": "^17.0.1", "renamer": "^2.0.1", "svgo": "^2.8.0", "typescript": "~4.1.0", "typescript-eslint": "^7.18.0", "vitest": "^3.2.4", "yargs": "^14.0.0"}, "dependencies": {"@griffel/react": "^1.0.0", "tslib": "^2.1.0"}, "peerDependencies": {"react": ">=16.8.0 <19.0.0"}, "files": ["lib/", "lib-cjs/"], "exports": {".": {"fluentIconFont": {"types": "./lib/fonts/index.d.ts", "import": "./lib/fonts/index.js", "require": "./lib-cjs/fonts/index.js"}, "default": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib-cjs/index.js"}}, "./fonts": {"types": "./lib/fonts/index.d.ts", "import": "./lib/fonts/index.js", "require": "./lib-cjs/fonts/index.js"}, "./lib/fonts": {"types": "./lib/fonts/index.d.ts", "import": "./lib/fonts/index.js", "require": "./lib-cjs/fonts/index.js"}, "./svg": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib-cjs/index.js"}, "./lib/svg": {"types": "./lib/index.d.ts", "import": "./lib/index.js", "require": "./lib-cjs/index.js"}, "./providers": {"types": "./lib/providers.d.ts", "import": "./lib/providers.js", "require": "./lib-cjs/providers.js"}, "./lib/providers": {"types": "./lib/providers.d.ts", "import": "./lib/providers.js", "require": "./lib-cjs/providers.js"}}}