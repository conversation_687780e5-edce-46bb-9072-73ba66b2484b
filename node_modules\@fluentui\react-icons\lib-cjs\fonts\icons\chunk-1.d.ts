import type { FluentFontIcon } from "../../utils/fonts/createFluentFontIcon";
export declare const BeakerSettingsFilled: FluentFontIcon;
export declare const BeakerSettingsRegular: FluentFontIcon;
export declare const BedFilled: FluentFontIcon;
export declare const BedRegular: FluentFontIcon;
export declare const BenchFilled: FluentFontIcon;
export declare const BenchRegular: FluentFontIcon;
export declare const BezierCurveSquareFilled: FluentFontIcon;
export declare const BezierCurveSquareRegular: FluentFontIcon;
export declare const BinFullFilled: FluentFontIcon;
export declare const BinFullRegular: FluentFontIcon;
export declare const BinRecycleFilled: FluentFontIcon;
export declare const BinRecycleRegular: FluentFontIcon;
export declare const BinRecycleFullFilled: FluentFontIcon;
export declare const BinRecycleFullRegular: FluentFontIcon;
export declare const BinderTriangleFilled: FluentFontIcon;
export declare const BinderTriangleRegular: FluentFontIcon;
export declare const BluetoothFilled: FluentFontIcon;
export declare const BluetoothRegular: FluentFontIcon;
export declare const BluetoothConnectedFilled: FluentFontIcon;
export declare const BluetoothConnectedRegular: FluentFontIcon;
export declare const BluetoothDisabledFilled: FluentFontIcon;
export declare const BluetoothDisabledRegular: FluentFontIcon;
export declare const BluetoothSearchingFilled: FluentFontIcon;
export declare const BluetoothSearchingRegular: FluentFontIcon;
export declare const BlurFilled: FluentFontIcon;
export declare const BlurRegular: FluentFontIcon;
export declare const BoardFilled: FluentFontIcon;
export declare const BoardRegular: FluentFontIcon;
export declare const BoardGamesFilled: FluentFontIcon;
export declare const BoardGamesRegular: FluentFontIcon;
export declare const BoardHeartFilled: FluentFontIcon;
export declare const BoardHeartRegular: FluentFontIcon;
export declare const BoardSplitFilled: FluentFontIcon;
export declare const BoardSplitRegular: FluentFontIcon;
export declare const BookFilled: FluentFontIcon;
export declare const BookRegular: FluentFontIcon;
export declare const BookAddFilled: FluentFontIcon;
export declare const BookAddRegular: FluentFontIcon;
export declare const BookArrowClockwiseFilled: FluentFontIcon;
export declare const BookArrowClockwiseRegular: FluentFontIcon;
export declare const BookClockFilled: FluentFontIcon;
export declare const BookClockRegular: FluentFontIcon;
export declare const BookCoinsFilled: FluentFontIcon;
export declare const BookCoinsRegular: FluentFontIcon;
export declare const BookCompassFilled: FluentFontIcon;
export declare const BookCompassRegular: FluentFontIcon;
export declare const BookContactsFilled: FluentFontIcon;
export declare const BookContactsRegular: FluentFontIcon;
export declare const BookDatabaseFilled: FluentFontIcon;
export declare const BookDatabaseRegular: FluentFontIcon;
export declare const BookDefaultFilled: FluentFontIcon;
export declare const BookDismissFilled: FluentFontIcon;
export declare const BookDismissRegular: FluentFontIcon;
export declare const BookExclamationMarkFilled: FluentFontIcon;
export declare const BookExclamationMarkRegular: FluentFontIcon;
export declare const BookGlobeFilled: FluentFontIcon;
export declare const BookGlobeRegular: FluentFontIcon;
export declare const BookInformationFilled: FluentFontIcon;
export declare const BookInformationRegular: FluentFontIcon;
export declare const BookLetterFilled: FluentFontIcon;
export declare const BookLetterRegular: FluentFontIcon;
export declare const BookNumberFilled: FluentFontIcon;
export declare const BookNumberRegular: FluentFontIcon;
export declare const BookOpenFilled: FluentFontIcon;
export declare const BookOpenRegular: FluentFontIcon;
export declare const BookOpenGlobeFilled: FluentFontIcon;
export declare const BookOpenGlobeRegular: FluentFontIcon;
export declare const BookOpenLightbulbFilled: FluentFontIcon;
export declare const BookOpenLightbulbRegular: FluentFontIcon;
export declare const BookOpenMicrophoneFilled: FluentFontIcon;
export declare const BookOpenMicrophoneRegular: FluentFontIcon;
export declare const BookPulseFilled: FluentFontIcon;
export declare const BookPulseRegular: FluentFontIcon;
export declare const BookQuestionMarkFilled: FluentFontIcon;
export declare const BookQuestionMarkRegular: FluentFontIcon;
export declare const BookQuestionMarkRtlFilled: FluentFontIcon;
export declare const BookQuestionMarkRtlRegular: FluentFontIcon;
export declare const BookSearchFilled: FluentFontIcon;
export declare const BookSearchRegular: FluentFontIcon;
export declare const BookStarFilled: FluentFontIcon;
export declare const BookStarRegular: FluentFontIcon;
export declare const BookTemplateFilled: FluentFontIcon;
export declare const BookTemplateRegular: FluentFontIcon;
export declare const BookThetaFilled: FluentFontIcon;
export declare const BookThetaRegular: FluentFontIcon;
export declare const BookToolboxFilled: FluentFontIcon;
export declare const BookToolboxRegular: FluentFontIcon;
export declare const BookmarkFilled: FluentFontIcon;
export declare const BookmarkRegular: FluentFontIcon;
export declare const BookmarkAddFilled: FluentFontIcon;
export declare const BookmarkAddRegular: FluentFontIcon;
export declare const BookmarkMultipleFilled: FluentFontIcon;
export declare const BookmarkMultipleRegular: FluentFontIcon;
export declare const BookmarkOffFilled: FluentFontIcon;
export declare const BookmarkOffRegular: FluentFontIcon;
export declare const BookmarkSearchFilled: FluentFontIcon;
export declare const BookmarkSearchRegular: FluentFontIcon;
export declare const BorderAllFilled: FluentFontIcon;
export declare const BorderAllRegular: FluentFontIcon;
export declare const BorderBottomFilled: FluentFontIcon;
export declare const BorderBottomRegular: FluentFontIcon;
export declare const BorderBottomDoubleFilled: FluentFontIcon;
export declare const BorderBottomDoubleRegular: FluentFontIcon;
export declare const BorderBottomThickFilled: FluentFontIcon;
export declare const BorderBottomThickRegular: FluentFontIcon;
export declare const BorderInsideFilled: FluentFontIcon;
export declare const BorderInsideRegular: FluentFontIcon;
export declare const BorderLeftFilled: FluentFontIcon;
export declare const BorderLeftRegular: FluentFontIcon;
export declare const BorderLeftRightFilled: FluentFontIcon;
export declare const BorderLeftRightRegular: FluentFontIcon;
export declare const BorderNoneFilled: FluentFontIcon;
export declare const BorderNoneRegular: FluentFontIcon;
export declare const BorderOutsideFilled: FluentFontIcon;
export declare const BorderOutsideRegular: FluentFontIcon;
export declare const BorderOutsideThickFilled: FluentFontIcon;
export declare const BorderOutsideThickRegular: FluentFontIcon;
export declare const BorderRightFilled: FluentFontIcon;
export declare const BorderRightRegular: FluentFontIcon;
export declare const BorderTopFilled: FluentFontIcon;
export declare const BorderTopRegular: FluentFontIcon;
export declare const BorderTopBottomFilled: FluentFontIcon;
export declare const BorderTopBottomRegular: FluentFontIcon;
export declare const BorderTopBottomDoubleFilled: FluentFontIcon;
export declare const BorderTopBottomDoubleRegular: FluentFontIcon;
export declare const BorderTopBottomThickFilled: FluentFontIcon;
export declare const BorderTopBottomThickRegular: FluentFontIcon;
export declare const BotFilled: FluentFontIcon;
export declare const BotRegular: FluentFontIcon;
export declare const BotAddFilled: FluentFontIcon;
export declare const BotAddRegular: FluentFontIcon;
export declare const BotSparkleFilled: FluentFontIcon;
export declare const BotSparkleRegular: FluentFontIcon;
export declare const BowTieFilled: FluentFontIcon;
export declare const BowTieRegular: FluentFontIcon;
export declare const BowlChopsticksFilled: FluentFontIcon;
export declare const BowlChopsticksRegular: FluentFontIcon;
export declare const BowlSaladFilled: FluentFontIcon;
export declare const BowlSaladRegular: FluentFontIcon;
export declare const BoxFilled: FluentFontIcon;
export declare const BoxRegular: FluentFontIcon;
export declare const BoxArrowLeftFilled: FluentFontIcon;
export declare const BoxArrowLeftRegular: FluentFontIcon;
export declare const BoxArrowUpFilled: FluentFontIcon;
export declare const BoxArrowUpRegular: FluentFontIcon;
export declare const BoxCheckmarkFilled: FluentFontIcon;
export declare const BoxCheckmarkRegular: FluentFontIcon;
export declare const BoxDismissFilled: FluentFontIcon;
export declare const BoxDismissRegular: FluentFontIcon;
export declare const BoxEditFilled: FluentFontIcon;
export declare const BoxEditRegular: FluentFontIcon;
export declare const BoxMultipleFilled: FluentFontIcon;
export declare const BoxMultipleRegular: FluentFontIcon;
export declare const BoxMultipleArrowLeftFilled: FluentFontIcon;
export declare const BoxMultipleArrowLeftRegular: FluentFontIcon;
export declare const BoxMultipleArrowRightFilled: FluentFontIcon;
export declare const BoxMultipleArrowRightRegular: FluentFontIcon;
export declare const BoxMultipleCheckmarkFilled: FluentFontIcon;
export declare const BoxMultipleCheckmarkRegular: FluentFontIcon;
export declare const BoxMultipleSearchFilled: FluentFontIcon;
export declare const BoxMultipleSearchRegular: FluentFontIcon;
export declare const BoxSearchFilled: FluentFontIcon;
export declare const BoxSearchRegular: FluentFontIcon;
export declare const BoxToolboxFilled: FluentFontIcon;
export declare const BoxToolboxRegular: FluentFontIcon;
export declare const BracesFilled: FluentFontIcon;
export declare const BracesRegular: FluentFontIcon;
export declare const BracesVariableFilled: FluentFontIcon;
export declare const BracesVariableRegular: FluentFontIcon;
export declare const BrainFilled: FluentFontIcon;
export declare const BrainRegular: FluentFontIcon;
export declare const BrainCircuitFilled: FluentFontIcon;
export declare const BrainCircuitRegular: FluentFontIcon;
export declare const BrainSparkleFilled: FluentFontIcon;
export declare const BrainSparkleRegular: FluentFontIcon;
export declare const BranchFilled: FluentFontIcon;
export declare const BranchRegular: FluentFontIcon;
export declare const BranchCompareFilled: FluentFontIcon;
export declare const BranchCompareRegular: FluentFontIcon;
export declare const BranchForkFilled: FluentFontIcon;
export declare const BranchForkRegular: FluentFontIcon;
export declare const BranchForkHintFilled: FluentFontIcon;
export declare const BranchForkHintRegular: FluentFontIcon;
export declare const BranchForkLinkFilled: FluentFontIcon;
export declare const BranchForkLinkRegular: FluentFontIcon;
export declare const BranchRequestFilled: FluentFontIcon;
export declare const BranchRequestRegular: FluentFontIcon;
export declare const BranchRequestClosedFilled: FluentFontIcon;
export declare const BranchRequestClosedRegular: FluentFontIcon;
export declare const BranchRequestDraftFilled: FluentFontIcon;
export declare const BranchRequestDraftRegular: FluentFontIcon;
export declare const BreakoutRoomFilled: FluentFontIcon;
export declare const BreakoutRoomRegular: FluentFontIcon;
export declare const BriefcaseFilled: FluentFontIcon;
export declare const BriefcaseRegular: FluentFontIcon;
export declare const BriefcaseMedicalFilled: FluentFontIcon;
export declare const BriefcaseMedicalRegular: FluentFontIcon;
export declare const BriefcaseOffFilled: FluentFontIcon;
export declare const BriefcaseOffRegular: FluentFontIcon;
export declare const BriefcaseSearchFilled: FluentFontIcon;
export declare const BriefcaseSearchRegular: FluentFontIcon;
export declare const BrightnessHighFilled: FluentFontIcon;
export declare const BrightnessHighRegular: FluentFontIcon;
export declare const BrightnessLowFilled: FluentFontIcon;
export declare const BrightnessLowRegular: FluentFontIcon;
export declare const BroadActivityFeedFilled: FluentFontIcon;
export declare const BroadActivityFeedRegular: FluentFontIcon;
export declare const BroomFilled: FluentFontIcon;
export declare const BroomRegular: FluentFontIcon;
export declare const BroomSparkleFilled: FluentFontIcon;
export declare const BroomSparkleRegular: FluentFontIcon;
export declare const BubbleMultipleFilled: FluentFontIcon;
export declare const BubbleMultipleRegular: FluentFontIcon;
export declare const BugFilled: FluentFontIcon;
export declare const BugRegular: FluentFontIcon;
export declare const BugArrowCounterclockwiseFilled: FluentFontIcon;
export declare const BugArrowCounterclockwiseRegular: FluentFontIcon;
export declare const BugProhibitedFilled: FluentFontIcon;
export declare const BugProhibitedRegular: FluentFontIcon;
export declare const BuildingFilled: FluentFontIcon;
export declare const BuildingRegular: FluentFontIcon;
export declare const BuildingBankFilled: FluentFontIcon;
export declare const BuildingBankRegular: FluentFontIcon;
export declare const BuildingBankLinkFilled: FluentFontIcon;
export declare const BuildingBankLinkRegular: FluentFontIcon;
export declare const BuildingBankToolboxFilled: FluentFontIcon;
export declare const BuildingBankToolboxRegular: FluentFontIcon;
export declare const BuildingCheckmarkFilled: FluentFontIcon;
export declare const BuildingCheckmarkRegular: FluentFontIcon;
export declare const BuildingDesktopFilled: FluentFontIcon;
export declare const BuildingDesktopRegular: FluentFontIcon;
export declare const BuildingFactoryFilled: FluentFontIcon;
export declare const BuildingFactoryRegular: FluentFontIcon;
export declare const BuildingGovernmentFilled: FluentFontIcon;
export declare const BuildingGovernmentRegular: FluentFontIcon;
export declare const BuildingGovernmentSearchFilled: FluentFontIcon;
export declare const BuildingGovernmentSearchRegular: FluentFontIcon;
export declare const BuildingHomeFilled: FluentFontIcon;
export declare const BuildingHomeRegular: FluentFontIcon;
export declare const BuildingLighthouseFilled: FluentFontIcon;
export declare const BuildingLighthouseRegular: FluentFontIcon;
export declare const BuildingMosqueFilled: FluentFontIcon;
export declare const BuildingMosqueRegular: FluentFontIcon;
export declare const BuildingMultipleFilled: FluentFontIcon;
export declare const BuildingMultipleRegular: FluentFontIcon;
export declare const BuildingPeopleFilled: FluentFontIcon;
export declare const BuildingPeopleRegular: FluentFontIcon;
export declare const BuildingRetailFilled: FluentFontIcon;
export declare const BuildingRetailRegular: FluentFontIcon;
export declare const BuildingRetailMoneyFilled: FluentFontIcon;
export declare const BuildingRetailMoneyRegular: FluentFontIcon;
export declare const BuildingRetailMoreFilled: FluentFontIcon;
export declare const BuildingRetailMoreRegular: FluentFontIcon;
export declare const BuildingRetailShieldFilled: FluentFontIcon;
export declare const BuildingRetailShieldRegular: FluentFontIcon;
export declare const BuildingRetailToolboxFilled: FluentFontIcon;
export declare const BuildingRetailToolboxRegular: FluentFontIcon;
export declare const BuildingShopFilled: FluentFontIcon;
export declare const BuildingShopRegular: FluentFontIcon;
export declare const BuildingSkyscraperFilled: FluentFontIcon;
export declare const BuildingSkyscraperRegular: FluentFontIcon;
export declare const BuildingSwapFilled: FluentFontIcon;
export declare const BuildingSwapRegular: FluentFontIcon;
export declare const BuildingTownhouseFilled: FluentFontIcon;
export declare const BuildingTownhouseRegular: FluentFontIcon;
export declare const ButtonFilled: FluentFontIcon;
export declare const ButtonRegular: FluentFontIcon;
export declare const CalculatorFilled: FluentFontIcon;
export declare const CalculatorRegular: FluentFontIcon;
export declare const CalculatorArrowClockwiseFilled: FluentFontIcon;
export declare const CalculatorArrowClockwiseRegular: FluentFontIcon;
export declare const CalculatorMultipleFilled: FluentFontIcon;
export declare const CalculatorMultipleRegular: FluentFontIcon;
export declare const CalendarFilled: FluentFontIcon;
export declare const CalendarRegular: FluentFontIcon;
export declare const Calendar3DayFilled: FluentFontIcon;
export declare const Calendar3DayRegular: FluentFontIcon;
export declare const CalendarAddFilled: FluentFontIcon;
export declare const CalendarAddRegular: FluentFontIcon;
export declare const CalendarAgendaFilled: FluentFontIcon;
export declare const CalendarAgendaRegular: FluentFontIcon;
export declare const CalendarArrowCounterclockwiseFilled: FluentFontIcon;
export declare const CalendarArrowCounterclockwiseRegular: FluentFontIcon;
export declare const CalendarArrowDownFilled: FluentFontIcon;
export declare const CalendarArrowDownRegular: FluentFontIcon;
export declare const CalendarArrowRepeatAllFilled: FluentFontIcon;
export declare const CalendarArrowRepeatAllRegular: FluentFontIcon;
export declare const CalendarArrowRightFilled: FluentFontIcon;
export declare const CalendarArrowRightRegular: FluentFontIcon;
export declare const CalendarAssistantFilled: FluentFontIcon;
export declare const CalendarAssistantRegular: FluentFontIcon;
export declare const CalendarCancelFilled: FluentFontIcon;
export declare const CalendarCancelRegular: FluentFontIcon;
export declare const CalendarChatFilled: FluentFontIcon;
export declare const CalendarChatRegular: FluentFontIcon;
export declare const CalendarCheckmarkFilled: FluentFontIcon;
export declare const CalendarCheckmarkRegular: FluentFontIcon;
export declare const CalendarCheckmarkCenterFilled: FluentFontIcon;
export declare const CalendarCheckmarkCenterRegular: FluentFontIcon;
export declare const CalendarCheckmarkSparkleFilled: FluentFontIcon;
export declare const CalendarCheckmarkSparkleRegular: FluentFontIcon;
export declare const CalendarClockFilled: FluentFontIcon;
export declare const CalendarClockRegular: FluentFontIcon;
export declare const CalendarDataBarFilled: FluentFontIcon;
export declare const CalendarDataBarRegular: FluentFontIcon;
export declare const CalendarDateFilled: FluentFontIcon;
export declare const CalendarDateRegular: FluentFontIcon;
export declare const CalendarDayFilled: FluentFontIcon;
export declare const CalendarDayRegular: FluentFontIcon;
export declare const CalendarEditFilled: FluentFontIcon;
export declare const CalendarEditRegular: FluentFontIcon;
export declare const CalendarEmptyFilled: FluentFontIcon;
export declare const CalendarEmptyRegular: FluentFontIcon;
export declare const CalendarErrorFilled: FluentFontIcon;
export declare const CalendarErrorRegular: FluentFontIcon;
export declare const CalendarEyeFilled: FluentFontIcon;
export declare const CalendarEyeRegular: FluentFontIcon;
export declare const CalendarInfoFilled: FluentFontIcon;
export declare const CalendarInfoRegular: FluentFontIcon;
export declare const CalendarLockFilled: FluentFontIcon;
export declare const CalendarLockRegular: FluentFontIcon;
export declare const CalendarLtrFilled: FluentFontIcon;
export declare const CalendarLtrRegular: FluentFontIcon;
export declare const CalendarMailFilled: FluentFontIcon;
export declare const CalendarMailRegular: FluentFontIcon;
export declare const CalendarMentionFilled: FluentFontIcon;
export declare const CalendarMentionRegular: FluentFontIcon;
export declare const CalendarMonthFilled: FluentFontIcon;
export declare const CalendarMonthRegular: FluentFontIcon;
export declare const CalendarMultipleFilled: FluentFontIcon;
export declare const CalendarMultipleRegular: FluentFontIcon;
export declare const CalendarNoteFilled: FluentFontIcon;
export declare const CalendarNoteRegular: FluentFontIcon;
export declare const CalendarPatternFilled: FluentFontIcon;
export declare const CalendarPatternRegular: FluentFontIcon;
export declare const CalendarPersonFilled: FluentFontIcon;
export declare const CalendarPersonRegular: FluentFontIcon;
export declare const CalendarPhoneFilled: FluentFontIcon;
export declare const CalendarPhoneRegular: FluentFontIcon;
export declare const CalendarPlayFilled: FluentFontIcon;
export declare const CalendarPlayRegular: FluentFontIcon;
export declare const CalendarQuestionMarkFilled: FluentFontIcon;
export declare const CalendarQuestionMarkRegular: FluentFontIcon;
export declare const CalendarRecordFilled: FluentFontIcon;
export declare const CalendarRecordRegular: FluentFontIcon;
export declare const CalendarReplyFilled: FluentFontIcon;
export declare const CalendarReplyRegular: FluentFontIcon;
export declare const CalendarRtlFilled: FluentFontIcon;
export declare const CalendarRtlRegular: FluentFontIcon;
export declare const CalendarSearchFilled: FluentFontIcon;
export declare const CalendarSearchRegular: FluentFontIcon;
export declare const CalendarSettingsFilled: FluentFontIcon;
export declare const CalendarSettingsRegular: FluentFontIcon;
export declare const CalendarShieldFilled: FluentFontIcon;
export declare const CalendarShieldRegular: FluentFontIcon;
export declare const CalendarSparkleFilled: FluentFontIcon;
export declare const CalendarSparkleRegular: FluentFontIcon;
export declare const CalendarStarFilled: FluentFontIcon;
export declare const CalendarStarRegular: FluentFontIcon;
export declare const CalendarSyncFilled: FluentFontIcon;
export declare const CalendarSyncRegular: FluentFontIcon;
export declare const CalendarTemplateFilled: FluentFontIcon;
export declare const CalendarTemplateRegular: FluentFontIcon;
export declare const CalendarTodayFilled: FluentFontIcon;
export declare const CalendarTodayRegular: FluentFontIcon;
export declare const CalendarToolboxFilled: FluentFontIcon;
export declare const CalendarToolboxRegular: FluentFontIcon;
export declare const CalendarVideoFilled: FluentFontIcon;
export declare const CalendarVideoRegular: FluentFontIcon;
export declare const CalendarWeekNumbersFilled: FluentFontIcon;
export declare const CalendarWeekNumbersRegular: FluentFontIcon;
export declare const CalendarWeekStartFilled: FluentFontIcon;
export declare const CalendarWeekStartRegular: FluentFontIcon;
export declare const CalendarWorkWeekFilled: FluentFontIcon;
export declare const CalendarWorkWeekRegular: FluentFontIcon;
export declare const CallFilled: FluentFontIcon;
export declare const CallRegular: FluentFontIcon;
export declare const CallAddFilled: FluentFontIcon;
export declare const CallAddRegular: FluentFontIcon;
export declare const CallCheckmarkFilled: FluentFontIcon;
export declare const CallCheckmarkRegular: FluentFontIcon;
export declare const CallConnectingFilled: FluentFontIcon;
export declare const CallConnectingRegular: FluentFontIcon;
export declare const CallDismissFilled: FluentFontIcon;
export declare const CallDismissRegular: FluentFontIcon;
export declare const CallEndFilled: FluentFontIcon;
export declare const CallEndRegular: FluentFontIcon;
export declare const CallExclamationFilled: FluentFontIcon;
export declare const CallExclamationRegular: FluentFontIcon;
export declare const CallForwardFilled: FluentFontIcon;
export declare const CallForwardRegular: FluentFontIcon;
export declare const CallInboundFilled: FluentFontIcon;
export declare const CallInboundRegular: FluentFontIcon;
export declare const CallMissedFilled: FluentFontIcon;
export declare const CallMissedRegular: FluentFontIcon;
export declare const CallOutboundFilled: FluentFontIcon;
export declare const CallOutboundRegular: FluentFontIcon;
export declare const CallParkFilled: FluentFontIcon;
export declare const CallParkRegular: FluentFontIcon;
export declare const CallPauseFilled: FluentFontIcon;
export declare const CallPauseRegular: FluentFontIcon;
export declare const CallProhibitedFilled: FluentFontIcon;
export declare const CallProhibitedRegular: FluentFontIcon;
export declare const CallRectangleLandscapeFilled: FluentFontIcon;
export declare const CallRectangleLandscapeRegular: FluentFontIcon;
export declare const CallSquareFilled: FluentFontIcon;
export declare const CallSquareRegular: FluentFontIcon;
export declare const CallTransferFilled: FluentFontIcon;
export declare const CallTransferRegular: FluentFontIcon;
export declare const CallWarningFilled: FluentFontIcon;
export declare const CallWarningRegular: FluentFontIcon;
export declare const CalligraphyPenFilled: FluentFontIcon;
export declare const CalligraphyPenRegular: FluentFontIcon;
export declare const CalligraphyPenCheckmarkFilled: FluentFontIcon;
export declare const CalligraphyPenCheckmarkRegular: FluentFontIcon;
export declare const CalligraphyPenErrorFilled: FluentFontIcon;
export declare const CalligraphyPenErrorRegular: FluentFontIcon;
export declare const CalligraphyPenQuestionMarkFilled: FluentFontIcon;
export declare const CalligraphyPenQuestionMarkRegular: FluentFontIcon;
export declare const CameraFilled: FluentFontIcon;
export declare const CameraRegular: FluentFontIcon;
export declare const CameraAddFilled: FluentFontIcon;
export declare const CameraAddRegular: FluentFontIcon;
export declare const CameraArrowUpFilled: FluentFontIcon;
export declare const CameraArrowUpRegular: FluentFontIcon;
export declare const CameraDomeFilled: FluentFontIcon;
export declare const CameraDomeRegular: FluentFontIcon;
export declare const CameraEditFilled: FluentFontIcon;
export declare const CameraEditRegular: FluentFontIcon;
export declare const CameraOffFilled: FluentFontIcon;
export declare const CameraOffRegular: FluentFontIcon;
export declare const CameraSparklesFilled: FluentFontIcon;
export declare const CameraSparklesRegular: FluentFontIcon;
export declare const CameraSwitchFilled: FluentFontIcon;
export declare const CameraSwitchRegular: FluentFontIcon;
export declare const CardUiFilled: FluentFontIcon;
export declare const CardUiRegular: FluentFontIcon;
export declare const CardUiPortraitFlipFilled: FluentFontIcon;
export declare const CardUiPortraitFlipRegular: FluentFontIcon;
export declare const CaretDownFilled: FluentFontIcon;
export declare const CaretDownRegular: FluentFontIcon;
export declare const CaretDownRightFilled: FluentFontIcon;
export declare const CaretDownRightRegular: FluentFontIcon;
export declare const CaretLeftFilled: FluentFontIcon;
export declare const CaretLeftRegular: FluentFontIcon;
export declare const CaretRightFilled: FluentFontIcon;
export declare const CaretRightRegular: FluentFontIcon;
export declare const CaretUpFilled: FluentFontIcon;
export declare const CaretUpRegular: FluentFontIcon;
export declare const CartFilled: FluentFontIcon;
export declare const CartRegular: FluentFontIcon;
export declare const CastFilled: FluentFontIcon;
export declare const CastRegular: FluentFontIcon;
export declare const CastMultipleFilled: FluentFontIcon;
export declare const CastMultipleRegular: FluentFontIcon;
export declare const CatchUpFilled: FluentFontIcon;
export declare const CatchUpRegular: FluentFontIcon;
export declare const Cellular3GFilled: FluentFontIcon;
export declare const Cellular3GRegular: FluentFontIcon;
export declare const Cellular4GFilled: FluentFontIcon;
export declare const Cellular4GRegular: FluentFontIcon;
export declare const Cellular5GFilled: FluentFontIcon;
export declare const Cellular5GRegular: FluentFontIcon;
export declare const CellularData1Filled: FluentFontIcon;
export declare const CellularData1Regular: FluentFontIcon;
export declare const CellularData2Filled: FluentFontIcon;
export declare const CellularData2Regular: FluentFontIcon;
export declare const CellularData3Filled: FluentFontIcon;
export declare const CellularData3Regular: FluentFontIcon;
export declare const CellularData4Filled: FluentFontIcon;
export declare const CellularData4Regular: FluentFontIcon;
export declare const CellularData5Filled: FluentFontIcon;
export declare const CellularData5Regular: FluentFontIcon;
export declare const CellularOffFilled: FluentFontIcon;
export declare const CellularOffRegular: FluentFontIcon;
export declare const CellularWarningFilled: FluentFontIcon;
export declare const CellularWarningRegular: FluentFontIcon;
export declare const CenterHorizontalFilled: FluentFontIcon;
export declare const CenterHorizontalRegular: FluentFontIcon;
export declare const CenterVerticalFilled: FluentFontIcon;
export declare const CenterVerticalRegular: FluentFontIcon;
export declare const CertificateFilled: FluentFontIcon;
export declare const CertificateRegular: FluentFontIcon;
export declare const ChannelFilled: FluentFontIcon;
export declare const ChannelRegular: FluentFontIcon;
export declare const ChannelAddFilled: FluentFontIcon;
export declare const ChannelAddRegular: FluentFontIcon;
export declare const ChannelAlertFilled: FluentFontIcon;
export declare const ChannelAlertRegular: FluentFontIcon;
export declare const ChannelArrowLeftFilled: FluentFontIcon;
export declare const ChannelArrowLeftRegular: FluentFontIcon;
export declare const ChannelDismissFilled: FluentFontIcon;
export declare const ChannelDismissRegular: FluentFontIcon;
export declare const ChannelShareFilled: FluentFontIcon;
export declare const ChannelShareRegular: FluentFontIcon;
export declare const ChannelSubtractFilled: FluentFontIcon;
export declare const ChannelSubtractRegular: FluentFontIcon;
export declare const ChartMultipleFilled: FluentFontIcon;
export declare const ChartMultipleRegular: FluentFontIcon;
export declare const ChartPersonFilled: FluentFontIcon;
