"use client";
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.EmojiSmileSlightRegular = exports.EmojiSmileSlightFilled = exports.EmojiSadSlightRegular = exports.EmojiSadSlightFilled = exports.EmojiSadRegular = exports.EmojiSadFilled = exports.EmojiMultipleRegular = exports.EmojiMultipleFilled = exports.EmojiMemeRegular = exports.EmojiMemeFilled = exports.EmojiMehRegular = exports.EmojiMehFilled = exports.EmojiLaughRegular = exports.EmojiLaughFilled = exports.EmojiHintRegular = exports.EmojiHintFilled = exports.EmojiHandRegular = exports.EmojiHandFilled = exports.EmojiEditRegular = exports.EmojiEditFilled = exports.EmojiAngryRegular = exports.EmojiAngryFilled = exports.EmojiAddRegular = exports.EmojiAddFilled = exports.EmojiRegular = exports.EmojiFilled = exports.ElevatorRegular = exports.ElevatorFilled = exports.EditSettingsRegular = exports.EditSettingsFilled = exports.EditProhibitedRegular = exports.EditProhibitedFilled = exports.EditPersonRegular = exports.EditPersonFilled = exports.EditOffRegular = exports.EditOffFilled = exports.EditLockRegular = exports.EditLockFilled = exports.EditLineHorizontal3Regular = exports.EditLineHorizontal3Filled = exports.EditArrowBackRegular = exports.EditArrowBackFilled = exports.EditRegular = exports.EditFilled = exports.EarthLeafRegular = exports.EarthLeafFilled = exports.EarthRegular = exports.EarthFilled = exports.DustRegular = exports.DustFilled = void 0;
exports.FastForwardRegular = exports.FastForwardFilled = exports.FastAccelerationRegular = exports.FastAccelerationFilled = exports.FStopRegular = exports.FStopFilled = exports.EyedropperOffRegular = exports.EyedropperOffFilled = exports.EyedropperRegular = exports.EyedropperFilled = exports.EyeTrackingOffRegular = exports.EyeTrackingOffFilled = exports.EyeTrackingRegular = exports.EyeTrackingFilled = exports.EyeOffRegular = exports.EyeOffFilled = exports.EyeLinesRegular = exports.EyeLinesFilled = exports.EyeRegular = exports.EyeFilled = exports.ExtendedDockRegular = exports.ExtendedDockFilled = exports.ExpandUpRightRegular = exports.ExpandUpRightFilled = exports.ExpandUpLeftRegular = exports.ExpandUpLeftFilled = exports.ErrorCircleSettingsRegular = exports.ErrorCircleSettingsFilled = exports.ErrorCircleRegular = exports.ErrorCircleFilled = exports.EraserToolRegular = exports.EraserToolFilled = exports.EraserSmallRegular = exports.EraserSmallFilled = exports.EraserSegmentRegular = exports.EraserSegmentFilled = exports.EraserMediumRegular = exports.EraserMediumFilled = exports.EraserRegular = exports.EraserFilled = exports.EqualOffRegular = exports.EqualOffFilled = exports.EqualCircleRegular = exports.EqualCircleFilled = exports.EngineRegular = exports.EngineFilled = exports.EmojiSurpriseRegular = exports.EmojiSurpriseFilled = exports.EmojiSparkleRegular = exports.EmojiSparkleFilled = void 0;
exports.FlashFlowRegular = exports.FlashFlowFilled = exports.FlashCheckmarkRegular = exports.FlashCheckmarkFilled = exports.FlashAutoRegular = exports.FlashAutoFilled = exports.FlashAddRegular = exports.FlashAddFilled = exports.FlashRegular = exports.FlashFilled = exports.FlagPrideProgressFilled = exports.FlagPridePhiladelphiaFilled = exports.FlagPrideIntersexInclusiveProgressFilled = exports.FlagPrideFilled = exports.FlagOffRegular = exports.FlagOffFilled = exports.FlagClockRegular = exports.FlagClockFilled = exports.FlagCheckeredRegular = exports.FlagCheckeredFilled = exports.FlagRegular = exports.FlagFilled = exports.FixedWidthRegular = exports.FixedWidthFilled = exports.FireplaceRegular = exports.FireplaceFilled = exports.FireRegular = exports.FireFilled = exports.FingerprintRegular = exports.FingerprintFilled = exports.FilterSyncRegular = exports.FilterSyncFilled = exports.FilterDismissRegular = exports.FilterDismissFilled = exports.FilterAddRegular = exports.FilterAddFilled = exports.FilterRegular = exports.FilterFilled = exports.FilmstripSplitRegular = exports.FilmstripSplitFilled = exports.FilmstripPlayRegular = exports.FilmstripPlayFilled = exports.FilmstripImageRegular = exports.FilmstripImageFilled = exports.FilmstripRegular = exports.FilmstripFilled = exports.FeedRegular = exports.FeedFilled = exports.FaxRegular = exports.FaxFilled = void 0;
exports.FolderLinkRegular = exports.FolderLinkFilled = exports.FolderLightningRegular = exports.FolderLightningFilled = exports.FolderGlobeRegular = exports.FolderGlobeFilled = exports.FolderDocumentRegular = exports.FolderDocumentFilled = exports.FolderBriefcaseRegular = exports.FolderBriefcaseFilled = exports.FolderArrowUpRegular = exports.FolderArrowUpFilled = exports.FolderArrowRightRegular = exports.FolderArrowRightFilled = exports.FolderArrowLeftRegular = exports.FolderArrowLeftFilled = exports.FolderAddRegular = exports.FolderAddFilled = exports.FolderRegular = exports.FolderFilled = exports.FluidRegular = exports.FluidFilled = exports.FluentRegular = exports.FluentFilled = exports.FlowchartCircleRegular = exports.FlowchartCircleFilled = exports.FlowchartRegular = exports.FlowchartFilled = exports.FlowSparkleRegular = exports.FlowSparkleFilled = exports.FlowDotRegular = exports.FlowDotFilled = exports.FlowRegular = exports.FlowFilled = exports.FlipVerticalRegular = exports.FlipVerticalFilled = exports.FlipHorizontalRegular = exports.FlipHorizontalFilled = exports.FlashlightOffRegular = exports.FlashlightOffFilled = exports.FlashlightRegular = exports.FlashlightFilled = exports.FlashSparkleRegular = exports.FlashSparkleFilled = exports.FlashSettingsRegular = exports.FlashSettingsFilled = exports.FlashPlayRegular = exports.FlashPlayFilled = exports.FlashOffRegular = exports.FlashOffFilled = void 0;
exports.FoodPizzaRegular = exports.FoodPizzaFilled = exports.FoodGrainsRegular = exports.FoodGrainsFilled = exports.FoodFishRegular = exports.FoodFishFilled = exports.FoodEggRegular = exports.FoodEggFilled = exports.FoodChickenLegRegular = exports.FoodChickenLegFilled = exports.FoodCarrotRegular = exports.FoodCarrotFilled = exports.FoodCakeRegular = exports.FoodCakeFilled = exports.FoodAppleRegular = exports.FoodAppleFilled = exports.FoodRegular = exports.FoodFilled = exports.FontSpaceTrackingOutRegular = exports.FontSpaceTrackingOutFilled = exports.FontSpaceTrackingInRegular = exports.FontSpaceTrackingInFilled = exports.FontIncreaseRegular = exports.FontIncreaseFilled = exports.FontDecreaseRegular = exports.FontDecreaseFilled = exports.FolderZipRegular = exports.FolderZipFilled = exports.FolderSyncRegular = exports.FolderSyncFilled = exports.FolderSwapRegular = exports.FolderSwapFilled = exports.FolderSearchRegular = exports.FolderSearchFilled = exports.FolderProhibitedRegular = exports.FolderProhibitedFilled = exports.FolderPersonRegular = exports.FolderPersonFilled = exports.FolderPeopleRegular = exports.FolderPeopleFilled = exports.FolderOpenVerticalRegular = exports.FolderOpenVerticalFilled = exports.FolderOpenDownRegular = exports.FolderOpenDownFilled = exports.FolderOpenRegular = exports.FolderOpenFilled = exports.FolderMailRegular = exports.FolderMailFilled = exports.FolderListRegular = exports.FolderListFilled = void 0;
exports.GestureRegular = exports.GestureFilled = exports.GavelProhibitedRegular = exports.GavelProhibitedFilled = exports.GavelRegular = exports.GavelFilled = exports.GaugeAddRegular = exports.GaugeAddFilled = exports.GaugeRegular = exports.GaugeFilled = exports.GatherRegular = exports.GatherFilled = exports.GasPumpRegular = exports.GasPumpFilled = exports.GasRegular = exports.GasFilled = exports.GanttChartRegular = exports.GanttChartFilled = exports.GamesRegular = exports.GamesFilled = exports.GameChatRegular = exports.GameChatFilled = exports.FullScreenMinimizeRegular = exports.FullScreenMinimizeFilled = exports.FullScreenMaximizeRegular = exports.FullScreenMaximizeFilled = exports.FrameRegular = exports.FrameFilled = exports.Fps960Regular = exports.Fps960Filled = exports.Fps60Regular = exports.Fps60Filled = exports.Fps30Regular = exports.Fps30Filled = exports.Fps240Regular = exports.Fps240Filled = exports.Fps120Regular = exports.Fps120Filled = exports.FormSparkleRegular = exports.FormSparkleFilled = exports.FormNewRegular = exports.FormNewFilled = exports.FormMultipleCollectionRegular = exports.FormMultipleCollectionFilled = exports.FormMultipleRegular = exports.FormMultipleFilled = exports.FormRegular = exports.FormFilled = exports.FoodToastRegular = exports.FoodToastFilled = void 0;
exports.GlobeSearchRegular = exports.GlobeSearchFilled = exports.GlobeProhibitedRegular = exports.GlobeProhibitedFilled = exports.GlobePersonRegular = exports.GlobePersonFilled = exports.GlobeOffRegular = exports.GlobeOffFilled = exports.GlobeLocationRegular = exports.GlobeLocationFilled = exports.GlobeErrorRegular = exports.GlobeErrorFilled = exports.GlobeDesktopRegular = exports.GlobeDesktopFilled = exports.GlobeClockRegular = exports.GlobeClockFilled = exports.GlobeArrowUpRegular = exports.GlobeArrowUpFilled = exports.GlobeArrowForwardRegular = exports.GlobeArrowForwardFilled = exports.GlobeAddRegular = exports.GlobeAddFilled = exports.GlobeRegular = exports.GlobeFilled = exports.GlassesOffRegular = exports.GlassesOffFilled = exports.GlassesRegular = exports.GlassesFilled = exports.GlanceHorizontalSparklesRegular = exports.GlanceHorizontalSparklesFilled = exports.GlanceHorizontalRegular = exports.GlanceHorizontalFilled = exports.GlanceRegular = exports.GlanceFilled = exports.GiftOpenRegular = exports.GiftOpenFilled = exports.GiftCardMultipleRegular = exports.GiftCardMultipleFilled = exports.GiftCardMoneyRegular = exports.GiftCardMoneyFilled = exports.GiftCardArrowRightRegular = exports.GiftCardArrowRightFilled = exports.GiftCardAddRegular = exports.GiftCardAddFilled = exports.GiftCardRegular = exports.GiftCardFilled = exports.GiftRegular = exports.GiftFilled = exports.GifRegular = exports.GifFilled = void 0;
exports.HandRightOffRegular = exports.HandRightOffFilled = exports.HandRightRegular = exports.HandRightFilled = exports.HandPointRegular = exports.HandPointFilled = exports.HandOpenHeartRegular = exports.HandOpenHeartFilled = exports.HandMultipleRegular = exports.HandMultipleFilled = exports.HandLeftChatRegular = exports.HandLeftChatFilled = exports.HandLeftRegular = exports.HandLeftFilled = exports.HandDrawRegular = exports.HandDrawFilled = exports.GuitarRegular = exports.GuitarFilled = exports.GuestAddRegular = exports.GuestAddFilled = exports.GuestRegular = exports.GuestFilled = exports.GuardianRegular = exports.GuardianFilled = exports.GroupReturnRegular = exports.GroupReturnFilled = exports.GroupListRegular = exports.GroupListFilled = exports.GroupDismissRegular = exports.GroupDismissFilled = exports.GroupRegular = exports.GroupFilled = exports.GridKanbanRegular = exports.GridKanbanFilled = exports.GridDotsRegular = exports.GridDotsFilled = exports.GridRegular = exports.GridFilled = exports.GlobeWarningRegular = exports.GlobeWarningFilled = exports.GlobeVideoRegular = exports.GlobeVideoFilled = exports.GlobeSyncRegular = exports.GlobeSyncFilled = exports.GlobeSurfaceRegular = exports.GlobeSurfaceFilled = exports.GlobeStarRegular = exports.GlobeStarFilled = exports.GlobeShieldRegular = exports.GlobeShieldFilled = void 0;
exports.HeartPulseErrorRegular = exports.HeartPulseErrorFilled = exports.HeartPulseCheckmarkRegular = exports.HeartPulseCheckmarkFilled = exports.HeartPulseRegular = exports.HeartPulseFilled = exports.HeartOffRegular = exports.HeartOffFilled = exports.HeartCircleHintRegular = exports.HeartCircleHintFilled = exports.HeartCircleRegular = exports.HeartCircleFilled = exports.HeartBrokenRegular = exports.HeartBrokenFilled = exports.HeartRegular = exports.HeartFilled = exports.HeadsetVrRegular = exports.HeadsetVrFilled = exports.HeadsetAddRegular = exports.HeadsetAddFilled = exports.HeadsetRegular = exports.HeadsetFilled = exports.HeadphonesSoundWaveRegular = exports.HeadphonesSoundWaveFilled = exports.HeadphonesRegular = exports.HeadphonesFilled = exports.HdrOffRegular = exports.HdrOffFilled = exports.HdrRegular = exports.HdrFilled = exports.HdOffRegular = exports.HdOffFilled = exports.HdRegular = exports.HdFilled = exports.HatGraduationSparkleRegular = exports.HatGraduationSparkleFilled = exports.HatGraduationAddRegular = exports.HatGraduationAddFilled = exports.HatGraduationRegular = exports.HatGraduationFilled = exports.HardDriveRegular = exports.HardDriveFilled = exports.HapticWeakRegular = exports.HapticWeakFilled = exports.HapticStrongRegular = exports.HapticStrongFilled = exports.HandshakeRegular = exports.HandshakeFilled = exports.HandWaveRegular = exports.HandWaveFilled = void 0;
exports.ImageFilled = exports.IconsRegular = exports.IconsFilled = exports.HourglassThreeQuarterRegular = exports.HourglassThreeQuarterFilled = exports.HourglassOneQuarterRegular = exports.HourglassOneQuarterFilled = exports.HourglassHalfRegular = exports.HourglassHalfFilled = exports.HourglassRegular = exports.HourglassFilled = exports.HomeSplitRegular = exports.HomeSplitFilled = exports.HomePersonRegular = exports.HomePersonFilled = exports.HomeMoreRegular = exports.HomeMoreFilled = exports.HomeHeartRegular = exports.HomeHeartFilled = exports.HomeGarageRegular = exports.HomeGarageFilled = exports.HomeEmptyRegular = exports.HomeEmptyFilled = exports.HomeDatabaseRegular = exports.HomeDatabaseFilled = exports.HomeCheckmarkRegular = exports.HomeCheckmarkFilled = exports.HomeAddRegular = exports.HomeAddFilled = exports.HomeRegular = exports.HomeFilled = exports.HistoryDismissRegular = exports.HistoryDismissFilled = exports.HistoryRegular = exports.HistoryFilled = exports.HighwayRegular = exports.HighwayFilled = exports.HighlightLinkRegular = exports.HighlightLinkFilled = exports.HighlightAccentFilled = exports.HighlightRegular = exports.HighlightFilled = exports.HexagonThreeRegular = exports.HexagonThreeFilled = exports.HexagonSparkleRegular = exports.HexagonSparkleFilled = exports.HexagonRegular = exports.HexagonFilled = exports.HeartPulseWarningRegular = exports.HeartPulseWarningFilled = void 0;
const createFluentFontIcon_1 = require("../../utils/fonts/createFluentFontIcon");
exports.DustFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DustFilled", "", 2, undefined));
exports.DustRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DustRegular", "", 2, undefined));
exports.EarthFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EarthFilled", "", 2, undefined));
exports.EarthRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EarthRegular", "", 2, undefined));
exports.EarthLeafFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EarthLeafFilled", "", 2, undefined));
exports.EarthLeafRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EarthLeafRegular", "", 2, undefined));
exports.EditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EditFilled", "", 2, undefined));
exports.EditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EditRegular", "", 2, undefined));
exports.EditArrowBackFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EditArrowBackFilled", "", 2, undefined));
exports.EditArrowBackRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EditArrowBackRegular", "", 2, undefined));
exports.EditLineHorizontal3Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EditLineHorizontal3Filled", "", 2, undefined));
exports.EditLineHorizontal3Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EditLineHorizontal3Regular", "", 2, undefined));
exports.EditLockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EditLockFilled", "", 2, undefined));
exports.EditLockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EditLockRegular", "", 2, undefined));
exports.EditOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EditOffFilled", "", 2, undefined));
exports.EditOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EditOffRegular", "", 2, undefined));
exports.EditPersonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EditPersonFilled", "", 2, undefined));
exports.EditPersonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EditPersonRegular", "", 2, undefined));
exports.EditProhibitedFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EditProhibitedFilled", "", 2, undefined));
exports.EditProhibitedRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EditProhibitedRegular", "", 2, undefined));
exports.EditSettingsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EditSettingsFilled", "", 2, undefined));
exports.EditSettingsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EditSettingsRegular", "", 2, undefined));
exports.ElevatorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ElevatorFilled", "", 2, undefined));
exports.ElevatorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ElevatorRegular", "", 2, undefined));
exports.EmojiFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiFilled", "", 2, undefined));
exports.EmojiRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiRegular", "", 2, undefined));
exports.EmojiAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiAddFilled", "", 2, undefined));
exports.EmojiAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiAddRegular", "", 2, undefined));
exports.EmojiAngryFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiAngryFilled", "", 2, undefined));
exports.EmojiAngryRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiAngryRegular", "", 2, undefined));
exports.EmojiEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiEditFilled", "", 2, undefined));
exports.EmojiEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiEditRegular", "", 2, undefined));
exports.EmojiHandFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiHandFilled", "", 2, undefined));
exports.EmojiHandRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiHandRegular", "", 2, undefined));
exports.EmojiHintFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiHintFilled", "", 2, undefined));
exports.EmojiHintRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiHintRegular", "", 2, undefined));
exports.EmojiLaughFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiLaughFilled", "", 2, undefined));
exports.EmojiLaughRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiLaughRegular", "", 2, undefined));
exports.EmojiMehFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiMehFilled", "", 2, undefined));
exports.EmojiMehRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiMehRegular", "", 2, undefined));
exports.EmojiMemeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiMemeFilled", "", 2, undefined));
exports.EmojiMemeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiMemeRegular", "", 2, undefined));
exports.EmojiMultipleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiMultipleFilled", "", 2, undefined));
exports.EmojiMultipleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiMultipleRegular", "", 2, undefined));
exports.EmojiSadFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiSadFilled", "", 2, undefined));
exports.EmojiSadRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiSadRegular", "", 2, undefined));
exports.EmojiSadSlightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiSadSlightFilled", "", 2, undefined));
exports.EmojiSadSlightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiSadSlightRegular", "", 2, undefined));
exports.EmojiSmileSlightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiSmileSlightFilled", "", 2, undefined));
exports.EmojiSmileSlightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiSmileSlightRegular", "", 2, undefined));
exports.EmojiSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiSparkleFilled", "", 2, undefined));
exports.EmojiSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiSparkleRegular", "", 2, undefined));
exports.EmojiSurpriseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiSurpriseFilled", "", 2, undefined));
exports.EmojiSurpriseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EmojiSurpriseRegular", "", 2, undefined));
exports.EngineFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EngineFilled", "", 2, undefined));
exports.EngineRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EngineRegular", "", 2, undefined));
exports.EqualCircleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EqualCircleFilled", "", 2, undefined));
exports.EqualCircleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EqualCircleRegular", "", 2, undefined));
exports.EqualOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EqualOffFilled", "", 2, undefined));
exports.EqualOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EqualOffRegular", "", 2, undefined));
exports.EraserFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EraserFilled", "", 2, undefined));
exports.EraserRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EraserRegular", "", 2, undefined));
exports.EraserMediumFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EraserMediumFilled", "", 2, undefined));
exports.EraserMediumRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EraserMediumRegular", "", 2, undefined));
exports.EraserSegmentFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EraserSegmentFilled", "", 2, undefined));
exports.EraserSegmentRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EraserSegmentRegular", "", 2, undefined));
exports.EraserSmallFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EraserSmallFilled", "", 2, undefined));
exports.EraserSmallRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EraserSmallRegular", "", 2, undefined));
exports.EraserToolFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EraserToolFilled", "", 2, undefined));
exports.EraserToolRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EraserToolRegular", "", 2, undefined));
exports.ErrorCircleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ErrorCircleFilled", "", 2, undefined));
exports.ErrorCircleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ErrorCircleRegular", "", 2, undefined));
exports.ErrorCircleSettingsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ErrorCircleSettingsFilled", "", 2, undefined));
exports.ErrorCircleSettingsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ErrorCircleSettingsRegular", "", 2, undefined));
exports.ExpandUpLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ExpandUpLeftFilled", "", 2, undefined));
exports.ExpandUpLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ExpandUpLeftRegular", "", 2, undefined));
exports.ExpandUpRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ExpandUpRightFilled", "", 2, undefined));
exports.ExpandUpRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ExpandUpRightRegular", "", 2, undefined));
exports.ExtendedDockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ExtendedDockFilled", "", 2, undefined));
exports.ExtendedDockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ExtendedDockRegular", "", 2, undefined));
exports.EyeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EyeFilled", "", 2, undefined));
exports.EyeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EyeRegular", "", 2, undefined));
exports.EyeLinesFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EyeLinesFilled", "", 2, undefined));
exports.EyeLinesRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EyeLinesRegular", "", 2, undefined));
exports.EyeOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EyeOffFilled", "", 2, undefined));
exports.EyeOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EyeOffRegular", "", 2, undefined));
exports.EyeTrackingFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EyeTrackingFilled", "", 2, undefined));
exports.EyeTrackingRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EyeTrackingRegular", "", 2, undefined));
exports.EyeTrackingOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EyeTrackingOffFilled", "", 2, undefined));
exports.EyeTrackingOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EyeTrackingOffRegular", "", 2, undefined));
exports.EyedropperFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EyedropperFilled", "", 2, undefined));
exports.EyedropperRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EyedropperRegular", "", 2, undefined));
exports.EyedropperOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EyedropperOffFilled", "", 2, undefined));
exports.EyedropperOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("EyedropperOffRegular", "", 2, undefined));
exports.FStopFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FStopFilled", "", 2, undefined));
exports.FStopRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FStopRegular", "", 2, undefined));
exports.FastAccelerationFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FastAccelerationFilled", "", 2, undefined));
exports.FastAccelerationRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FastAccelerationRegular", "", 2, undefined));
exports.FastForwardFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FastForwardFilled", "", 2, undefined));
exports.FastForwardRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FastForwardRegular", "", 2, undefined));
exports.FaxFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FaxFilled", "", 2, undefined));
exports.FaxRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FaxRegular", "", 2, undefined));
exports.FeedFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FeedFilled", "", 2, undefined));
exports.FeedRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FeedRegular", "", 2, undefined));
exports.FilmstripFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FilmstripFilled", "", 2, undefined));
exports.FilmstripRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FilmstripRegular", "", 2, undefined));
exports.FilmstripImageFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FilmstripImageFilled", "", 2, undefined));
exports.FilmstripImageRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FilmstripImageRegular", "", 2, undefined));
exports.FilmstripPlayFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FilmstripPlayFilled", "", 2, undefined));
exports.FilmstripPlayRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FilmstripPlayRegular", "", 2, undefined));
exports.FilmstripSplitFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FilmstripSplitFilled", "", 2, undefined));
exports.FilmstripSplitRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FilmstripSplitRegular", "", 2, undefined));
exports.FilterFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FilterFilled", "", 2, undefined));
exports.FilterRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FilterRegular", "", 2, undefined));
exports.FilterAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FilterAddFilled", "", 2, undefined));
exports.FilterAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FilterAddRegular", "", 2, undefined));
exports.FilterDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FilterDismissFilled", "", 2, undefined));
exports.FilterDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FilterDismissRegular", "", 2, undefined));
exports.FilterSyncFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FilterSyncFilled", "", 2, undefined));
exports.FilterSyncRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FilterSyncRegular", "", 2, undefined));
exports.FingerprintFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FingerprintFilled", "", 2, undefined));
exports.FingerprintRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FingerprintRegular", "", 2, undefined));
exports.FireFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FireFilled", "", 2, undefined));
exports.FireRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FireRegular", "", 2, undefined));
exports.FireplaceFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FireplaceFilled", "", 2, undefined));
exports.FireplaceRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FireplaceRegular", "", 2, undefined));
exports.FixedWidthFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FixedWidthFilled", "", 2, undefined));
exports.FixedWidthRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FixedWidthRegular", "", 2, undefined));
exports.FlagFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlagFilled", "", 2, undefined));
exports.FlagRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlagRegular", "", 2, undefined));
exports.FlagCheckeredFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlagCheckeredFilled", "", 2, undefined));
exports.FlagCheckeredRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlagCheckeredRegular", "", 2, undefined));
exports.FlagClockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlagClockFilled", "", 2, undefined));
exports.FlagClockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlagClockRegular", "", 2, undefined));
exports.FlagOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlagOffFilled", "", 2, undefined));
exports.FlagOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlagOffRegular", "", 2, undefined));
exports.FlagPrideFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlagPrideFilled", "", 2, undefined));
exports.FlagPrideIntersexInclusiveProgressFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlagPrideIntersexInclusiveProgressFilled", "", 2, undefined));
exports.FlagPridePhiladelphiaFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlagPridePhiladelphiaFilled", "", 2, undefined));
exports.FlagPrideProgressFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlagPrideProgressFilled", "", 2, undefined));
exports.FlashFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashFilled", "", 2, undefined));
exports.FlashRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashRegular", "", 2, undefined));
exports.FlashAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashAddFilled", "", 2, undefined));
exports.FlashAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashAddRegular", "", 2, undefined));
exports.FlashAutoFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashAutoFilled", "", 2, undefined));
exports.FlashAutoRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashAutoRegular", "", 2, undefined));
exports.FlashCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashCheckmarkFilled", "", 2, undefined));
exports.FlashCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashCheckmarkRegular", "", 2, undefined));
exports.FlashFlowFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashFlowFilled", "", 2, undefined));
exports.FlashFlowRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashFlowRegular", "", 2, undefined));
exports.FlashOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashOffFilled", "", 2, undefined));
exports.FlashOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashOffRegular", "", 2, undefined));
exports.FlashPlayFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashPlayFilled", "", 2, undefined));
exports.FlashPlayRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashPlayRegular", "", 2, undefined));
exports.FlashSettingsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashSettingsFilled", "", 2, undefined));
exports.FlashSettingsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashSettingsRegular", "", 2, undefined));
exports.FlashSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashSparkleFilled", "", 2, undefined));
exports.FlashSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashSparkleRegular", "", 2, undefined));
exports.FlashlightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashlightFilled", "", 2, undefined));
exports.FlashlightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashlightRegular", "", 2, undefined));
exports.FlashlightOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashlightOffFilled", "", 2, undefined));
exports.FlashlightOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlashlightOffRegular", "", 2, undefined));
exports.FlipHorizontalFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlipHorizontalFilled", "", 2, undefined));
exports.FlipHorizontalRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlipHorizontalRegular", "", 2, undefined));
exports.FlipVerticalFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlipVerticalFilled", "", 2, undefined));
exports.FlipVerticalRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlipVerticalRegular", "", 2, undefined));
exports.FlowFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlowFilled", "", 2, undefined));
exports.FlowRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlowRegular", "", 2, undefined));
exports.FlowDotFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlowDotFilled", "", 2, undefined));
exports.FlowDotRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlowDotRegular", "", 2, undefined));
exports.FlowSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlowSparkleFilled", "", 2, undefined));
exports.FlowSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlowSparkleRegular", "", 2, undefined));
exports.FlowchartFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlowchartFilled", "", 2, undefined));
exports.FlowchartRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlowchartRegular", "", 2, undefined));
exports.FlowchartCircleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlowchartCircleFilled", "", 2, undefined));
exports.FlowchartCircleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FlowchartCircleRegular", "", 2, undefined));
exports.FluentFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FluentFilled", "", 2, undefined));
exports.FluentRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FluentRegular", "", 2, undefined));
exports.FluidFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FluidFilled", "", 2, undefined));
exports.FluidRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FluidRegular", "", 2, undefined));
exports.FolderFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderFilled", "", 2, undefined));
exports.FolderRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderRegular", "", 2, undefined));
exports.FolderAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderAddFilled", "", 2, undefined));
exports.FolderAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderAddRegular", "", 2, undefined));
exports.FolderArrowLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderArrowLeftFilled", "", 2, undefined));
exports.FolderArrowLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderArrowLeftRegular", "", 2, undefined));
exports.FolderArrowRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderArrowRightFilled", "", 2, undefined, { flipInRtl: true }));
exports.FolderArrowRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderArrowRightRegular", "", 2, undefined, { flipInRtl: true }));
exports.FolderArrowUpFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderArrowUpFilled", "", 2, undefined));
exports.FolderArrowUpRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderArrowUpRegular", "", 2, undefined));
exports.FolderBriefcaseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderBriefcaseFilled", "", 2, undefined));
exports.FolderBriefcaseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderBriefcaseRegular", "", 2, undefined));
exports.FolderDocumentFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderDocumentFilled", "", 2, undefined));
exports.FolderDocumentRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderDocumentRegular", "", 2, undefined));
exports.FolderGlobeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderGlobeFilled", "", 2, undefined));
exports.FolderGlobeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderGlobeRegular", "", 2, undefined));
exports.FolderLightningFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderLightningFilled", "", 2, undefined));
exports.FolderLightningRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderLightningRegular", "", 2, undefined));
exports.FolderLinkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderLinkFilled", "", 2, undefined));
exports.FolderLinkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderLinkRegular", "", 2, undefined));
exports.FolderListFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderListFilled", "", 2, undefined));
exports.FolderListRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderListRegular", "", 2, undefined));
exports.FolderMailFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderMailFilled", "", 2, undefined));
exports.FolderMailRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderMailRegular", "", 2, undefined));
exports.FolderOpenFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderOpenFilled", "", 2, undefined, { flipInRtl: true }));
exports.FolderOpenRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderOpenRegular", "", 2, undefined, { flipInRtl: true }));
exports.FolderOpenDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderOpenDownFilled", "", 2, undefined));
exports.FolderOpenDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderOpenDownRegular", "", 2, undefined));
exports.FolderOpenVerticalFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderOpenVerticalFilled", "", 2, undefined, { flipInRtl: true }));
exports.FolderOpenVerticalRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderOpenVerticalRegular", "", 2, undefined, { flipInRtl: true }));
exports.FolderPeopleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderPeopleFilled", "", 2, undefined));
exports.FolderPeopleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderPeopleRegular", "", 2, undefined));
exports.FolderPersonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderPersonFilled", "", 2, undefined));
exports.FolderPersonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderPersonRegular", "", 2, undefined));
exports.FolderProhibitedFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderProhibitedFilled", "", 2, undefined));
exports.FolderProhibitedRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderProhibitedRegular", "", 2, undefined));
exports.FolderSearchFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderSearchFilled", "", 2, undefined));
exports.FolderSearchRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderSearchRegular", "", 2, undefined));
exports.FolderSwapFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderSwapFilled", "", 2, undefined));
exports.FolderSwapRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderSwapRegular", "", 2, undefined));
exports.FolderSyncFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderSyncFilled", "", 2, undefined));
exports.FolderSyncRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderSyncRegular", "", 2, undefined));
exports.FolderZipFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderZipFilled", "", 2, undefined));
exports.FolderZipRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FolderZipRegular", "", 2, undefined));
exports.FontDecreaseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FontDecreaseFilled", "", 2, undefined));
exports.FontDecreaseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FontDecreaseRegular", "", 2, undefined));
exports.FontIncreaseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FontIncreaseFilled", "", 2, undefined));
exports.FontIncreaseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FontIncreaseRegular", "", 2, undefined));
exports.FontSpaceTrackingInFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FontSpaceTrackingInFilled", "", 2, undefined));
exports.FontSpaceTrackingInRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FontSpaceTrackingInRegular", "", 2, undefined));
exports.FontSpaceTrackingOutFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FontSpaceTrackingOutFilled", "", 2, undefined));
exports.FontSpaceTrackingOutRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FontSpaceTrackingOutRegular", "", 2, undefined));
exports.FoodFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FoodFilled", "", 2, undefined));
exports.FoodRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FoodRegular", "", 2, undefined));
exports.FoodAppleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FoodAppleFilled", "", 2, undefined));
exports.FoodAppleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FoodAppleRegular", "", 2, undefined));
exports.FoodCakeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FoodCakeFilled", "", 2, undefined));
exports.FoodCakeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FoodCakeRegular", "", 2, undefined));
exports.FoodCarrotFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FoodCarrotFilled", "", 2, undefined));
exports.FoodCarrotRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FoodCarrotRegular", "", 2, undefined));
exports.FoodChickenLegFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FoodChickenLegFilled", "", 2, undefined));
exports.FoodChickenLegRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FoodChickenLegRegular", "", 2, undefined));
exports.FoodEggFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FoodEggFilled", "", 2, undefined));
exports.FoodEggRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FoodEggRegular", "", 2, undefined));
exports.FoodFishFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FoodFishFilled", "", 2, undefined));
exports.FoodFishRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FoodFishRegular", "", 2, undefined));
exports.FoodGrainsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FoodGrainsFilled", "", 2, undefined));
exports.FoodGrainsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FoodGrainsRegular", "", 2, undefined));
exports.FoodPizzaFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FoodPizzaFilled", "", 2, undefined));
exports.FoodPizzaRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FoodPizzaRegular", "", 2, undefined));
exports.FoodToastFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FoodToastFilled", "", 2, undefined));
exports.FoodToastRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FoodToastRegular", "", 2, undefined));
exports.FormFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FormFilled", "", 2, undefined));
exports.FormRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FormRegular", "", 2, undefined));
exports.FormMultipleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FormMultipleFilled", "", 2, undefined));
exports.FormMultipleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FormMultipleRegular", "", 2, undefined));
exports.FormMultipleCollectionFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FormMultipleCollectionFilled", "", 2, undefined));
exports.FormMultipleCollectionRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FormMultipleCollectionRegular", "", 2, undefined));
exports.FormNewFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FormNewFilled", "", 2, undefined));
exports.FormNewRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FormNewRegular", "", 2, undefined));
exports.FormSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FormSparkleFilled", "", 2, undefined));
exports.FormSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FormSparkleRegular", "", 2, undefined));
exports.Fps120Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Fps120Filled", "", 2, undefined));
exports.Fps120Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Fps120Regular", "", 2, undefined));
exports.Fps240Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Fps240Filled", "", 2, undefined));
exports.Fps240Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Fps240Regular", "", 2, undefined));
exports.Fps30Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Fps30Filled", "", 2, undefined));
exports.Fps30Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Fps30Regular", "", 2, undefined));
exports.Fps60Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Fps60Filled", "", 2, undefined));
exports.Fps60Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Fps60Regular", "", 2, undefined));
exports.Fps960Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Fps960Filled", "", 2, undefined));
exports.Fps960Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Fps960Regular", "", 2, undefined));
exports.FrameFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FrameFilled", "", 2, undefined));
exports.FrameRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FrameRegular", "", 2, undefined));
exports.FullScreenMaximizeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FullScreenMaximizeFilled", "", 2, undefined));
exports.FullScreenMaximizeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FullScreenMaximizeRegular", "", 2, undefined));
exports.FullScreenMinimizeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FullScreenMinimizeFilled", "", 2, undefined));
exports.FullScreenMinimizeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("FullScreenMinimizeRegular", "", 2, undefined));
exports.GameChatFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GameChatFilled", "", 2, undefined));
exports.GameChatRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GameChatRegular", "", 2, undefined));
exports.GamesFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GamesFilled", "", 2, undefined));
exports.GamesRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GamesRegular", "", 2, undefined));
exports.GanttChartFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GanttChartFilled", "", 2, undefined));
exports.GanttChartRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GanttChartRegular", "", 2, undefined));
exports.GasFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GasFilled", "", 2, undefined));
exports.GasRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GasRegular", "", 2, undefined));
exports.GasPumpFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GasPumpFilled", "", 2, undefined));
exports.GasPumpRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GasPumpRegular", "", 2, undefined));
exports.GatherFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GatherFilled", "", 2, undefined));
exports.GatherRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GatherRegular", "", 2, undefined));
exports.GaugeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GaugeFilled", "", 2, undefined));
exports.GaugeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GaugeRegular", "", 2, undefined));
exports.GaugeAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GaugeAddFilled", "", 2, undefined));
exports.GaugeAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GaugeAddRegular", "", 2, undefined));
exports.GavelFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GavelFilled", "", 2, undefined));
exports.GavelRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GavelRegular", "", 2, undefined));
exports.GavelProhibitedFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GavelProhibitedFilled", "", 2, undefined));
exports.GavelProhibitedRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GavelProhibitedRegular", "", 2, undefined));
exports.GestureFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GestureFilled", "", 2, undefined));
exports.GestureRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GestureRegular", "", 2, undefined));
exports.GifFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GifFilled", "", 2, undefined));
exports.GifRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GifRegular", "", 2, undefined));
exports.GiftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GiftFilled", "", 2, undefined));
exports.GiftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GiftRegular", "", 2, undefined));
exports.GiftCardFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GiftCardFilled", "", 2, undefined));
exports.GiftCardRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GiftCardRegular", "", 2, undefined));
exports.GiftCardAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GiftCardAddFilled", "", 2, undefined));
exports.GiftCardAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GiftCardAddRegular", "", 2, undefined));
exports.GiftCardArrowRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GiftCardArrowRightFilled", "", 2, undefined));
exports.GiftCardArrowRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GiftCardArrowRightRegular", "", 2, undefined));
exports.GiftCardMoneyFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GiftCardMoneyFilled", "", 2, undefined));
exports.GiftCardMoneyRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GiftCardMoneyRegular", "", 2, undefined));
exports.GiftCardMultipleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GiftCardMultipleFilled", "", 2, undefined));
exports.GiftCardMultipleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GiftCardMultipleRegular", "", 2, undefined));
exports.GiftOpenFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GiftOpenFilled", "", 2, undefined));
exports.GiftOpenRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GiftOpenRegular", "", 2, undefined));
exports.GlanceFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlanceFilled", "", 2, undefined));
exports.GlanceRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlanceRegular", "", 2, undefined));
exports.GlanceHorizontalFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlanceHorizontalFilled", "", 2, undefined));
exports.GlanceHorizontalRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlanceHorizontalRegular", "", 2, undefined));
exports.GlanceHorizontalSparklesFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlanceHorizontalSparklesFilled", "", 2, undefined));
exports.GlanceHorizontalSparklesRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlanceHorizontalSparklesRegular", "", 2, undefined));
exports.GlassesFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlassesFilled", "", 2, undefined));
exports.GlassesRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlassesRegular", "", 2, undefined));
exports.GlassesOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlassesOffFilled", "", 2, undefined));
exports.GlassesOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlassesOffRegular", "", 2, undefined));
exports.GlobeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeFilled", "", 2, undefined));
exports.GlobeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeRegular", "", 2, undefined));
exports.GlobeAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeAddFilled", "", 2, undefined));
exports.GlobeAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeAddRegular", "", 2, undefined));
exports.GlobeArrowForwardFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeArrowForwardFilled", "", 2, undefined));
exports.GlobeArrowForwardRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeArrowForwardRegular", "", 2, undefined));
exports.GlobeArrowUpFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeArrowUpFilled", "", 2, undefined));
exports.GlobeArrowUpRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeArrowUpRegular", "", 2, undefined));
exports.GlobeClockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeClockFilled", "", 2, undefined));
exports.GlobeClockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeClockRegular", "", 2, undefined));
exports.GlobeDesktopFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeDesktopFilled", "", 2, undefined));
exports.GlobeDesktopRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeDesktopRegular", "", 2, undefined));
exports.GlobeErrorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeErrorFilled", "", 2, undefined));
exports.GlobeErrorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeErrorRegular", "", 2, undefined));
exports.GlobeLocationFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeLocationFilled", "", 2, undefined));
exports.GlobeLocationRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeLocationRegular", "", 2, undefined));
exports.GlobeOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeOffFilled", "", 2, undefined));
exports.GlobeOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeOffRegular", "", 2, undefined));
exports.GlobePersonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobePersonFilled", "", 2, undefined));
exports.GlobePersonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobePersonRegular", "", 2, undefined));
exports.GlobeProhibitedFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeProhibitedFilled", "", 2, undefined));
exports.GlobeProhibitedRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeProhibitedRegular", "", 2, undefined));
exports.GlobeSearchFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeSearchFilled", "", 2, undefined));
exports.GlobeSearchRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeSearchRegular", "", 2, undefined));
exports.GlobeShieldFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeShieldFilled", "", 2, undefined));
exports.GlobeShieldRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeShieldRegular", "", 2, undefined));
exports.GlobeStarFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeStarFilled", "", 2, undefined));
exports.GlobeStarRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeStarRegular", "", 2, undefined));
exports.GlobeSurfaceFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeSurfaceFilled", "", 2, undefined));
exports.GlobeSurfaceRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeSurfaceRegular", "", 2, undefined));
exports.GlobeSyncFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeSyncFilled", "", 2, undefined));
exports.GlobeSyncRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeSyncRegular", "", 2, undefined));
exports.GlobeVideoFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeVideoFilled", "", 2, undefined));
exports.GlobeVideoRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeVideoRegular", "", 2, undefined));
exports.GlobeWarningFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeWarningFilled", "", 2, undefined));
exports.GlobeWarningRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GlobeWarningRegular", "", 2, undefined));
exports.GridFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GridFilled", "", 2, undefined));
exports.GridRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GridRegular", "", 2, undefined));
exports.GridDotsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GridDotsFilled", "", 2, undefined));
exports.GridDotsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GridDotsRegular", "", 2, undefined));
exports.GridKanbanFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GridKanbanFilled", "", 2, undefined));
exports.GridKanbanRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GridKanbanRegular", "", 2, undefined));
exports.GroupFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GroupFilled", "", 2, undefined));
exports.GroupRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GroupRegular", "", 2, undefined));
exports.GroupDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GroupDismissFilled", "", 2, undefined));
exports.GroupDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GroupDismissRegular", "", 2, undefined));
exports.GroupListFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GroupListFilled", "", 2, undefined));
exports.GroupListRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GroupListRegular", "", 2, undefined));
exports.GroupReturnFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GroupReturnFilled", "", 2, undefined));
exports.GroupReturnRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GroupReturnRegular", "", 2, undefined));
exports.GuardianFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GuardianFilled", "", 2, undefined));
exports.GuardianRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GuardianRegular", "", 2, undefined));
exports.GuestFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GuestFilled", "", 2, undefined));
exports.GuestRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GuestRegular", "", 2, undefined));
exports.GuestAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GuestAddFilled", "", 2, undefined));
exports.GuestAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GuestAddRegular", "", 2, undefined));
exports.GuitarFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GuitarFilled", "", 2, undefined));
exports.GuitarRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("GuitarRegular", "", 2, undefined));
exports.HandDrawFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HandDrawFilled", "", 2, undefined));
exports.HandDrawRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HandDrawRegular", "", 2, undefined));
exports.HandLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HandLeftFilled", "", 2, undefined));
exports.HandLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HandLeftRegular", "", 2, undefined));
exports.HandLeftChatFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HandLeftChatFilled", "", 2, undefined));
exports.HandLeftChatRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HandLeftChatRegular", "", 2, undefined));
exports.HandMultipleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HandMultipleFilled", "", 2, undefined));
exports.HandMultipleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HandMultipleRegular", "", 2, undefined));
exports.HandOpenHeartFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HandOpenHeartFilled", "", 2, undefined));
exports.HandOpenHeartRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HandOpenHeartRegular", "", 2, undefined));
exports.HandPointFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HandPointFilled", "", 2, undefined));
exports.HandPointRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HandPointRegular", "", 2, undefined));
exports.HandRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HandRightFilled", "", 2, undefined));
exports.HandRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HandRightRegular", "", 2, undefined));
exports.HandRightOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HandRightOffFilled", "", 2, undefined));
exports.HandRightOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HandRightOffRegular", "", 2, undefined));
exports.HandWaveFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HandWaveFilled", "", 2, undefined));
exports.HandWaveRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HandWaveRegular", "", 2, undefined));
exports.HandshakeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HandshakeFilled", "", 2, undefined));
exports.HandshakeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HandshakeRegular", "", 2, undefined));
exports.HapticStrongFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HapticStrongFilled", "", 2, undefined));
exports.HapticStrongRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HapticStrongRegular", "", 2, undefined));
exports.HapticWeakFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HapticWeakFilled", "", 2, undefined));
exports.HapticWeakRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HapticWeakRegular", "", 2, undefined));
exports.HardDriveFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HardDriveFilled", "", 2, undefined));
exports.HardDriveRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HardDriveRegular", "", 2, undefined));
exports.HatGraduationFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HatGraduationFilled", "", 2, undefined));
exports.HatGraduationRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HatGraduationRegular", "", 2, undefined));
exports.HatGraduationAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HatGraduationAddFilled", "", 2, undefined));
exports.HatGraduationAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HatGraduationAddRegular", "", 2, undefined));
exports.HatGraduationSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HatGraduationSparkleFilled", "", 2, undefined));
exports.HatGraduationSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HatGraduationSparkleRegular", "", 2, undefined));
exports.HdFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HdFilled", "", 2, undefined));
exports.HdRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HdRegular", "", 2, undefined));
exports.HdOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HdOffFilled", "", 2, undefined));
exports.HdOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HdOffRegular", "", 2, undefined));
exports.HdrFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HdrFilled", "", 2, undefined));
exports.HdrRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HdrRegular", "", 2, undefined));
exports.HdrOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HdrOffFilled", "", 2, undefined));
exports.HdrOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HdrOffRegular", "", 2, undefined));
exports.HeadphonesFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeadphonesFilled", "", 2, undefined));
exports.HeadphonesRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeadphonesRegular", "", 2, undefined));
exports.HeadphonesSoundWaveFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeadphonesSoundWaveFilled", "", 2, undefined));
exports.HeadphonesSoundWaveRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeadphonesSoundWaveRegular", "", 2, undefined));
exports.HeadsetFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeadsetFilled", "", 2, undefined));
exports.HeadsetRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeadsetRegular", "", 2, undefined));
exports.HeadsetAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeadsetAddFilled", "", 2, undefined));
exports.HeadsetAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeadsetAddRegular", "", 2, undefined));
exports.HeadsetVrFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeadsetVrFilled", "", 2, undefined));
exports.HeadsetVrRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeadsetVrRegular", "", 2, undefined));
exports.HeartFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeartFilled", "", 2, undefined));
exports.HeartRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeartRegular", "", 2, undefined));
exports.HeartBrokenFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeartBrokenFilled", "", 2, undefined));
exports.HeartBrokenRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeartBrokenRegular", "", 2, undefined));
exports.HeartCircleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeartCircleFilled", "", 2, undefined));
exports.HeartCircleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeartCircleRegular", "", 2, undefined));
exports.HeartCircleHintFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeartCircleHintFilled", "", 2, undefined));
exports.HeartCircleHintRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeartCircleHintRegular", "", 2, undefined));
exports.HeartOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeartOffFilled", "", 2, undefined));
exports.HeartOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeartOffRegular", "", 2, undefined));
exports.HeartPulseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeartPulseFilled", "", 2, undefined));
exports.HeartPulseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeartPulseRegular", "", 2, undefined));
exports.HeartPulseCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeartPulseCheckmarkFilled", "", 2, undefined));
exports.HeartPulseCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeartPulseCheckmarkRegular", "", 2, undefined));
exports.HeartPulseErrorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeartPulseErrorFilled", "", 2, undefined));
exports.HeartPulseErrorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeartPulseErrorRegular", "", 2, undefined));
exports.HeartPulseWarningFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeartPulseWarningFilled", "", 2, undefined));
exports.HeartPulseWarningRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HeartPulseWarningRegular", "", 2, undefined));
exports.HexagonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HexagonFilled", "", 2, undefined));
exports.HexagonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HexagonRegular", "", 2, undefined));
exports.HexagonSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HexagonSparkleFilled", "", 2, undefined));
exports.HexagonSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HexagonSparkleRegular", "", 2, undefined));
exports.HexagonThreeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HexagonThreeFilled", "", 2, undefined));
exports.HexagonThreeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HexagonThreeRegular", "", 2, undefined));
exports.HighlightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HighlightFilled", "", 2, undefined));
exports.HighlightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HighlightRegular", "", 2, undefined));
exports.HighlightAccentFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HighlightAccentFilled", "", 2, undefined));
exports.HighlightLinkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HighlightLinkFilled", "", 2, undefined));
exports.HighlightLinkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HighlightLinkRegular", "", 2, undefined));
exports.HighwayFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HighwayFilled", "", 2, undefined));
exports.HighwayRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HighwayRegular", "", 2, undefined));
exports.HistoryFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HistoryFilled", "", 2, undefined));
exports.HistoryRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HistoryRegular", "", 2, undefined));
exports.HistoryDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HistoryDismissFilled", "", 2, undefined));
exports.HistoryDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HistoryDismissRegular", "", 2, undefined));
exports.HomeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HomeFilled", "", 2, undefined));
exports.HomeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HomeRegular", "", 2, undefined));
exports.HomeAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HomeAddFilled", "", 2, undefined));
exports.HomeAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HomeAddRegular", "", 2, undefined));
exports.HomeCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HomeCheckmarkFilled", "", 2, undefined));
exports.HomeCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HomeCheckmarkRegular", "", 2, undefined));
exports.HomeDatabaseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HomeDatabaseFilled", "", 2, undefined));
exports.HomeDatabaseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HomeDatabaseRegular", "", 2, undefined));
exports.HomeEmptyFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HomeEmptyFilled", "", 2, undefined));
exports.HomeEmptyRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HomeEmptyRegular", "", 2, undefined));
exports.HomeGarageFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HomeGarageFilled", "", 2, undefined));
exports.HomeGarageRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HomeGarageRegular", "", 2, undefined));
exports.HomeHeartFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HomeHeartFilled", "", 2, undefined));
exports.HomeHeartRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HomeHeartRegular", "", 2, undefined));
exports.HomeMoreFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HomeMoreFilled", "", 2, undefined));
exports.HomeMoreRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HomeMoreRegular", "", 2, undefined));
exports.HomePersonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HomePersonFilled", "", 2, undefined));
exports.HomePersonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HomePersonRegular", "", 2, undefined));
exports.HomeSplitFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HomeSplitFilled", "", 2, undefined));
exports.HomeSplitRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HomeSplitRegular", "", 2, undefined));
exports.HourglassFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HourglassFilled", "", 2, undefined));
exports.HourglassRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HourglassRegular", "", 2, undefined));
exports.HourglassHalfFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HourglassHalfFilled", "", 2, undefined));
exports.HourglassHalfRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HourglassHalfRegular", "", 2, undefined));
exports.HourglassOneQuarterFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HourglassOneQuarterFilled", "", 2, undefined));
exports.HourglassOneQuarterRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HourglassOneQuarterRegular", "", 2, undefined));
exports.HourglassThreeQuarterFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HourglassThreeQuarterFilled", "", 2, undefined));
exports.HourglassThreeQuarterRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("HourglassThreeQuarterRegular", "", 2, undefined));
exports.IconsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("IconsFilled", "", 2, undefined));
exports.IconsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("IconsRegular", "", 2, undefined));
exports.ImageFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageFilled", "", 2, undefined));
