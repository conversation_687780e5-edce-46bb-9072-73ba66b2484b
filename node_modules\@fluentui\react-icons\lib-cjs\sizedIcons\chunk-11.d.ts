import type { FluentIcon } from "../utils/createFluentIcon";
export declare const MoneyHand16Filled: FluentIcon;
export declare const MoneyHand16Regular: FluentIcon;
export declare const MoneyHand20Filled: FluentIcon;
export declare const MoneyHand20Regular: FluentIcon;
export declare const MoneyHand24Filled: FluentIcon;
export declare const MoneyHand24Regular: FluentIcon;
export declare const MoneyOff20Filled: FluentIcon;
export declare const MoneyOff20Regular: FluentIcon;
export declare const MoneyOff24Filled: FluentIcon;
export declare const MoneyOff24Regular: FluentIcon;
export declare const MoneySettings16Filled: FluentIcon;
export declare const MoneySettings16Regular: FluentIcon;
export declare const MoneySettings20Filled: FluentIcon;
export declare const MoneySettings20Regular: FluentIcon;
export declare const MoneySettings24Filled: FluentIcon;
export declare const MoneySettings24Regular: FluentIcon;
export declare const MoreCircle16Filled: FluentIcon;
export declare const MoreCircle16Regular: FluentIcon;
export declare const MoreCircle20Filled: FluentIcon;
export declare const MoreCircle20Regular: FluentIcon;
export declare const MoreCircle24Filled: FluentIcon;
export declare const MoreCircle24Regular: FluentIcon;
export declare const MoreCircle28Filled: FluentIcon;
export declare const MoreCircle28Regular: FluentIcon;
export declare const MoreCircle32Filled: FluentIcon;
export declare const MoreCircle32Regular: FluentIcon;
export declare const MoreCircle48Filled: FluentIcon;
export declare const MoreCircle48Regular: FluentIcon;
export declare const MoreHorizontal16Filled: FluentIcon;
export declare const MoreHorizontal16Regular: FluentIcon;
export declare const MoreHorizontal20Filled: FluentIcon;
export declare const MoreHorizontal20Regular: FluentIcon;
export declare const MoreHorizontal24Filled: FluentIcon;
export declare const MoreHorizontal24Regular: FluentIcon;
export declare const MoreHorizontal28Filled: FluentIcon;
export declare const MoreHorizontal28Regular: FluentIcon;
export declare const MoreHorizontal32Filled: FluentIcon;
export declare const MoreHorizontal32Regular: FluentIcon;
export declare const MoreHorizontal48Filled: FluentIcon;
export declare const MoreHorizontal48Regular: FluentIcon;
export declare const MoreVertical16Filled: FluentIcon;
export declare const MoreVertical16Regular: FluentIcon;
export declare const MoreVertical20Filled: FluentIcon;
export declare const MoreVertical20Regular: FluentIcon;
export declare const MoreVertical24Filled: FluentIcon;
export declare const MoreVertical24Regular: FluentIcon;
export declare const MoreVertical28Filled: FluentIcon;
export declare const MoreVertical28Regular: FluentIcon;
export declare const MoreVertical32Filled: FluentIcon;
export declare const MoreVertical32Regular: FluentIcon;
export declare const MoreVertical48Filled: FluentIcon;
export declare const MoreVertical48Regular: FluentIcon;
export declare const MountainLocationBottom20Filled: FluentIcon;
export declare const MountainLocationBottom20Regular: FluentIcon;
export declare const MountainLocationBottom24Filled: FluentIcon;
export declare const MountainLocationBottom24Regular: FluentIcon;
export declare const MountainLocationBottom28Filled: FluentIcon;
export declare const MountainLocationBottom28Regular: FluentIcon;
export declare const MountainLocationTop20Filled: FluentIcon;
export declare const MountainLocationTop20Regular: FluentIcon;
export declare const MountainLocationTop24Filled: FluentIcon;
export declare const MountainLocationTop24Regular: FluentIcon;
export declare const MountainLocationTop28Filled: FluentIcon;
export declare const MountainLocationTop28Regular: FluentIcon;
export declare const MountainTrail20Filled: FluentIcon;
export declare const MountainTrail20Regular: FluentIcon;
export declare const MountainTrail24Filled: FluentIcon;
export declare const MountainTrail24Regular: FluentIcon;
export declare const MountainTrail28Filled: FluentIcon;
export declare const MountainTrail28Regular: FluentIcon;
export declare const MoviesAndTv16Filled: FluentIcon;
export declare const MoviesAndTv16Regular: FluentIcon;
export declare const MoviesAndTv20Filled: FluentIcon;
export declare const MoviesAndTv20Regular: FluentIcon;
export declare const MoviesAndTv24Filled: FluentIcon;
export declare const MoviesAndTv24Regular: FluentIcon;
export declare const Multiplier12X20Filled: FluentIcon;
export declare const Multiplier12X20Regular: FluentIcon;
export declare const Multiplier12X24Filled: FluentIcon;
export declare const Multiplier12X24Regular: FluentIcon;
export declare const Multiplier12X28Filled: FluentIcon;
export declare const Multiplier12X28Regular: FluentIcon;
export declare const Multiplier12X32Filled: FluentIcon;
export declare const Multiplier12X32Regular: FluentIcon;
export declare const Multiplier12X48Filled: FluentIcon;
export declare const Multiplier12X48Regular: FluentIcon;
export declare const Multiplier15X20Filled: FluentIcon;
export declare const Multiplier15X20Regular: FluentIcon;
export declare const Multiplier15X24Filled: FluentIcon;
export declare const Multiplier15X24Regular: FluentIcon;
export declare const Multiplier15X28Filled: FluentIcon;
export declare const Multiplier15X28Regular: FluentIcon;
export declare const Multiplier15X32Filled: FluentIcon;
export declare const Multiplier15X32Regular: FluentIcon;
export declare const Multiplier15X48Filled: FluentIcon;
export declare const Multiplier15X48Regular: FluentIcon;
export declare const Multiplier18X20Filled: FluentIcon;
export declare const Multiplier18X20Regular: FluentIcon;
export declare const Multiplier18X24Filled: FluentIcon;
export declare const Multiplier18X24Regular: FluentIcon;
export declare const Multiplier18X28Filled: FluentIcon;
export declare const Multiplier18X28Regular: FluentIcon;
export declare const Multiplier18X32Filled: FluentIcon;
export declare const Multiplier18X32Regular: FluentIcon;
export declare const Multiplier18X48Filled: FluentIcon;
export declare const Multiplier18X48Regular: FluentIcon;
export declare const Multiplier1X20Filled: FluentIcon;
export declare const Multiplier1X20Regular: FluentIcon;
export declare const Multiplier1X24Filled: FluentIcon;
export declare const Multiplier1X24Regular: FluentIcon;
export declare const Multiplier1X28Filled: FluentIcon;
export declare const Multiplier1X28Regular: FluentIcon;
export declare const Multiplier1X32Filled: FluentIcon;
export declare const Multiplier1X32Regular: FluentIcon;
export declare const Multiplier1X48Filled: FluentIcon;
export declare const Multiplier1X48Regular: FluentIcon;
export declare const Multiplier2X20Filled: FluentIcon;
export declare const Multiplier2X20Regular: FluentIcon;
export declare const Multiplier2X24Filled: FluentIcon;
export declare const Multiplier2X24Regular: FluentIcon;
export declare const Multiplier2X28Filled: FluentIcon;
export declare const Multiplier2X28Regular: FluentIcon;
export declare const Multiplier2X32Filled: FluentIcon;
export declare const Multiplier2X32Regular: FluentIcon;
export declare const Multiplier2X48Filled: FluentIcon;
export declare const Multiplier2X48Regular: FluentIcon;
export declare const Multiplier5X20Filled: FluentIcon;
export declare const Multiplier5X20Regular: FluentIcon;
export declare const Multiplier5X24Filled: FluentIcon;
export declare const Multiplier5X24Regular: FluentIcon;
export declare const Multiplier5X28Filled: FluentIcon;
export declare const Multiplier5X28Regular: FluentIcon;
export declare const Multiplier5X32Filled: FluentIcon;
export declare const Multiplier5X32Regular: FluentIcon;
export declare const Multiplier5X48Filled: FluentIcon;
export declare const Multiplier5X48Regular: FluentIcon;
export declare const MultiselectLtr16Filled: FluentIcon;
export declare const MultiselectLtr16Regular: FluentIcon;
export declare const MultiselectLtr20Filled: FluentIcon;
export declare const MultiselectLtr20Regular: FluentIcon;
export declare const MultiselectLtr24Filled: FluentIcon;
export declare const MultiselectLtr24Regular: FluentIcon;
export declare const MultiselectRtl16Filled: FluentIcon;
export declare const MultiselectRtl16Regular: FluentIcon;
export declare const MultiselectRtl20Filled: FluentIcon;
export declare const MultiselectRtl20Regular: FluentIcon;
export declare const MultiselectRtl24Filled: FluentIcon;
export declare const MultiselectRtl24Regular: FluentIcon;
export declare const MusicNote120Filled: FluentIcon;
export declare const MusicNote120Regular: FluentIcon;
export declare const MusicNote124Filled: FluentIcon;
export declare const MusicNote124Regular: FluentIcon;
export declare const MusicNote216Filled: FluentIcon;
export declare const MusicNote216Regular: FluentIcon;
export declare const MusicNote220Filled: FluentIcon;
export declare const MusicNote220Regular: FluentIcon;
export declare const MusicNote224Filled: FluentIcon;
export declare const MusicNote224Regular: FluentIcon;
export declare const MusicNote2Play20Filled: FluentIcon;
export declare const MusicNote2Play20Regular: FluentIcon;
export declare const MusicNoteOff120Filled: FluentIcon;
export declare const MusicNoteOff120Regular: FluentIcon;
export declare const MusicNoteOff124Filled: FluentIcon;
export declare const MusicNoteOff124Regular: FluentIcon;
export declare const MusicNoteOff216Filled: FluentIcon;
export declare const MusicNoteOff216Regular: FluentIcon;
export declare const MusicNoteOff220Filled: FluentIcon;
export declare const MusicNoteOff220Regular: FluentIcon;
export declare const MusicNoteOff224Filled: FluentIcon;
export declare const MusicNoteOff224Regular: FluentIcon;
export declare const MyLocation12Filled: FluentIcon;
export declare const MyLocation12Regular: FluentIcon;
export declare const MyLocation16Filled: FluentIcon;
export declare const MyLocation16Regular: FluentIcon;
export declare const MyLocation20Filled: FluentIcon;
export declare const MyLocation20Regular: FluentIcon;
export declare const MyLocation24Filled: FluentIcon;
export declare const MyLocation24Regular: FluentIcon;
export declare const Navigation16Filled: FluentIcon;
export declare const Navigation16Regular: FluentIcon;
export declare const Navigation20Filled: FluentIcon;
export declare const Navigation20Regular: FluentIcon;
export declare const Navigation24Filled: FluentIcon;
export declare const Navigation24Regular: FluentIcon;
export declare const Navigation28Filled: FluentIcon;
export declare const Navigation28Regular: FluentIcon;
export declare const Navigation32Filled: FluentIcon;
export declare const Navigation32Regular: FluentIcon;
export declare const Navigation48Filled: FluentIcon;
export declare const Navigation48Regular: FluentIcon;
export declare const NavigationBriefcase20Filled: FluentIcon;
export declare const NavigationBriefcase20Regular: FluentIcon;
export declare const NavigationBriefcase24Filled: FluentIcon;
export declare const NavigationBriefcase24Regular: FluentIcon;
export declare const NavigationBriefcase28Filled: FluentIcon;
export declare const NavigationBriefcase28Regular: FluentIcon;
export declare const NavigationLocationTarget20Filled: FluentIcon;
export declare const NavigationLocationTarget20Regular: FluentIcon;
export declare const NavigationPerson20Filled: FluentIcon;
export declare const NavigationPerson20Regular: FluentIcon;
export declare const NavigationPerson24Filled: FluentIcon;
export declare const NavigationPerson24Regular: FluentIcon;
export declare const NavigationPerson28Filled: FluentIcon;
export declare const NavigationPerson28Regular: FluentIcon;
export declare const NavigationPlay20Filled: FluentIcon;
export declare const NavigationPlay20Regular: FluentIcon;
export declare const NavigationUnread20Filled: FluentIcon;
export declare const NavigationUnread20Regular: FluentIcon;
export declare const NavigationUnread24Filled: FluentIcon;
export declare const NavigationUnread24Regular: FluentIcon;
export declare const NetworkAdapter16Filled: FluentIcon;
export declare const NetworkAdapter16Regular: FluentIcon;
export declare const NetworkCheck20Filled: FluentIcon;
export declare const NetworkCheck20Regular: FluentIcon;
export declare const NetworkCheck24Filled: FluentIcon;
export declare const NetworkCheck24Regular: FluentIcon;
export declare const New16Filled: FluentIcon;
export declare const New16Regular: FluentIcon;
export declare const New20Filled: FluentIcon;
export declare const New20Regular: FluentIcon;
export declare const New24Filled: FluentIcon;
export declare const New24Regular: FluentIcon;
export declare const News16Color: FluentIcon;
export declare const News16Filled: FluentIcon;
export declare const News16Regular: FluentIcon;
export declare const News20Color: FluentIcon;
export declare const News20Filled: FluentIcon;
export declare const News20Regular: FluentIcon;
export declare const News24Color: FluentIcon;
export declare const News24Filled: FluentIcon;
export declare const News24Regular: FluentIcon;
export declare const News28Color: FluentIcon;
export declare const News28Filled: FluentIcon;
export declare const News28Regular: FluentIcon;
export declare const Next16Filled: FluentIcon;
export declare const Next16Regular: FluentIcon;
export declare const Next20Filled: FluentIcon;
export declare const Next20Regular: FluentIcon;
export declare const Next24Filled: FluentIcon;
export declare const Next24Regular: FluentIcon;
export declare const Next28Filled: FluentIcon;
export declare const Next28Regular: FluentIcon;
export declare const Next32Filled: FluentIcon;
export declare const Next32Regular: FluentIcon;
export declare const Next48Filled: FluentIcon;
export declare const Next48Regular: FluentIcon;
export declare const NextFrame20Filled: FluentIcon;
export declare const NextFrame20Regular: FluentIcon;
export declare const NextFrame24Filled: FluentIcon;
export declare const NextFrame24Regular: FluentIcon;
export declare const Note16Filled: FluentIcon;
export declare const Note16Regular: FluentIcon;
export declare const Note20Filled: FluentIcon;
export declare const Note20Regular: FluentIcon;
export declare const Note24Filled: FluentIcon;
export declare const Note24Regular: FluentIcon;
export declare const Note28Filled: FluentIcon;
export declare const Note28Regular: FluentIcon;
export declare const Note32Light: FluentIcon;
export declare const Note48Filled: FluentIcon;
export declare const Note48Regular: FluentIcon;
export declare const NoteAdd16Filled: FluentIcon;
export declare const NoteAdd16Regular: FluentIcon;
export declare const NoteAdd20Filled: FluentIcon;
export declare const NoteAdd20Regular: FluentIcon;
export declare const NoteAdd24Filled: FluentIcon;
export declare const NoteAdd24Regular: FluentIcon;
export declare const NoteAdd28Filled: FluentIcon;
export declare const NoteAdd28Regular: FluentIcon;
export declare const NoteAdd48Filled: FluentIcon;
export declare const NoteAdd48Regular: FluentIcon;
export declare const NoteEdit20Filled: FluentIcon;
export declare const NoteEdit20Regular: FluentIcon;
export declare const NoteEdit24Filled: FluentIcon;
export declare const NoteEdit24Regular: FluentIcon;
export declare const NotePin16Filled: FluentIcon;
export declare const NotePin16Regular: FluentIcon;
export declare const NotePin20Filled: FluentIcon;
export declare const NotePin20Regular: FluentIcon;
export declare const Notebook16Color: FluentIcon;
export declare const Notebook16Filled: FluentIcon;
export declare const Notebook16Regular: FluentIcon;
export declare const Notebook20Color: FluentIcon;
export declare const Notebook20Filled: FluentIcon;
export declare const Notebook20Regular: FluentIcon;
export declare const Notebook24Color: FluentIcon;
export declare const Notebook24Filled: FluentIcon;
export declare const Notebook24Regular: FluentIcon;
export declare const Notebook32Color: FluentIcon;
export declare const Notebook32Filled: FluentIcon;
export declare const Notebook32Regular: FluentIcon;
export declare const NotebookAdd20Filled: FluentIcon;
export declare const NotebookAdd20Regular: FluentIcon;
export declare const NotebookAdd24Filled: FluentIcon;
export declare const NotebookAdd24Regular: FluentIcon;
export declare const NotebookArrowCurveDown20Filled: FluentIcon;
export declare const NotebookArrowCurveDown20Regular: FluentIcon;
export declare const NotebookError20Filled: FluentIcon;
export declare const NotebookError20Regular: FluentIcon;
export declare const NotebookError24Filled: FluentIcon;
export declare const NotebookError24Regular: FluentIcon;
export declare const NotebookEye20Filled: FluentIcon;
export declare const NotebookEye20Regular: FluentIcon;
export declare const NotebookLightning20Filled: FluentIcon;
export declare const NotebookLightning20Regular: FluentIcon;
export declare const NotebookLightning24Filled: FluentIcon;
export declare const NotebookLightning24Regular: FluentIcon;
export declare const NotebookQuestionMark20Color: FluentIcon;
export declare const NotebookQuestionMark20Filled: FluentIcon;
export declare const NotebookQuestionMark20Regular: FluentIcon;
export declare const NotebookQuestionMark24Color: FluentIcon;
export declare const NotebookQuestionMark24Filled: FluentIcon;
export declare const NotebookQuestionMark24Regular: FluentIcon;
export declare const NotebookSection20Filled: FluentIcon;
export declare const NotebookSection20Regular: FluentIcon;
export declare const NotebookSection24Filled: FluentIcon;
export declare const NotebookSection24Regular: FluentIcon;
export declare const NotebookSectionArrowRight20Filled: FluentIcon;
export declare const NotebookSectionArrowRight20Regular: FluentIcon;
export declare const NotebookSectionArrowRight24Filled: FluentIcon;
export declare const NotebookSectionArrowRight24Regular: FluentIcon;
export declare const NotebookSubsection20Filled: FluentIcon;
export declare const NotebookSubsection20Regular: FluentIcon;
export declare const NotebookSubsection24Filled: FluentIcon;
export declare const NotebookSubsection24Regular: FluentIcon;
export declare const NotebookSync20Filled: FluentIcon;
export declare const NotebookSync20Regular: FluentIcon;
export declare const NotebookSync24Filled: FluentIcon;
export declare const NotebookSync24Regular: FluentIcon;
export declare const Notepad12Filled: FluentIcon;
export declare const Notepad12Regular: FluentIcon;
export declare const Notepad16Filled: FluentIcon;
export declare const Notepad16Regular: FluentIcon;
export declare const Notepad20Filled: FluentIcon;
export declare const Notepad20Regular: FluentIcon;
export declare const Notepad24Filled: FluentIcon;
export declare const Notepad24Regular: FluentIcon;
export declare const Notepad28Filled: FluentIcon;
export declare const Notepad28Regular: FluentIcon;
export declare const Notepad32Filled: FluentIcon;
export declare const Notepad32Regular: FluentIcon;
export declare const Notepad48Filled: FluentIcon;
export declare const Notepad48Regular: FluentIcon;
export declare const NotepadEdit16Filled: FluentIcon;
export declare const NotepadEdit16Regular: FluentIcon;
export declare const NotepadEdit20Filled: FluentIcon;
export declare const NotepadEdit20Regular: FluentIcon;
export declare const NotepadPerson16Filled: FluentIcon;
export declare const NotepadPerson16Regular: FluentIcon;
export declare const NotepadPerson20Filled: FluentIcon;
export declare const NotepadPerson20Regular: FluentIcon;
export declare const NotepadPerson24Filled: FluentIcon;
export declare const NotepadPerson24Regular: FluentIcon;
export declare const NotepadPerson28Filled: FluentIcon;
export declare const NotepadPerson28Regular: FluentIcon;
export declare const NotepadPerson32Filled: FluentIcon;
export declare const NotepadPerson32Regular: FluentIcon;
export declare const NotepadPerson48Filled: FluentIcon;
export declare const NotepadPerson48Regular: FluentIcon;
export declare const NotepadPersonOff16Filled: FluentIcon;
export declare const NotepadPersonOff16Regular: FluentIcon;
export declare const NotepadPersonOff20Filled: FluentIcon;
export declare const NotepadPersonOff20Regular: FluentIcon;
export declare const NotepadPersonOff24Filled: FluentIcon;
export declare const NotepadPersonOff24Regular: FluentIcon;
export declare const NotepadPersonOff28Filled: FluentIcon;
export declare const NotepadPersonOff28Regular: FluentIcon;
export declare const NotepadPersonOff32Filled: FluentIcon;
export declare const NotepadPersonOff32Regular: FluentIcon;
export declare const NotepadPersonOff48Filled: FluentIcon;
export declare const NotepadPersonOff48Regular: FluentIcon;
export declare const NotepadSparkle16Filled: FluentIcon;
export declare const NotepadSparkle16Regular: FluentIcon;
export declare const NotepadSparkle20Filled: FluentIcon;
export declare const NotepadSparkle20Regular: FluentIcon;
export declare const NotepadSparkle24Filled: FluentIcon;
export declare const NotepadSparkle24Regular: FluentIcon;
export declare const NotepadSparkle28Filled: FluentIcon;
export declare const NotepadSparkle28Regular: FluentIcon;
export declare const NotepadSparkle32Filled: FluentIcon;
export declare const NotepadSparkle32Regular: FluentIcon;
export declare const NumberCircle016Filled: FluentIcon;
export declare const NumberCircle016Regular: FluentIcon;
export declare const NumberCircle020Filled: FluentIcon;
export declare const NumberCircle020Regular: FluentIcon;
export declare const NumberCircle024Filled: FluentIcon;
export declare const NumberCircle024Regular: FluentIcon;
export declare const NumberCircle028Filled: FluentIcon;
export declare const NumberCircle028Regular: FluentIcon;
export declare const NumberCircle032Filled: FluentIcon;
export declare const NumberCircle032Regular: FluentIcon;
export declare const NumberCircle048Filled: FluentIcon;
export declare const NumberCircle048Regular: FluentIcon;
export declare const NumberCircle116Filled: FluentIcon;
export declare const NumberCircle116Regular: FluentIcon;
export declare const NumberCircle120Filled: FluentIcon;
export declare const NumberCircle120Regular: FluentIcon;
export declare const NumberCircle124Filled: FluentIcon;
export declare const NumberCircle124Regular: FluentIcon;
export declare const NumberCircle128Filled: FluentIcon;
export declare const NumberCircle128Regular: FluentIcon;
export declare const NumberCircle132Filled: FluentIcon;
export declare const NumberCircle132Regular: FluentIcon;
export declare const NumberCircle148Filled: FluentIcon;
export declare const NumberCircle148Regular: FluentIcon;
export declare const NumberCircle216Filled: FluentIcon;
export declare const NumberCircle216Regular: FluentIcon;
export declare const NumberCircle220Filled: FluentIcon;
export declare const NumberCircle220Regular: FluentIcon;
export declare const NumberCircle224Filled: FluentIcon;
export declare const NumberCircle224Regular: FluentIcon;
export declare const NumberCircle228Filled: FluentIcon;
export declare const NumberCircle228Regular: FluentIcon;
export declare const NumberCircle232Filled: FluentIcon;
export declare const NumberCircle232Regular: FluentIcon;
export declare const NumberCircle248Filled: FluentIcon;
export declare const NumberCircle248Regular: FluentIcon;
export declare const NumberCircle316Filled: FluentIcon;
export declare const NumberCircle316Regular: FluentIcon;
export declare const NumberCircle320Filled: FluentIcon;
export declare const NumberCircle320Regular: FluentIcon;
export declare const NumberCircle324Filled: FluentIcon;
export declare const NumberCircle324Regular: FluentIcon;
export declare const NumberCircle328Filled: FluentIcon;
export declare const NumberCircle328Regular: FluentIcon;
export declare const NumberCircle332Filled: FluentIcon;
export declare const NumberCircle332Regular: FluentIcon;
export declare const NumberCircle348Filled: FluentIcon;
export declare const NumberCircle348Regular: FluentIcon;
export declare const NumberCircle416Filled: FluentIcon;
export declare const NumberCircle416Regular: FluentIcon;
export declare const NumberCircle420Filled: FluentIcon;
export declare const NumberCircle420Regular: FluentIcon;
export declare const NumberCircle424Filled: FluentIcon;
export declare const NumberCircle424Regular: FluentIcon;
export declare const NumberCircle428Filled: FluentIcon;
export declare const NumberCircle428Regular: FluentIcon;
export declare const NumberCircle432Filled: FluentIcon;
export declare const NumberCircle432Regular: FluentIcon;
export declare const NumberCircle448Filled: FluentIcon;
export declare const NumberCircle448Regular: FluentIcon;
export declare const NumberCircle516Filled: FluentIcon;
export declare const NumberCircle516Regular: FluentIcon;
export declare const NumberCircle520Filled: FluentIcon;
export declare const NumberCircle520Regular: FluentIcon;
export declare const NumberCircle524Filled: FluentIcon;
export declare const NumberCircle524Regular: FluentIcon;
export declare const NumberCircle528Filled: FluentIcon;
export declare const NumberCircle528Regular: FluentIcon;
export declare const NumberCircle532Filled: FluentIcon;
export declare const NumberCircle532Regular: FluentIcon;
export declare const NumberCircle548Filled: FluentIcon;
export declare const NumberCircle548Regular: FluentIcon;
export declare const NumberCircle616Filled: FluentIcon;
export declare const NumberCircle616Regular: FluentIcon;
export declare const NumberCircle620Filled: FluentIcon;
export declare const NumberCircle620Regular: FluentIcon;
export declare const NumberCircle624Filled: FluentIcon;
export declare const NumberCircle624Regular: FluentIcon;
export declare const NumberCircle628Filled: FluentIcon;
export declare const NumberCircle628Regular: FluentIcon;
export declare const NumberCircle632Filled: FluentIcon;
export declare const NumberCircle632Regular: FluentIcon;
export declare const NumberCircle648Filled: FluentIcon;
export declare const NumberCircle648Regular: FluentIcon;
export declare const NumberCircle716Filled: FluentIcon;
export declare const NumberCircle716Regular: FluentIcon;
export declare const NumberCircle720Filled: FluentIcon;
export declare const NumberCircle720Regular: FluentIcon;
export declare const NumberCircle724Filled: FluentIcon;
export declare const NumberCircle724Regular: FluentIcon;
export declare const NumberCircle728Filled: FluentIcon;
export declare const NumberCircle728Regular: FluentIcon;
export declare const NumberCircle732Filled: FluentIcon;
export declare const NumberCircle732Regular: FluentIcon;
export declare const NumberCircle748Filled: FluentIcon;
export declare const NumberCircle748Regular: FluentIcon;
export declare const NumberCircle816Filled: FluentIcon;
export declare const NumberCircle816Regular: FluentIcon;
export declare const NumberCircle820Filled: FluentIcon;
export declare const NumberCircle820Regular: FluentIcon;
export declare const NumberCircle824Filled: FluentIcon;
export declare const NumberCircle824Regular: FluentIcon;
export declare const NumberCircle828Filled: FluentIcon;
export declare const NumberCircle828Regular: FluentIcon;
export declare const NumberCircle832Filled: FluentIcon;
export declare const NumberCircle832Regular: FluentIcon;
export declare const NumberCircle848Filled: FluentIcon;
export declare const NumberCircle848Regular: FluentIcon;
export declare const NumberCircle916Filled: FluentIcon;
export declare const NumberCircle916Regular: FluentIcon;
export declare const NumberCircle920Filled: FluentIcon;
export declare const NumberCircle920Regular: FluentIcon;
export declare const NumberCircle924Filled: FluentIcon;
export declare const NumberCircle924Regular: FluentIcon;
export declare const NumberCircle928Filled: FluentIcon;
export declare const NumberCircle928Regular: FluentIcon;
export declare const NumberCircle932Filled: FluentIcon;
export declare const NumberCircle932Regular: FluentIcon;
export declare const NumberCircle948Filled: FluentIcon;
export declare const NumberCircle948Regular: FluentIcon;
export declare const NumberRow16Filled: FluentIcon;
export declare const NumberRow16Regular: FluentIcon;
export declare const NumberRow20Filled: FluentIcon;
export declare const NumberRow20Regular: FluentIcon;
export declare const NumberRow24Filled: FluentIcon;
export declare const NumberRow24Regular: FluentIcon;
export declare const NumberSymbol16Filled: FluentIcon;
export declare const NumberSymbol16Regular: FluentIcon;
export declare const NumberSymbol20Filled: FluentIcon;
export declare const NumberSymbol20Regular: FluentIcon;
export declare const NumberSymbol24Filled: FluentIcon;
export declare const NumberSymbol24Regular: FluentIcon;
export declare const NumberSymbol28Filled: FluentIcon;
export declare const NumberSymbol28Regular: FluentIcon;
export declare const NumberSymbol32Filled: FluentIcon;
export declare const NumberSymbol32Regular: FluentIcon;
export declare const NumberSymbol48Filled: FluentIcon;
export declare const NumberSymbol48Regular: FluentIcon;
export declare const NumberSymbolDismiss20Filled: FluentIcon;
export declare const NumberSymbolDismiss20Regular: FluentIcon;
export declare const NumberSymbolDismiss24Filled: FluentIcon;
export declare const NumberSymbolDismiss24Regular: FluentIcon;
export declare const NumberSymbolSquare20Color: FluentIcon;
export declare const NumberSymbolSquare20Filled: FluentIcon;
export declare const NumberSymbolSquare20Regular: FluentIcon;
export declare const NumberSymbolSquare24Color: FluentIcon;
export declare const NumberSymbolSquare24Filled: FluentIcon;
export declare const NumberSymbolSquare24Regular: FluentIcon;
export declare const NumberSymbolSquare32Color: FluentIcon;
export declare const NumberSymbolSquare32Filled: FluentIcon;
export declare const NumberSymbolSquare32Regular: FluentIcon;
export declare const Open12Filled: FluentIcon;
export declare const Open12Regular: FluentIcon;
export declare const Open16Filled: FluentIcon;
export declare const Open16Regular: FluentIcon;
export declare const Open20Filled: FluentIcon;
export declare const Open20Regular: FluentIcon;
export declare const Open24Filled: FluentIcon;
export declare const Open24Regular: FluentIcon;
export declare const Open28Filled: FluentIcon;
export declare const Open28Regular: FluentIcon;
export declare const Open32Filled: FluentIcon;
export declare const Open32Regular: FluentIcon;
export declare const Open48Filled: FluentIcon;
export declare const Open48Regular: FluentIcon;
export declare const OpenFolder16Filled: FluentIcon;
export declare const OpenFolder16Regular: FluentIcon;
export declare const OpenFolder20Filled: FluentIcon;
export declare const OpenFolder20Regular: FluentIcon;
export declare const OpenFolder24Filled: FluentIcon;
export declare const OpenFolder24Regular: FluentIcon;
export declare const OpenFolder28Filled: FluentIcon;
export declare const OpenFolder28Regular: FluentIcon;
export declare const OpenFolder48Filled: FluentIcon;
export declare const OpenFolder48Regular: FluentIcon;
export declare const OpenOff16Filled: FluentIcon;
export declare const OpenOff16Regular: FluentIcon;
export declare const OpenOff20Filled: FluentIcon;
export declare const OpenOff20Regular: FluentIcon;
export declare const OpenOff24Filled: FluentIcon;
export declare const OpenOff24Regular: FluentIcon;
export declare const OpenOff28Filled: FluentIcon;
export declare const OpenOff28Regular: FluentIcon;
export declare const OpenOff48Filled: FluentIcon;
export declare const OpenOff48Regular: FluentIcon;
export declare const Options16Color: FluentIcon;
export declare const Options16Filled: FluentIcon;
export declare const Options16Regular: FluentIcon;
export declare const Options20Color: FluentIcon;
export declare const Options20Filled: FluentIcon;
export declare const Options20Regular: FluentIcon;
export declare const Options24Color: FluentIcon;
export declare const Options24Filled: FluentIcon;
export declare const Options24Regular: FluentIcon;
export declare const Options28Color: FluentIcon;
export declare const Options28Filled: FluentIcon;
export declare const Options28Regular: FluentIcon;
export declare const Options32Color: FluentIcon;
export declare const Options32Filled: FluentIcon;
export declare const Options32Light: FluentIcon;
export declare const Options32Regular: FluentIcon;
export declare const Options48Color: FluentIcon;
export declare const Options48Filled: FluentIcon;
export declare const Options48Regular: FluentIcon;
export declare const Org16Color: FluentIcon;
export declare const Org20Color: FluentIcon;
export declare const Org24Color: FluentIcon;
export declare const Org28Color: FluentIcon;
export declare const Org32Color: FluentIcon;
export declare const Org48Color: FluentIcon;
export declare const Organization12Filled: FluentIcon;
export declare const Organization12Regular: FluentIcon;
export declare const Organization16Filled: FluentIcon;
export declare const Organization16Regular: FluentIcon;
export declare const Organization20Filled: FluentIcon;
export declare const Organization20Regular: FluentIcon;
export declare const Organization24Filled: FluentIcon;
export declare const Organization24Regular: FluentIcon;
export declare const Organization28Filled: FluentIcon;
export declare const Organization28Regular: FluentIcon;
export declare const Organization32Filled: FluentIcon;
export declare const Organization32Regular: FluentIcon;
export declare const Organization48Filled: FluentIcon;
export declare const Organization48Regular: FluentIcon;
export declare const OrganizationHorizontal16Filled: FluentIcon;
export declare const OrganizationHorizontal16Regular: FluentIcon;
export declare const OrganizationHorizontal20Filled: FluentIcon;
export declare const OrganizationHorizontal20Regular: FluentIcon;
export declare const OrganizationHorizontal24Filled: FluentIcon;
export declare const OrganizationHorizontal24Regular: FluentIcon;
export declare const Orientation20Filled: FluentIcon;
export declare const Orientation20Regular: FluentIcon;
export declare const Orientation24Filled: FluentIcon;
export declare const Orientation24Regular: FluentIcon;
export declare const Oval16Filled: FluentIcon;
export declare const Oval16Regular: FluentIcon;
export declare const Oval20Filled: FluentIcon;
export declare const Oval20Regular: FluentIcon;
export declare const Oval24Filled: FluentIcon;
export declare const Oval24Regular: FluentIcon;
export declare const Oval28Filled: FluentIcon;
export declare const Oval28Regular: FluentIcon;
export declare const Oval32Filled: FluentIcon;
export declare const Oval32Regular: FluentIcon;
export declare const Oval48Filled: FluentIcon;
export declare const Oval48Regular: FluentIcon;
export declare const Oven20Filled: FluentIcon;
export declare const Oven20Regular: FluentIcon;
export declare const Oven24Filled: FluentIcon;
export declare const Oven24Regular: FluentIcon;
export declare const Oven32Filled: FluentIcon;
export declare const Oven32Regular: FluentIcon;
export declare const Oven48Filled: FluentIcon;
export declare const Oven48Regular: FluentIcon;
export declare const PaddingDown20Filled: FluentIcon;
export declare const PaddingDown20Regular: FluentIcon;
export declare const PaddingDown24Filled: FluentIcon;
export declare const PaddingDown24Regular: FluentIcon;
export declare const PaddingLeft20Filled: FluentIcon;
export declare const PaddingLeft20Regular: FluentIcon;
export declare const PaddingLeft24Filled: FluentIcon;
export declare const PaddingLeft24Regular: FluentIcon;
export declare const PaddingRight20Filled: FluentIcon;
export declare const PaddingRight20Regular: FluentIcon;
export declare const PaddingRight24Filled: FluentIcon;
export declare const PaddingRight24Regular: FluentIcon;
export declare const PaddingTop20Filled: FluentIcon;
export declare const PaddingTop20Regular: FluentIcon;
export declare const PaddingTop24Filled: FluentIcon;
export declare const PaddingTop24Regular: FluentIcon;
export declare const PageFit16Filled: FluentIcon;
export declare const PageFit16Regular: FluentIcon;
export declare const PageFit20Filled: FluentIcon;
export declare const PageFit20Regular: FluentIcon;
export declare const PageFit24Filled: FluentIcon;
export declare const PageFit24Regular: FluentIcon;
export declare const PaintBrush12Filled: FluentIcon;
export declare const PaintBrush12Regular: FluentIcon;
export declare const PaintBrush16Color: FluentIcon;
export declare const PaintBrush16Filled: FluentIcon;
export declare const PaintBrush16Regular: FluentIcon;
export declare const PaintBrush20Color: FluentIcon;
export declare const PaintBrush20Filled: FluentIcon;
export declare const PaintBrush20Regular: FluentIcon;
export declare const PaintBrush24Color: FluentIcon;
export declare const PaintBrush24Filled: FluentIcon;
export declare const PaintBrush24Regular: FluentIcon;
export declare const PaintBrush28Color: FluentIcon;
export declare const PaintBrush28Filled: FluentIcon;
export declare const PaintBrush28Regular: FluentIcon;
export declare const PaintBrush32Color: FluentIcon;
export declare const PaintBrush32Filled: FluentIcon;
export declare const PaintBrush32Light: FluentIcon;
export declare const PaintBrush32Regular: FluentIcon;
export declare const PaintBrushArrowDown20Filled: FluentIcon;
export declare const PaintBrushArrowDown20Regular: FluentIcon;
export declare const PaintBrushArrowDown24Filled: FluentIcon;
export declare const PaintBrushArrowDown24Regular: FluentIcon;
export declare const PaintBrushArrowUp20Filled: FluentIcon;
export declare const PaintBrushArrowUp20Regular: FluentIcon;
export declare const PaintBrushArrowUp24Filled: FluentIcon;
export declare const PaintBrushArrowUp24Regular: FluentIcon;
export declare const PaintBrushSparkle20Filled: FluentIcon;
export declare const PaintBrushSparkle20Regular: FluentIcon;
export declare const PaintBrushSparkle24Filled: FluentIcon;
export declare const PaintBrushSparkle24Regular: FluentIcon;
export declare const PaintBrushSubtract16Filled: FluentIcon;
export declare const PaintBrushSubtract16Regular: FluentIcon;
export declare const PaintBrushSubtract20Filled: FluentIcon;
export declare const PaintBrushSubtract20Regular: FluentIcon;
export declare const PaintBrushSubtract24Filled: FluentIcon;
export declare const PaintBrushSubtract24Regular: FluentIcon;
export declare const PaintBrushSubtract28Filled: FluentIcon;
export declare const PaintBrushSubtract28Regular: FluentIcon;
export declare const PaintBrushSubtract32Filled: FluentIcon;
export declare const PaintBrushSubtract32Regular: FluentIcon;
export declare const PaintBucket16Filled: FluentIcon;
export declare const PaintBucket16Regular: FluentIcon;
export declare const PaintBucket20Filled: FluentIcon;
export declare const PaintBucket20Regular: FluentIcon;
export declare const PaintBucket24Filled: FluentIcon;
export declare const PaintBucket24Regular: FluentIcon;
export declare const PaintBucketBrush16Filled: FluentIcon;
export declare const PaintBucketBrush16Regular: FluentIcon;
export declare const PaintBucketBrush20Filled: FluentIcon;
export declare const PaintBucketBrush20Regular: FluentIcon;
export declare const PaintBucketBrush24Filled: FluentIcon;
export declare const PaintBucketBrush24Regular: FluentIcon;
export declare const PaintBucketBrush28Filled: FluentIcon;
export declare const PaintBucketBrush28Regular: FluentIcon;
export declare const Pair20Filled: FluentIcon;
export declare const Pair20Regular: FluentIcon;
export declare const Pair24Filled: FluentIcon;
export declare const Pair24Regular: FluentIcon;
export declare const PanelBottom20Filled: FluentIcon;
export declare const PanelBottom20Regular: FluentIcon;
export declare const PanelBottomContract20Filled: FluentIcon;
export declare const PanelBottomContract20Regular: FluentIcon;
export declare const PanelBottomExpand20Filled: FluentIcon;
export declare const PanelBottomExpand20Regular: FluentIcon;
export declare const PanelLeft16Filled: FluentIcon;
export declare const PanelLeft16Regular: FluentIcon;
export declare const PanelLeft20Filled: FluentIcon;
export declare const PanelLeft20Regular: FluentIcon;
export declare const PanelLeft24Filled: FluentIcon;
export declare const PanelLeft24Regular: FluentIcon;
export declare const PanelLeft28Filled: FluentIcon;
export declare const PanelLeft28Regular: FluentIcon;
export declare const PanelLeft32Filled: FluentIcon;
export declare const PanelLeft32Regular: FluentIcon;
export declare const PanelLeft48Filled: FluentIcon;
export declare const PanelLeft48Regular: FluentIcon;
export declare const PanelLeftAdd16Filled: FluentIcon;
export declare const PanelLeftAdd16Regular: FluentIcon;
export declare const PanelLeftAdd20Filled: FluentIcon;
export declare const PanelLeftAdd20Regular: FluentIcon;
export declare const PanelLeftAdd24Filled: FluentIcon;
export declare const PanelLeftAdd24Regular: FluentIcon;
export declare const PanelLeftAdd28Filled: FluentIcon;
export declare const PanelLeftAdd28Regular: FluentIcon;
export declare const PanelLeftAdd32Filled: FluentIcon;
export declare const PanelLeftAdd32Regular: FluentIcon;
export declare const PanelLeftAdd48Filled: FluentIcon;
export declare const PanelLeftAdd48Regular: FluentIcon;
export declare const PanelLeftContract16Filled: FluentIcon;
export declare const PanelLeftContract16Regular: FluentIcon;
export declare const PanelLeftContract20Filled: FluentIcon;
export declare const PanelLeftContract20Regular: FluentIcon;
export declare const PanelLeftContract24Filled: FluentIcon;
export declare const PanelLeftContract24Regular: FluentIcon;
export declare const PanelLeftContract28Filled: FluentIcon;
export declare const PanelLeftContract28Regular: FluentIcon;
export declare const PanelLeftDefault28Filled: FluentIcon;
export declare const PanelLeftDefault28Regular: FluentIcon;
export declare const PanelLeftDefault32Light: FluentIcon;
export declare const PanelLeftExpand16Filled: FluentIcon;
export declare const PanelLeftExpand16Regular: FluentIcon;
export declare const PanelLeftExpand20Filled: FluentIcon;
export declare const PanelLeftExpand20Regular: FluentIcon;
export declare const PanelLeftExpand24Filled: FluentIcon;
export declare const PanelLeftExpand24Regular: FluentIcon;
export declare const PanelLeftExpand28Filled: FluentIcon;
export declare const PanelLeftExpand28Regular: FluentIcon;
export declare const PanelLeftFocusRight16Filled: FluentIcon;
export declare const PanelLeftFocusRight20Filled: FluentIcon;
export declare const PanelLeftFocusRight24Filled: FluentIcon;
export declare const PanelLeftFocusRight28Filled: FluentIcon;
export declare const PanelLeftFocusRight32Light: FluentIcon;
export declare const PanelLeftHeader16Filled: FluentIcon;
export declare const PanelLeftHeader16Regular: FluentIcon;
export declare const PanelLeftHeader20Filled: FluentIcon;
export declare const PanelLeftHeader20Regular: FluentIcon;
export declare const PanelLeftHeader24Filled: FluentIcon;
export declare const PanelLeftHeader24Regular: FluentIcon;
export declare const PanelLeftHeader28Filled: FluentIcon;
export declare const PanelLeftHeader28Regular: FluentIcon;
export declare const PanelLeftHeader32Filled: FluentIcon;
export declare const PanelLeftHeader32Regular: FluentIcon;
export declare const PanelLeftHeader48Filled: FluentIcon;
export declare const PanelLeftHeader48Regular: FluentIcon;
export declare const PanelLeftHeaderAdd16Filled: FluentIcon;
export declare const PanelLeftHeaderAdd16Regular: FluentIcon;
export declare const PanelLeftHeaderAdd20Filled: FluentIcon;
export declare const PanelLeftHeaderAdd20Regular: FluentIcon;
export declare const PanelLeftHeaderAdd24Filled: FluentIcon;
export declare const PanelLeftHeaderAdd24Regular: FluentIcon;
export declare const PanelLeftHeaderAdd28Filled: FluentIcon;
export declare const PanelLeftHeaderAdd28Regular: FluentIcon;
export declare const PanelLeftHeaderAdd32Filled: FluentIcon;
export declare const PanelLeftHeaderAdd32Regular: FluentIcon;
export declare const PanelLeftHeaderAdd48Filled: FluentIcon;
export declare const PanelLeftHeaderAdd48Regular: FluentIcon;
export declare const PanelLeftHeaderKey16Filled: FluentIcon;
export declare const PanelLeftHeaderKey16Regular: FluentIcon;
export declare const PanelLeftHeaderKey20Filled: FluentIcon;
export declare const PanelLeftHeaderKey20Regular: FluentIcon;
export declare const PanelLeftHeaderKey24Filled: FluentIcon;
export declare const PanelLeftHeaderKey24Regular: FluentIcon;
export declare const PanelLeftKey16Filled: FluentIcon;
export declare const PanelLeftKey16Regular: FluentIcon;
export declare const PanelLeftKey20Filled: FluentIcon;
export declare const PanelLeftKey20Regular: FluentIcon;
export declare const PanelLeftKey24Filled: FluentIcon;
export declare const PanelLeftKey24Regular: FluentIcon;
export declare const PanelLeftText16Filled: FluentIcon;
export declare const PanelLeftText16Regular: FluentIcon;
export declare const PanelLeftText20Filled: FluentIcon;
export declare const PanelLeftText20Regular: FluentIcon;
export declare const PanelLeftText24Filled: FluentIcon;
export declare const PanelLeftText24Regular: FluentIcon;
export declare const PanelLeftText28Filled: FluentIcon;
export declare const PanelLeftText28Regular: FluentIcon;
export declare const PanelLeftText32Filled: FluentIcon;
export declare const PanelLeftText32Regular: FluentIcon;
export declare const PanelLeftText48Filled: FluentIcon;
export declare const PanelLeftText48Regular: FluentIcon;
export declare const PanelLeftTextAdd16Filled: FluentIcon;
export declare const PanelLeftTextAdd16Regular: FluentIcon;
export declare const PanelLeftTextAdd20Filled: FluentIcon;
export declare const PanelLeftTextAdd20Regular: FluentIcon;
export declare const PanelLeftTextAdd24Filled: FluentIcon;
export declare const PanelLeftTextAdd24Regular: FluentIcon;
export declare const PanelLeftTextAdd28Filled: FluentIcon;
export declare const PanelLeftTextAdd28Regular: FluentIcon;
export declare const PanelLeftTextAdd32Filled: FluentIcon;
export declare const PanelLeftTextAdd32Regular: FluentIcon;
export declare const PanelLeftTextAdd48Filled: FluentIcon;
export declare const PanelLeftTextAdd48Regular: FluentIcon;
export declare const PanelLeftTextDismiss16Filled: FluentIcon;
export declare const PanelLeftTextDismiss16Regular: FluentIcon;
export declare const PanelLeftTextDismiss20Filled: FluentIcon;
export declare const PanelLeftTextDismiss20Regular: FluentIcon;
export declare const PanelLeftTextDismiss24Filled: FluentIcon;
export declare const PanelLeftTextDismiss24Regular: FluentIcon;
export declare const PanelLeftTextDismiss28Filled: FluentIcon;
export declare const PanelLeftTextDismiss28Regular: FluentIcon;
export declare const PanelLeftTextDismiss32Filled: FluentIcon;
export declare const PanelLeftTextDismiss32Regular: FluentIcon;
export declare const PanelLeftTextDismiss48Filled: FluentIcon;
export declare const PanelLeftTextDismiss48Regular: FluentIcon;
export declare const PanelRight12Filled: FluentIcon;
export declare const PanelRight12Regular: FluentIcon;
export declare const PanelRight16Filled: FluentIcon;
export declare const PanelRight16Regular: FluentIcon;
export declare const PanelRight20Filled: FluentIcon;
export declare const PanelRight20Regular: FluentIcon;
export declare const PanelRight24Filled: FluentIcon;
export declare const PanelRight24Regular: FluentIcon;
export declare const PanelRight28Filled: FluentIcon;
export declare const PanelRight28Regular: FluentIcon;
export declare const PanelRight32Filled: FluentIcon;
export declare const PanelRight32Regular: FluentIcon;
export declare const PanelRight48Filled: FluentIcon;
export declare const PanelRight48Regular: FluentIcon;
export declare const PanelRightAdd20Filled: FluentIcon;
export declare const PanelRightAdd20Regular: FluentIcon;
export declare const PanelRightContract16Filled: FluentIcon;
export declare const PanelRightContract16Regular: FluentIcon;
export declare const PanelRightContract20Filled: FluentIcon;
export declare const PanelRightContract20Regular: FluentIcon;
export declare const PanelRightContract24Filled: FluentIcon;
export declare const PanelRightContract24Regular: FluentIcon;
export declare const PanelRightContract28Filled: FluentIcon;
export declare const PanelRightContract28Regular: FluentIcon;
export declare const PanelRightCursor20Filled: FluentIcon;
export declare const PanelRightCursor20Regular: FluentIcon;
export declare const PanelRightCursor24Filled: FluentIcon;
export declare const PanelRightCursor24Regular: FluentIcon;
export declare const PanelRightDefault28Filled: FluentIcon;
export declare const PanelRightDefault28Regular: FluentIcon;
export declare const PanelRightExpand20Filled: FluentIcon;
export declare const PanelRightExpand20Regular: FluentIcon;
export declare const PanelRightExpand28Filled: FluentIcon;
export declare const PanelRightExpand28Regular: FluentIcon;
export declare const PanelRightGallery16Filled: FluentIcon;
export declare const PanelRightGallery16Regular: FluentIcon;
export declare const PanelRightGallery20Filled: FluentIcon;
export declare const PanelRightGallery20Regular: FluentIcon;
export declare const PanelRightGallery24Filled: FluentIcon;
export declare const PanelRightGallery24Regular: FluentIcon;
export declare const PanelRightGallery28Filled: FluentIcon;
export declare const PanelRightGallery28Regular: FluentIcon;
export declare const PanelSeparateWindow20Filled: FluentIcon;
export declare const PanelSeparateWindow20Regular: FluentIcon;
export declare const PanelTopContract20Filled: FluentIcon;
export declare const PanelTopContract20Regular: FluentIcon;
export declare const PanelTopExpand20Filled: FluentIcon;
export declare const PanelTopExpand20Regular: FluentIcon;
export declare const PanelTopGallery16Filled: FluentIcon;
export declare const PanelTopGallery16Regular: FluentIcon;
export declare const PanelTopGallery20Filled: FluentIcon;
export declare const PanelTopGallery20Regular: FluentIcon;
export declare const PanelTopGallery24Filled: FluentIcon;
export declare const PanelTopGallery24Regular: FluentIcon;
export declare const PanelTopGallery28Filled: FluentIcon;
export declare const PanelTopGallery28Regular: FluentIcon;
export declare const Password16Filled: FluentIcon;
export declare const Password16Regular: FluentIcon;
export declare const Password20Filled: FluentIcon;
export declare const Password20Regular: FluentIcon;
export declare const Password24Filled: FluentIcon;
export declare const Password24Regular: FluentIcon;
export declare const Password32Filled: FluentIcon;
export declare const Password32Regular: FluentIcon;
export declare const Password48Filled: FluentIcon;
export declare const Password48Regular: FluentIcon;
export declare const PasswordClock16Filled: FluentIcon;
export declare const PasswordClock16Regular: FluentIcon;
export declare const PasswordClock20Filled: FluentIcon;
export declare const PasswordClock20Regular: FluentIcon;
export declare const PasswordClock24Filled: FluentIcon;
export declare const PasswordClock24Regular: FluentIcon;
export declare const PasswordClock48Filled: FluentIcon;
export declare const PasswordClock48Regular: FluentIcon;
export declare const PasswordReset48Filled: FluentIcon;
export declare const PasswordReset48Regular: FluentIcon;
export declare const Patch20Filled: FluentIcon;
export declare const Patch20Regular: FluentIcon;
export declare const Patch24Filled: FluentIcon;
export declare const Patch24Regular: FluentIcon;
export declare const Patient20Color: FluentIcon;
export declare const Patient20Filled: FluentIcon;
export declare const Patient20Regular: FluentIcon;
export declare const Patient24Color: FluentIcon;
export declare const Patient24Filled: FluentIcon;
export declare const Patient24Regular: FluentIcon;
export declare const Patient32Color: FluentIcon;
export declare const Patient32Filled: FluentIcon;
export declare const Patient32Regular: FluentIcon;
export declare const Pause12Filled: FluentIcon;
export declare const Pause12Regular: FluentIcon;
export declare const Pause16Filled: FluentIcon;
export declare const Pause16Regular: FluentIcon;
export declare const Pause20Filled: FluentIcon;
export declare const Pause20Regular: FluentIcon;
export declare const Pause24Filled: FluentIcon;
export declare const Pause24Regular: FluentIcon;
export declare const Pause28Filled: FluentIcon;
export declare const Pause28Regular: FluentIcon;
export declare const Pause32Filled: FluentIcon;
export declare const Pause32Regular: FluentIcon;
export declare const Pause48Filled: FluentIcon;
export declare const Pause48Regular: FluentIcon;
export declare const PauseCircle12Filled: FluentIcon;
export declare const PauseCircle12Regular: FluentIcon;
export declare const PauseCircle16Filled: FluentIcon;
export declare const PauseCircle16Regular: FluentIcon;
export declare const PauseCircle20Filled: FluentIcon;
export declare const PauseCircle20Regular: FluentIcon;
export declare const PauseCircle24Filled: FluentIcon;
export declare const PauseCircle24Regular: FluentIcon;
export declare const PauseCircle32Filled: FluentIcon;
export declare const PauseCircle32Regular: FluentIcon;
export declare const PauseCircle48Filled: FluentIcon;
export declare const PauseCircle48Regular: FluentIcon;
export declare const PauseOff16Filled: FluentIcon;
export declare const PauseOff16Regular: FluentIcon;
export declare const PauseOff20Filled: FluentIcon;
export declare const PauseOff20Regular: FluentIcon;
export declare const PauseSettings16Filled: FluentIcon;
export declare const PauseSettings16Regular: FluentIcon;
export declare const PauseSettings20Filled: FluentIcon;
export declare const PauseSettings20Regular: FluentIcon;
export declare const Paw16Color: FluentIcon;
export declare const Paw20Color: FluentIcon;
export declare const Paw24Color: FluentIcon;
export declare const Paw28Color: FluentIcon;
export declare const Paw32Color: FluentIcon;
export declare const Paw48Color: FluentIcon;
export declare const Payment16Filled: FluentIcon;
export declare const Payment16Regular: FluentIcon;
export declare const Payment20Filled: FluentIcon;
export declare const Payment20Regular: FluentIcon;
export declare const Payment24Filled: FluentIcon;
export declare const Payment24Regular: FluentIcon;
export declare const Payment28Filled: FluentIcon;
export declare const Payment28Regular: FluentIcon;
export declare const Payment32Filled: FluentIcon;
export declare const Payment32Regular: FluentIcon;
export declare const Payment48Filled: FluentIcon;
export declare const Payment48Regular: FluentIcon;
export declare const PaymentWireless16Filled: FluentIcon;
export declare const PaymentWireless16Regular: FluentIcon;
export declare const PaymentWireless20Filled: FluentIcon;
export declare const PaymentWireless20Regular: FluentIcon;
export declare const PaymentWireless24Filled: FluentIcon;
export declare const PaymentWireless24Regular: FluentIcon;
export declare const PaymentWireless28Filled: FluentIcon;
export declare const PaymentWireless28Regular: FluentIcon;
export declare const PaymentWireless32Filled: FluentIcon;
export declare const PaymentWireless32Regular: FluentIcon;
export declare const PaymentWireless48Filled: FluentIcon;
export declare const PaymentWireless48Regular: FluentIcon;
export declare const Pen16Filled: FluentIcon;
export declare const Pen16Regular: FluentIcon;
export declare const Pen20Filled: FluentIcon;
export declare const Pen20Regular: FluentIcon;
export declare const Pen24Filled: FluentIcon;
export declare const Pen24Regular: FluentIcon;
