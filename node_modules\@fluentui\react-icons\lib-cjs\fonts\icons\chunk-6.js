"use client";
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.NavigationBriefcaseFilled = exports.NavigationRegular = exports.NavigationFilled = exports.MyLocationRegular = exports.MyLocationFilled = exports.MusicNoteOff2Regular = exports.MusicNoteOff2Filled = exports.MusicNoteOff1Regular = exports.MusicNoteOff1Filled = exports.MusicNote2PlayRegular = exports.MusicNote2PlayFilled = exports.MusicNote2Regular = exports.MusicNote2Filled = exports.MusicNote1Regular = exports.MusicNote1Filled = exports.MultiselectRtlRegular = exports.MultiselectRtlFilled = exports.MultiselectLtrRegular = exports.MultiselectLtrFilled = exports.Multiplier5XRegular = exports.Multiplier5XFilled = exports.Multiplier2XRegular = exports.Multiplier2XFilled = exports.Multiplier1XRegular = exports.Multiplier1XFilled = exports.Multiplier18XRegular = exports.Multiplier18XFilled = exports.Multiplier15XRegular = exports.Multiplier15XFilled = exports.Multiplier12XRegular = exports.Multiplier12XFilled = exports.MoviesAndTvRegular = exports.MoviesAndTvFilled = exports.MountainTrailRegular = exports.MountainTrailFilled = exports.MountainLocationTopRegular = exports.MountainLocationTopFilled = exports.MountainLocationBottomRegular = exports.MountainLocationBottomFilled = exports.MoreVerticalRegular = exports.MoreVerticalFilled = exports.MoreHorizontalRegular = exports.MoreHorizontalFilled = exports.MoreCircleRegular = exports.MoreCircleFilled = exports.MoneySettingsRegular = exports.MoneySettingsFilled = exports.MoneyOffRegular = exports.MoneyOffFilled = exports.MoneyHandRegular = void 0;
exports.NotepadFilled = exports.NotebookSyncRegular = exports.NotebookSyncFilled = exports.NotebookSubsectionRegular = exports.NotebookSubsectionFilled = exports.NotebookSectionArrowRightRegular = exports.NotebookSectionArrowRightFilled = exports.NotebookSectionRegular = exports.NotebookSectionFilled = exports.NotebookQuestionMarkRegular = exports.NotebookQuestionMarkFilled = exports.NotebookLightningRegular = exports.NotebookLightningFilled = exports.NotebookEyeRegular = exports.NotebookEyeFilled = exports.NotebookErrorRegular = exports.NotebookErrorFilled = exports.NotebookArrowCurveDownRegular = exports.NotebookArrowCurveDownFilled = exports.NotebookAddRegular = exports.NotebookAddFilled = exports.NotebookRegular = exports.NotebookFilled = exports.NotePinRegular = exports.NotePinFilled = exports.NoteEditRegular = exports.NoteEditFilled = exports.NoteAddRegular = exports.NoteAddFilled = exports.NoteRegular = exports.NoteFilled = exports.NextFrameRegular = exports.NextFrameFilled = exports.NextRegular = exports.NextFilled = exports.NewsRegular = exports.NewsFilled = exports.NewRegular = exports.NewFilled = exports.NetworkCheckRegular = exports.NetworkCheckFilled = exports.NavigationUnreadRegular = exports.NavigationUnreadFilled = exports.NavigationPlayRegular = exports.NavigationPlayFilled = exports.NavigationPersonRegular = exports.NavigationPersonFilled = exports.NavigationLocationTargetRegular = exports.NavigationLocationTargetFilled = exports.NavigationBriefcaseRegular = void 0;
exports.OrientationFilled = exports.OrganizationHorizontalRegular = exports.OrganizationHorizontalFilled = exports.OrganizationRegular = exports.OrganizationFilled = exports.OptionsRegular = exports.OptionsFilled = exports.OpenOffRegular = exports.OpenOffFilled = exports.OpenFolderRegular = exports.OpenFolderFilled = exports.OpenRegular = exports.OpenFilled = exports.NumberSymbolSquareRegular = exports.NumberSymbolSquareFilled = exports.NumberSymbolDismissRegular = exports.NumberSymbolDismissFilled = exports.NumberSymbolRegular = exports.NumberSymbolFilled = exports.NumberRowRegular = exports.NumberRowFilled = exports.NumberCircle9Regular = exports.NumberCircle9Filled = exports.NumberCircle8Regular = exports.NumberCircle8Filled = exports.NumberCircle7Regular = exports.NumberCircle7Filled = exports.NumberCircle6Regular = exports.NumberCircle6Filled = exports.NumberCircle5Regular = exports.NumberCircle5Filled = exports.NumberCircle4Regular = exports.NumberCircle4Filled = exports.NumberCircle3Regular = exports.NumberCircle3Filled = exports.NumberCircle2Regular = exports.NumberCircle2Filled = exports.NumberCircle1Regular = exports.NumberCircle1Filled = exports.NumberCircle0Regular = exports.NumberCircle0Filled = exports.NotepadSparkleRegular = exports.NotepadSparkleFilled = exports.NotepadPersonOffRegular = exports.NotepadPersonOffFilled = exports.NotepadPersonRegular = exports.NotepadPersonFilled = exports.NotepadEditRegular = exports.NotepadEditFilled = exports.NotepadRegular = void 0;
exports.PanelLeftHeaderAddRegular = exports.PanelLeftHeaderAddFilled = exports.PanelLeftHeaderRegular = exports.PanelLeftHeaderFilled = exports.PanelLeftFocusRightFilled = exports.PanelLeftExpandRegular = exports.PanelLeftExpandFilled = exports.PanelLeftContractRegular = exports.PanelLeftContractFilled = exports.PanelLeftAddRegular = exports.PanelLeftAddFilled = exports.PanelLeftRegular = exports.PanelLeftFilled = exports.PanelBottomExpandRegular = exports.PanelBottomExpandFilled = exports.PanelBottomContractRegular = exports.PanelBottomContractFilled = exports.PanelBottomRegular = exports.PanelBottomFilled = exports.PairRegular = exports.PairFilled = exports.PaintBucketBrushRegular = exports.PaintBucketBrushFilled = exports.PaintBucketRegular = exports.PaintBucketFilled = exports.PaintBrushSubtractRegular = exports.PaintBrushSubtractFilled = exports.PaintBrushSparkleRegular = exports.PaintBrushSparkleFilled = exports.PaintBrushArrowUpRegular = exports.PaintBrushArrowUpFilled = exports.PaintBrushArrowDownRegular = exports.PaintBrushArrowDownFilled = exports.PaintBrushRegular = exports.PaintBrushFilled = exports.PageFitRegular = exports.PageFitFilled = exports.PaddingTopRegular = exports.PaddingTopFilled = exports.PaddingRightRegular = exports.PaddingRightFilled = exports.PaddingLeftRegular = exports.PaddingLeftFilled = exports.PaddingDownRegular = exports.PaddingDownFilled = exports.OvenRegular = exports.OvenFilled = exports.OvalRegular = exports.OvalFilled = exports.OrientationRegular = void 0;
exports.PaymentWirelessRegular = exports.PaymentWirelessFilled = exports.PaymentRegular = exports.PaymentFilled = exports.PauseSettingsRegular = exports.PauseSettingsFilled = exports.PauseOffRegular = exports.PauseOffFilled = exports.PauseCircleRegular = exports.PauseCircleFilled = exports.PauseRegular = exports.PauseFilled = exports.PatientRegular = exports.PatientFilled = exports.PatchRegular = exports.PatchFilled = exports.PasswordClockRegular = exports.PasswordClockFilled = exports.PasswordRegular = exports.PasswordFilled = exports.PanelTopGalleryRegular = exports.PanelTopGalleryFilled = exports.PanelTopExpandRegular = exports.PanelTopExpandFilled = exports.PanelTopContractRegular = exports.PanelTopContractFilled = exports.PanelSeparateWindowRegular = exports.PanelSeparateWindowFilled = exports.PanelRightGalleryRegular = exports.PanelRightGalleryFilled = exports.PanelRightExpandRegular = exports.PanelRightExpandFilled = exports.PanelRightCursorRegular = exports.PanelRightCursorFilled = exports.PanelRightContractRegular = exports.PanelRightContractFilled = exports.PanelRightAddRegular = exports.PanelRightAddFilled = exports.PanelRightRegular = exports.PanelRightFilled = exports.PanelLeftTextDismissRegular = exports.PanelLeftTextDismissFilled = exports.PanelLeftTextAddRegular = exports.PanelLeftTextAddFilled = exports.PanelLeftTextRegular = exports.PanelLeftTextFilled = exports.PanelLeftKeyRegular = exports.PanelLeftKeyFilled = exports.PanelLeftHeaderKeyRegular = exports.PanelLeftHeaderKeyFilled = void 0;
exports.PeopleProhibitedRegular = exports.PeopleProhibitedFilled = exports.PeopleMoneyRegular = exports.PeopleMoneyFilled = exports.PeopleLockRegular = exports.PeopleLockFilled = exports.PeopleListRegular = exports.PeopleListFilled = exports.PeopleLinkRegular = exports.PeopleLinkFilled = exports.PeopleInterwovenRegular = exports.PeopleInterwovenFilled = exports.PeopleEyeRegular = exports.PeopleEyeFilled = exports.PeopleErrorRegular = exports.PeopleErrorFilled = exports.PeopleEditRegular = exports.PeopleEditFilled = exports.PeopleCommunityAddRegular = exports.PeopleCommunityAddFilled = exports.PeopleCommunityRegular = exports.PeopleCommunityFilled = exports.PeopleCommunicationRegular = exports.PeopleCommunicationFilled = exports.PeopleCheckmarkRegular = exports.PeopleCheckmarkFilled = exports.PeopleChatRegular = exports.PeopleChatFilled = exports.PeopleCallRegular = exports.PeopleCallFilled = exports.PeopleAudienceRegular = exports.PeopleAudienceFilled = exports.PeopleAddRegular = exports.PeopleAddFilled = exports.PeopleRegular = exports.PeopleFilled = exports.PentagonRegular = exports.PentagonFilled = exports.PenSyncRegular = exports.PenSyncFilled = exports.PenSparkleRegular = exports.PenSparkleFilled = exports.PenProhibitedRegular = exports.PenProhibitedFilled = exports.PenOffRegular = exports.PenOffFilled = exports.PenDismissRegular = exports.PenDismissFilled = exports.PenRegular = exports.PenFilled = void 0;
exports.PersonBoardAddRegular = exports.PersonBoardAddFilled = exports.PersonBoardRegular = exports.PersonBoardFilled = exports.PersonAvailableRegular = exports.PersonAvailableFilled = exports.PersonArrowRightRegular = exports.PersonArrowRightFilled = exports.PersonArrowLeftRegular = exports.PersonArrowLeftFilled = exports.PersonArrowBackRegular = exports.PersonArrowBackFilled = exports.PersonAlertOffRegular = exports.PersonAlertOffFilled = exports.PersonAlertRegular = exports.PersonAlertFilled = exports.PersonAddRegular = exports.PersonAddFilled = exports.PersonAccountsRegular = exports.PersonAccountsFilled = exports.Person6Regular = exports.Person6Filled = exports.Person5Regular = exports.Person5Filled = exports.PersonRegular = exports.PersonFilled = exports.PeopleToolboxRegular = exports.PeopleToolboxFilled = exports.PeopleTeamToolboxRegular = exports.PeopleTeamToolboxFilled = exports.PeopleTeamDeleteRegular = exports.PeopleTeamDeleteFilled = exports.PeopleTeamAddRegular = exports.PeopleTeamAddFilled = exports.PeopleTeamRegular = exports.PeopleTeamFilled = exports.PeopleSyncRegular = exports.PeopleSyncFilled = exports.PeopleSwapRegular = exports.PeopleSwapFilled = exports.PeopleSubtractRegular = exports.PeopleSubtractFilled = exports.PeopleStarRegular = exports.PeopleStarFilled = exports.PeopleSettingsRegular = exports.PeopleSettingsFilled = exports.PeopleSearchRegular = exports.PeopleSearchFilled = exports.PeopleQueueRegular = exports.PeopleQueueFilled = void 0;
exports.PersonPillRegular = exports.PersonPillFilled = exports.PersonPasskeyRegular = exports.PersonPasskeyFilled = exports.PersonNoteRegular = exports.PersonNoteFilled = exports.PersonMoneyRegular = exports.PersonMoneyFilled = exports.PersonMailRegular = exports.PersonMailFilled = exports.PersonLockRegular = exports.PersonLockFilled = exports.PersonLinkRegular = exports.PersonLinkFilled = exports.PersonLightningRegular = exports.PersonLightningFilled = exports.PersonLightbulbRegular = exports.PersonLightbulbFilled = exports.PersonKeyRegular = exports.PersonKeyFilled = exports.PersonInfoRegular = exports.PersonInfoFilled = exports.PersonHomeRegular = exports.PersonHomeFilled = exports.PersonHeartRegular = exports.PersonHeartFilled = exports.PersonHeadHintRegular = exports.PersonHeadHintFilled = exports.PersonGuestRegular = exports.PersonGuestFilled = exports.PersonFeedbackRegular = exports.PersonFeedbackFilled = exports.PersonErrorRegular = exports.PersonErrorFilled = exports.PersonEditRegular = exports.PersonEditFilled = exports.PersonDesktopRegular = exports.PersonDesktopFilled = exports.PersonDeleteRegular = exports.PersonDeleteFilled = exports.PersonClockRegular = exports.PersonClockFilled = exports.PersonCircleRegular = exports.PersonCircleFilled = exports.PersonChatRegular = exports.PersonChatFilled = exports.PersonCallRegular = exports.PersonCallFilled = exports.PersonBriefcaseRegular = exports.PersonBriefcaseFilled = void 0;
exports.PhoneAddRegular = exports.PhoneAddFilled = exports.PhoneRegular = exports.PhoneFilled = exports.PersonWrenchRegular = exports.PersonWrenchFilled = exports.PersonWarningRegular = exports.PersonWarningFilled = exports.PersonWalkingRegular = exports.PersonWalkingFilled = exports.PersonVoiceRegular = exports.PersonVoiceFilled = exports.PersonTentativeRegular = exports.PersonTentativeFilled = exports.PersonTagRegular = exports.PersonTagFilled = exports.PersonSyncRegular = exports.PersonSyncFilled = exports.PersonSwapRegular = exports.PersonSwapFilled = exports.PersonSupportRegular = exports.PersonSupportFilled = exports.PersonSubtractRegular = exports.PersonSubtractFilled = exports.PersonStarburstRegular = exports.PersonStarburstFilled = exports.PersonStarRegular = exports.PersonStarFilled = exports.PersonSquareCheckmarkRegular = exports.PersonSquareCheckmarkFilled = exports.PersonSquareAddRegular = exports.PersonSquareAddFilled = exports.PersonSquareRegular = exports.PersonSquareFilled = exports.PersonSoundSpatialRegular = exports.PersonSoundSpatialFilled = exports.PersonShieldRegular = exports.PersonShieldFilled = exports.PersonSettingsRegular = exports.PersonSettingsFilled = exports.PersonSearchRegular = exports.PersonSearchFilled = exports.PersonRunningRegular = exports.PersonRunningFilled = exports.PersonRibbonRegular = exports.PersonRibbonFilled = exports.PersonQuestionMarkRegular = exports.PersonQuestionMarkFilled = exports.PersonProhibitedRegular = exports.PersonProhibitedFilled = void 0;
exports.PhoneUpdateCheckmarkRegular = exports.PhoneUpdateCheckmarkFilled = exports.PhoneUpdateRegular = exports.PhoneUpdateFilled = exports.PhoneTabletRegular = exports.PhoneTabletFilled = exports.PhoneStatusBarRegular = exports.PhoneStatusBarFilled = exports.PhoneSpeakerRegular = exports.PhoneSpeakerFilled = exports.PhoneSpanOutRegular = exports.PhoneSpanOutFilled = exports.PhoneSpanInRegular = exports.PhoneSpanInFilled = exports.PhoneShakeRegular = exports.PhoneShakeFilled = exports.PhoneScreenTimeRegular = exports.PhoneScreenTimeFilled = exports.PhonePaginationRegular = exports.PhonePaginationFilled = exports.PhonePageHeaderRegular = exports.PhonePageHeaderFilled = exports.PhoneLockRegular = exports.PhoneLockFilled = exports.PhoneLinkSetupRegular = exports.PhoneLinkSetupFilled = exports.PhoneLaptopRegular = exports.PhoneLaptopFilled = exports.PhoneKeyRegular = exports.PhoneKeyFilled = exports.PhoneHeaderArrowUpRegular = exports.PhoneHeaderArrowUpFilled = exports.PhoneFooterArrowDownRegular = exports.PhoneFooterArrowDownFilled = exports.PhoneEraserRegular = exports.PhoneEraserFilled = exports.PhoneEditRegular = exports.PhoneEditFilled = exports.PhoneDismissRegular = exports.PhoneDismissFilled = exports.PhoneDesktopAddRegular = exports.PhoneDesktopAddFilled = exports.PhoneDesktopRegular = exports.PhoneDesktopFilled = exports.PhoneCheckmarkRegular = exports.PhoneCheckmarkFilled = exports.PhoneChatRegular = exports.PhoneChatFilled = exports.PhoneArrowRightRegular = exports.PhoneArrowRightFilled = void 0;
const createFluentFontIcon_1 = require("../../utils/fonts/createFluentFontIcon");
exports.MoneyHandRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoneyHandRegular", "", 2, undefined));
exports.MoneyOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoneyOffFilled", "", 2, undefined));
exports.MoneyOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoneyOffRegular", "", 2, undefined));
exports.MoneySettingsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoneySettingsFilled", "", 2, undefined));
exports.MoneySettingsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoneySettingsRegular", "", 2, undefined));
exports.MoreCircleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoreCircleFilled", "", 2, undefined));
exports.MoreCircleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoreCircleRegular", "", 2, undefined));
exports.MoreHorizontalFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoreHorizontalFilled", "", 2, undefined));
exports.MoreHorizontalRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoreHorizontalRegular", "", 2, undefined));
exports.MoreVerticalFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoreVerticalFilled", "", 2, undefined));
exports.MoreVerticalRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoreVerticalRegular", "", 2, undefined));
exports.MountainLocationBottomFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MountainLocationBottomFilled", "", 2, undefined));
exports.MountainLocationBottomRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MountainLocationBottomRegular", "", 2, undefined));
exports.MountainLocationTopFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MountainLocationTopFilled", "", 2, undefined));
exports.MountainLocationTopRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MountainLocationTopRegular", "", 2, undefined));
exports.MountainTrailFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MountainTrailFilled", "", 2, undefined));
exports.MountainTrailRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MountainTrailRegular", "", 2, undefined));
exports.MoviesAndTvFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoviesAndTvFilled", "", 2, undefined));
exports.MoviesAndTvRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoviesAndTvRegular", "", 2, undefined));
exports.Multiplier12XFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Multiplier12XFilled", "", 2, undefined));
exports.Multiplier12XRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Multiplier12XRegular", "", 2, undefined));
exports.Multiplier15XFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Multiplier15XFilled", "", 2, undefined));
exports.Multiplier15XRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Multiplier15XRegular", "", 2, undefined));
exports.Multiplier18XFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Multiplier18XFilled", "", 2, undefined));
exports.Multiplier18XRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Multiplier18XRegular", "", 2, undefined));
exports.Multiplier1XFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Multiplier1XFilled", "", 2, undefined));
exports.Multiplier1XRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Multiplier1XRegular", "", 2, undefined));
exports.Multiplier2XFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Multiplier2XFilled", "", 2, undefined));
exports.Multiplier2XRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Multiplier2XRegular", "", 2, undefined));
exports.Multiplier5XFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Multiplier5XFilled", "", 2, undefined));
exports.Multiplier5XRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Multiplier5XRegular", "", 2, undefined));
exports.MultiselectLtrFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MultiselectLtrFilled", "", 2, undefined));
exports.MultiselectLtrRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MultiselectLtrRegular", "", 2, undefined));
exports.MultiselectRtlFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MultiselectRtlFilled", "", 2, undefined));
exports.MultiselectRtlRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MultiselectRtlRegular", "", 2, undefined));
exports.MusicNote1Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MusicNote1Filled", "", 2, undefined));
exports.MusicNote1Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MusicNote1Regular", "", 2, undefined));
exports.MusicNote2Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MusicNote2Filled", "", 2, undefined));
exports.MusicNote2Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MusicNote2Regular", "", 2, undefined));
exports.MusicNote2PlayFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MusicNote2PlayFilled", "", 2, undefined));
exports.MusicNote2PlayRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MusicNote2PlayRegular", "", 2, undefined));
exports.MusicNoteOff1Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MusicNoteOff1Filled", "", 2, undefined));
exports.MusicNoteOff1Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MusicNoteOff1Regular", "", 2, undefined));
exports.MusicNoteOff2Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MusicNoteOff2Filled", "", 2, undefined));
exports.MusicNoteOff2Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MusicNoteOff2Regular", "", 2, undefined));
exports.MyLocationFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MyLocationFilled", "", 2, undefined));
exports.MyLocationRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MyLocationRegular", "", 2, undefined));
exports.NavigationFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NavigationFilled", "", 2, undefined));
exports.NavigationRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NavigationRegular", "", 2, undefined));
exports.NavigationBriefcaseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NavigationBriefcaseFilled", "", 2, undefined));
exports.NavigationBriefcaseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NavigationBriefcaseRegular", "", 2, undefined));
exports.NavigationLocationTargetFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NavigationLocationTargetFilled", "", 2, undefined));
exports.NavigationLocationTargetRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NavigationLocationTargetRegular", "", 2, undefined));
exports.NavigationPersonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NavigationPersonFilled", "", 2, undefined));
exports.NavigationPersonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NavigationPersonRegular", "", 2, undefined));
exports.NavigationPlayFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NavigationPlayFilled", "", 2, undefined));
exports.NavigationPlayRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NavigationPlayRegular", "", 2, undefined));
exports.NavigationUnreadFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NavigationUnreadFilled", "", 2, undefined));
exports.NavigationUnreadRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NavigationUnreadRegular", "", 2, undefined));
exports.NetworkCheckFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NetworkCheckFilled", "", 2, undefined));
exports.NetworkCheckRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NetworkCheckRegular", "", 2, undefined));
exports.NewFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NewFilled", "", 2, undefined));
exports.NewRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NewRegular", "", 2, undefined));
exports.NewsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NewsFilled", "", 2, undefined));
exports.NewsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NewsRegular", "", 2, undefined));
exports.NextFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NextFilled", "", 2, undefined));
exports.NextRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NextRegular", "", 2, undefined));
exports.NextFrameFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NextFrameFilled", "", 2, undefined));
exports.NextFrameRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NextFrameRegular", "", 2, undefined));
exports.NoteFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NoteFilled", "", 2, undefined));
exports.NoteRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NoteRegular", "", 2, undefined));
exports.NoteAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NoteAddFilled", "", 2, undefined));
exports.NoteAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NoteAddRegular", "", 2, undefined));
exports.NoteEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NoteEditFilled", "", 2, undefined));
exports.NoteEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NoteEditRegular", "", 2, undefined));
exports.NotePinFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotePinFilled", "", 2, undefined));
exports.NotePinRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotePinRegular", "", 2, undefined));
exports.NotebookFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookFilled", "", 2, undefined));
exports.NotebookRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookRegular", "", 2, undefined));
exports.NotebookAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookAddFilled", "", 2, undefined));
exports.NotebookAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookAddRegular", "", 2, undefined));
exports.NotebookArrowCurveDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookArrowCurveDownFilled", "", 2, undefined));
exports.NotebookArrowCurveDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookArrowCurveDownRegular", "", 2, undefined));
exports.NotebookErrorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookErrorFilled", "", 2, undefined));
exports.NotebookErrorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookErrorRegular", "", 2, undefined));
exports.NotebookEyeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookEyeFilled", "", 2, undefined));
exports.NotebookEyeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookEyeRegular", "", 2, undefined));
exports.NotebookLightningFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookLightningFilled", "", 2, undefined));
exports.NotebookLightningRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookLightningRegular", "", 2, undefined));
exports.NotebookQuestionMarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookQuestionMarkFilled", "", 2, undefined));
exports.NotebookQuestionMarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookQuestionMarkRegular", "", 2, undefined));
exports.NotebookSectionFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookSectionFilled", "", 2, undefined));
exports.NotebookSectionRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookSectionRegular", "", 2, undefined));
exports.NotebookSectionArrowRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookSectionArrowRightFilled", "", 2, undefined));
exports.NotebookSectionArrowRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookSectionArrowRightRegular", "", 2, undefined));
exports.NotebookSubsectionFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookSubsectionFilled", "", 2, undefined));
exports.NotebookSubsectionRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookSubsectionRegular", "", 2, undefined));
exports.NotebookSyncFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookSyncFilled", "", 2, undefined));
exports.NotebookSyncRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotebookSyncRegular", "", 2, undefined));
exports.NotepadFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotepadFilled", "", 2, undefined));
exports.NotepadRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotepadRegular", "", 2, undefined));
exports.NotepadEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotepadEditFilled", "", 2, undefined));
exports.NotepadEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotepadEditRegular", "", 2, undefined));
exports.NotepadPersonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotepadPersonFilled", "", 2, undefined));
exports.NotepadPersonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotepadPersonRegular", "", 2, undefined));
exports.NotepadPersonOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotepadPersonOffFilled", "", 2, undefined));
exports.NotepadPersonOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotepadPersonOffRegular", "", 2, undefined));
exports.NotepadSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotepadSparkleFilled", "", 2, undefined));
exports.NotepadSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NotepadSparkleRegular", "", 2, undefined));
exports.NumberCircle0Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberCircle0Filled", "", 2, undefined));
exports.NumberCircle0Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberCircle0Regular", "", 2, undefined));
exports.NumberCircle1Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberCircle1Filled", "", 2, undefined));
exports.NumberCircle1Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberCircle1Regular", "", 2, undefined));
exports.NumberCircle2Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberCircle2Filled", "", 2, undefined));
exports.NumberCircle2Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberCircle2Regular", "", 2, undefined));
exports.NumberCircle3Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberCircle3Filled", "", 2, undefined));
exports.NumberCircle3Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberCircle3Regular", "", 2, undefined));
exports.NumberCircle4Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberCircle4Filled", "", 2, undefined));
exports.NumberCircle4Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberCircle4Regular", "", 2, undefined));
exports.NumberCircle5Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberCircle5Filled", "", 2, undefined));
exports.NumberCircle5Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberCircle5Regular", "", 2, undefined));
exports.NumberCircle6Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberCircle6Filled", "", 2, undefined));
exports.NumberCircle6Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberCircle6Regular", "", 2, undefined));
exports.NumberCircle7Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberCircle7Filled", "", 2, undefined));
exports.NumberCircle7Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberCircle7Regular", "", 2, undefined));
exports.NumberCircle8Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberCircle8Filled", "", 2, undefined));
exports.NumberCircle8Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberCircle8Regular", "", 2, undefined));
exports.NumberCircle9Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberCircle9Filled", "", 2, undefined));
exports.NumberCircle9Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberCircle9Regular", "", 2, undefined));
exports.NumberRowFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberRowFilled", "", 2, undefined));
exports.NumberRowRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberRowRegular", "", 2, undefined));
exports.NumberSymbolFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberSymbolFilled", "", 2, undefined));
exports.NumberSymbolRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberSymbolRegular", "", 2, undefined));
exports.NumberSymbolDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberSymbolDismissFilled", "", 2, undefined));
exports.NumberSymbolDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberSymbolDismissRegular", "", 2, undefined));
exports.NumberSymbolSquareFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberSymbolSquareFilled", "", 2, undefined));
exports.NumberSymbolSquareRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("NumberSymbolSquareRegular", "", 2, undefined));
exports.OpenFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("OpenFilled", "", 2, undefined));
exports.OpenRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("OpenRegular", "", 2, undefined));
exports.OpenFolderFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("OpenFolderFilled", "", 2, undefined));
exports.OpenFolderRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("OpenFolderRegular", "", 2, undefined));
exports.OpenOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("OpenOffFilled", "", 2, undefined));
exports.OpenOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("OpenOffRegular", "", 2, undefined));
exports.OptionsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("OptionsFilled", "", 2, undefined));
exports.OptionsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("OptionsRegular", "", 2, undefined));
exports.OrganizationFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("OrganizationFilled", "", 2, undefined));
exports.OrganizationRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("OrganizationRegular", "", 2, undefined));
exports.OrganizationHorizontalFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("OrganizationHorizontalFilled", "", 2, undefined));
exports.OrganizationHorizontalRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("OrganizationHorizontalRegular", "", 2, undefined));
exports.OrientationFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("OrientationFilled", "", 2, undefined));
exports.OrientationRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("OrientationRegular", "", 2, undefined));
exports.OvalFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("OvalFilled", "", 2, undefined));
exports.OvalRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("OvalRegular", "", 2, undefined));
exports.OvenFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("OvenFilled", "", 2, undefined));
exports.OvenRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("OvenRegular", "", 2, undefined));
exports.PaddingDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaddingDownFilled", "", 2, undefined));
exports.PaddingDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaddingDownRegular", "", 2, undefined));
exports.PaddingLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaddingLeftFilled", "", 2, undefined));
exports.PaddingLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaddingLeftRegular", "", 2, undefined));
exports.PaddingRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaddingRightFilled", "", 2, undefined));
exports.PaddingRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaddingRightRegular", "", 2, undefined));
exports.PaddingTopFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaddingTopFilled", "", 2, undefined));
exports.PaddingTopRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaddingTopRegular", "", 2, undefined));
exports.PageFitFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PageFitFilled", "", 2, undefined));
exports.PageFitRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PageFitRegular", "", 2, undefined));
exports.PaintBrushFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaintBrushFilled", "", 2, undefined));
exports.PaintBrushRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaintBrushRegular", "", 2, undefined));
exports.PaintBrushArrowDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaintBrushArrowDownFilled", "", 2, undefined));
exports.PaintBrushArrowDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaintBrushArrowDownRegular", "", 2, undefined));
exports.PaintBrushArrowUpFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaintBrushArrowUpFilled", "", 2, undefined));
exports.PaintBrushArrowUpRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaintBrushArrowUpRegular", "", 2, undefined));
exports.PaintBrushSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaintBrushSparkleFilled", "", 2, undefined));
exports.PaintBrushSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaintBrushSparkleRegular", "", 2, undefined));
exports.PaintBrushSubtractFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaintBrushSubtractFilled", "", 2, undefined));
exports.PaintBrushSubtractRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaintBrushSubtractRegular", "", 2, undefined));
exports.PaintBucketFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaintBucketFilled", "", 2, undefined));
exports.PaintBucketRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaintBucketRegular", "", 2, undefined));
exports.PaintBucketBrushFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaintBucketBrushFilled", "", 2, undefined));
exports.PaintBucketBrushRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaintBucketBrushRegular", "", 2, undefined));
exports.PairFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PairFilled", "", 2, undefined));
exports.PairRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PairRegular", "", 2, undefined));
exports.PanelBottomFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelBottomFilled", "", 2, undefined));
exports.PanelBottomRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelBottomRegular", "", 2, undefined));
exports.PanelBottomContractFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelBottomContractFilled", "", 2, undefined));
exports.PanelBottomContractRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelBottomContractRegular", "", 2, undefined));
exports.PanelBottomExpandFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelBottomExpandFilled", "", 2, undefined));
exports.PanelBottomExpandRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelBottomExpandRegular", "", 2, undefined));
exports.PanelLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftFilled", "", 2, undefined));
exports.PanelLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftRegular", "", 2, undefined));
exports.PanelLeftAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftAddFilled", "", 2, undefined, { flipInRtl: true }));
exports.PanelLeftAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftAddRegular", "", 2, undefined, { flipInRtl: true }));
exports.PanelLeftContractFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftContractFilled", "", 2, undefined));
exports.PanelLeftContractRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftContractRegular", "", 2, undefined));
exports.PanelLeftExpandFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftExpandFilled", "", 2, undefined));
exports.PanelLeftExpandRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftExpandRegular", "", 2, undefined));
exports.PanelLeftFocusRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftFocusRightFilled", "", 2, undefined));
exports.PanelLeftHeaderFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftHeaderFilled", "", 2, undefined, { flipInRtl: true }));
exports.PanelLeftHeaderRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftHeaderRegular", "", 2, undefined, { flipInRtl: true }));
exports.PanelLeftHeaderAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftHeaderAddFilled", "", 2, undefined, { flipInRtl: true }));
exports.PanelLeftHeaderAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftHeaderAddRegular", "", 2, undefined, { flipInRtl: true }));
exports.PanelLeftHeaderKeyFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftHeaderKeyFilled", "", 2, undefined, { flipInRtl: true }));
exports.PanelLeftHeaderKeyRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftHeaderKeyRegular", "", 2, undefined, { flipInRtl: true }));
exports.PanelLeftKeyFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftKeyFilled", "", 2, undefined, { flipInRtl: true }));
exports.PanelLeftKeyRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftKeyRegular", "", 2, undefined, { flipInRtl: true }));
exports.PanelLeftTextFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftTextFilled", "", 2, undefined, { flipInRtl: true }));
exports.PanelLeftTextRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftTextRegular", "", 2, undefined, { flipInRtl: true }));
exports.PanelLeftTextAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftTextAddFilled", "", 2, undefined, { flipInRtl: true }));
exports.PanelLeftTextAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftTextAddRegular", "", 2, undefined, { flipInRtl: true }));
exports.PanelLeftTextDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftTextDismissFilled", "", 2, undefined, { flipInRtl: true }));
exports.PanelLeftTextDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelLeftTextDismissRegular", "", 2, undefined, { flipInRtl: true }));
exports.PanelRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelRightFilled", "", 2, undefined));
exports.PanelRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelRightRegular", "", 2, undefined));
exports.PanelRightAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelRightAddFilled", "", 2, undefined));
exports.PanelRightAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelRightAddRegular", "", 2, undefined));
exports.PanelRightContractFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelRightContractFilled", "", 2, undefined));
exports.PanelRightContractRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelRightContractRegular", "", 2, undefined));
exports.PanelRightCursorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelRightCursorFilled", "", 2, undefined));
exports.PanelRightCursorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelRightCursorRegular", "", 2, undefined));
exports.PanelRightExpandFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelRightExpandFilled", "", 2, undefined));
exports.PanelRightExpandRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelRightExpandRegular", "", 2, undefined));
exports.PanelRightGalleryFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelRightGalleryFilled", "", 2, undefined, { flipInRtl: true }));
exports.PanelRightGalleryRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelRightGalleryRegular", "", 2, undefined, { flipInRtl: true }));
exports.PanelSeparateWindowFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelSeparateWindowFilled", "", 2, undefined));
exports.PanelSeparateWindowRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelSeparateWindowRegular", "", 2, undefined));
exports.PanelTopContractFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelTopContractFilled", "", 2, undefined));
exports.PanelTopContractRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelTopContractRegular", "", 2, undefined));
exports.PanelTopExpandFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelTopExpandFilled", "", 2, undefined));
exports.PanelTopExpandRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelTopExpandRegular", "", 2, undefined));
exports.PanelTopGalleryFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelTopGalleryFilled", "", 2, undefined));
exports.PanelTopGalleryRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PanelTopGalleryRegular", "", 2, undefined));
exports.PasswordFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PasswordFilled", "", 2, undefined));
exports.PasswordRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PasswordRegular", "", 2, undefined));
exports.PasswordClockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PasswordClockFilled", "", 2, undefined));
exports.PasswordClockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PasswordClockRegular", "", 2, undefined));
exports.PatchFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PatchFilled", "", 2, undefined));
exports.PatchRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PatchRegular", "", 2, undefined));
exports.PatientFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PatientFilled", "", 2, undefined));
exports.PatientRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PatientRegular", "", 2, undefined));
exports.PauseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PauseFilled", "", 2, undefined));
exports.PauseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PauseRegular", "", 2, undefined));
exports.PauseCircleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PauseCircleFilled", "", 2, undefined));
exports.PauseCircleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PauseCircleRegular", "", 2, undefined));
exports.PauseOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PauseOffFilled", "", 2, undefined));
exports.PauseOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PauseOffRegular", "", 2, undefined));
exports.PauseSettingsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PauseSettingsFilled", "", 2, undefined));
exports.PauseSettingsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PauseSettingsRegular", "", 2, undefined));
exports.PaymentFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaymentFilled", "", 2, undefined));
exports.PaymentRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaymentRegular", "", 2, undefined));
exports.PaymentWirelessFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaymentWirelessFilled", "", 2, undefined));
exports.PaymentWirelessRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PaymentWirelessRegular", "", 2, undefined));
exports.PenFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PenFilled", "", 2, undefined));
exports.PenRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PenRegular", "", 2, undefined));
exports.PenDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PenDismissFilled", "", 2, undefined));
exports.PenDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PenDismissRegular", "", 2, undefined));
exports.PenOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PenOffFilled", "", 2, undefined));
exports.PenOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PenOffRegular", "", 2, undefined));
exports.PenProhibitedFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PenProhibitedFilled", "", 2, undefined));
exports.PenProhibitedRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PenProhibitedRegular", "", 2, undefined));
exports.PenSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PenSparkleFilled", "", 2, undefined));
exports.PenSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PenSparkleRegular", "", 2, undefined));
exports.PenSyncFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PenSyncFilled", "", 2, undefined));
exports.PenSyncRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PenSyncRegular", "", 2, undefined));
exports.PentagonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PentagonFilled", "", 2, undefined));
exports.PentagonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PentagonRegular", "", 2, undefined));
exports.PeopleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleFilled", "", 2, undefined));
exports.PeopleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleRegular", "", 2, undefined));
exports.PeopleAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleAddFilled", "", 2, undefined));
exports.PeopleAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleAddRegular", "", 2, undefined));
exports.PeopleAudienceFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleAudienceFilled", "", 2, undefined));
exports.PeopleAudienceRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleAudienceRegular", "", 2, undefined));
exports.PeopleCallFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleCallFilled", "", 2, undefined));
exports.PeopleCallRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleCallRegular", "", 2, undefined));
exports.PeopleChatFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleChatFilled", "", 2, undefined));
exports.PeopleChatRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleChatRegular", "", 2, undefined));
exports.PeopleCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleCheckmarkFilled", "", 2, undefined));
exports.PeopleCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleCheckmarkRegular", "", 2, undefined));
exports.PeopleCommunicationFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleCommunicationFilled", "", 2, undefined));
exports.PeopleCommunicationRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleCommunicationRegular", "", 2, undefined));
exports.PeopleCommunityFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleCommunityFilled", "", 2, undefined));
exports.PeopleCommunityRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleCommunityRegular", "", 2, undefined));
exports.PeopleCommunityAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleCommunityAddFilled", "", 2, undefined));
exports.PeopleCommunityAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleCommunityAddRegular", "", 2, undefined));
exports.PeopleEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleEditFilled", "", 2, undefined));
exports.PeopleEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleEditRegular", "", 2, undefined));
exports.PeopleErrorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleErrorFilled", "", 2, undefined));
exports.PeopleErrorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleErrorRegular", "", 2, undefined));
exports.PeopleEyeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleEyeFilled", "", 2, undefined));
exports.PeopleEyeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleEyeRegular", "", 2, undefined));
exports.PeopleInterwovenFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleInterwovenFilled", "", 2, undefined));
exports.PeopleInterwovenRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleInterwovenRegular", "", 2, undefined));
exports.PeopleLinkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleLinkFilled", "", 2, undefined));
exports.PeopleLinkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleLinkRegular", "", 2, undefined));
exports.PeopleListFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleListFilled", "", 2, undefined));
exports.PeopleListRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleListRegular", "", 2, undefined));
exports.PeopleLockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleLockFilled", "", 2, undefined));
exports.PeopleLockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleLockRegular", "", 2, undefined));
exports.PeopleMoneyFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleMoneyFilled", "", 2, undefined));
exports.PeopleMoneyRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleMoneyRegular", "", 2, undefined));
exports.PeopleProhibitedFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleProhibitedFilled", "", 2, undefined));
exports.PeopleProhibitedRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleProhibitedRegular", "", 2, undefined));
exports.PeopleQueueFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleQueueFilled", "", 2, undefined));
exports.PeopleQueueRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleQueueRegular", "", 2, undefined));
exports.PeopleSearchFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleSearchFilled", "", 2, undefined));
exports.PeopleSearchRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleSearchRegular", "", 2, undefined));
exports.PeopleSettingsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleSettingsFilled", "", 2, undefined));
exports.PeopleSettingsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleSettingsRegular", "", 2, undefined));
exports.PeopleStarFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleStarFilled", "", 2, undefined));
exports.PeopleStarRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleStarRegular", "", 2, undefined));
exports.PeopleSubtractFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleSubtractFilled", "", 2, undefined));
exports.PeopleSubtractRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleSubtractRegular", "", 2, undefined));
exports.PeopleSwapFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleSwapFilled", "", 2, undefined));
exports.PeopleSwapRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleSwapRegular", "", 2, undefined));
exports.PeopleSyncFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleSyncFilled", "", 2, undefined));
exports.PeopleSyncRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleSyncRegular", "", 2, undefined));
exports.PeopleTeamFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleTeamFilled", "", 2, undefined));
exports.PeopleTeamRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleTeamRegular", "", 2, undefined));
exports.PeopleTeamAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleTeamAddFilled", "", 2, undefined));
exports.PeopleTeamAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleTeamAddRegular", "", 2, undefined));
exports.PeopleTeamDeleteFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleTeamDeleteFilled", "", 2, undefined));
exports.PeopleTeamDeleteRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleTeamDeleteRegular", "", 2, undefined));
exports.PeopleTeamToolboxFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleTeamToolboxFilled", "", 2, undefined));
exports.PeopleTeamToolboxRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleTeamToolboxRegular", "", 2, undefined));
exports.PeopleToolboxFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleToolboxFilled", "", 2, undefined));
exports.PeopleToolboxRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PeopleToolboxRegular", "", 2, undefined));
exports.PersonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonFilled", "", 2, undefined));
exports.PersonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonRegular", "", 2, undefined));
exports.Person5Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Person5Filled", "", 2, undefined));
exports.Person5Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Person5Regular", "", 2, undefined));
exports.Person6Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Person6Filled", "", 2, undefined));
exports.Person6Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Person6Regular", "", 2, undefined));
exports.PersonAccountsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonAccountsFilled", "", 2, undefined));
exports.PersonAccountsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonAccountsRegular", "", 2, undefined));
exports.PersonAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonAddFilled", "", 2, undefined));
exports.PersonAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonAddRegular", "", 2, undefined));
exports.PersonAlertFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonAlertFilled", "", 2, undefined));
exports.PersonAlertRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonAlertRegular", "", 2, undefined));
exports.PersonAlertOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonAlertOffFilled", "", 2, undefined));
exports.PersonAlertOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonAlertOffRegular", "", 2, undefined));
exports.PersonArrowBackFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonArrowBackFilled", "", 2, undefined));
exports.PersonArrowBackRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonArrowBackRegular", "", 2, undefined));
exports.PersonArrowLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonArrowLeftFilled", "", 2, undefined));
exports.PersonArrowLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonArrowLeftRegular", "", 2, undefined));
exports.PersonArrowRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonArrowRightFilled", "", 2, undefined));
exports.PersonArrowRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonArrowRightRegular", "", 2, undefined));
exports.PersonAvailableFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonAvailableFilled", "", 2, undefined));
exports.PersonAvailableRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonAvailableRegular", "", 2, undefined));
exports.PersonBoardFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonBoardFilled", "", 2, undefined));
exports.PersonBoardRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonBoardRegular", "", 2, undefined));
exports.PersonBoardAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonBoardAddFilled", "", 2, undefined));
exports.PersonBoardAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonBoardAddRegular", "", 2, undefined));
exports.PersonBriefcaseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonBriefcaseFilled", "", 2, undefined));
exports.PersonBriefcaseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonBriefcaseRegular", "", 2, undefined));
exports.PersonCallFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonCallFilled", "", 2, undefined));
exports.PersonCallRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonCallRegular", "", 2, undefined));
exports.PersonChatFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonChatFilled", "", 2, undefined));
exports.PersonChatRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonChatRegular", "", 2, undefined));
exports.PersonCircleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonCircleFilled", "", 2, undefined));
exports.PersonCircleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonCircleRegular", "", 2, undefined));
exports.PersonClockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonClockFilled", "", 2, undefined));
exports.PersonClockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonClockRegular", "", 2, undefined));
exports.PersonDeleteFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonDeleteFilled", "", 2, undefined));
exports.PersonDeleteRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonDeleteRegular", "", 2, undefined));
exports.PersonDesktopFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonDesktopFilled", "", 2, undefined));
exports.PersonDesktopRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonDesktopRegular", "", 2, undefined));
exports.PersonEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonEditFilled", "", 2, undefined));
exports.PersonEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonEditRegular", "", 2, undefined));
exports.PersonErrorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonErrorFilled", "", 2, undefined));
exports.PersonErrorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonErrorRegular", "", 2, undefined));
exports.PersonFeedbackFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonFeedbackFilled", "", 2, undefined));
exports.PersonFeedbackRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonFeedbackRegular", "", 2, undefined));
exports.PersonGuestFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonGuestFilled", "", 2, undefined));
exports.PersonGuestRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonGuestRegular", "", 2, undefined));
exports.PersonHeadHintFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonHeadHintFilled", "", 2, undefined));
exports.PersonHeadHintRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonHeadHintRegular", "", 2, undefined));
exports.PersonHeartFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonHeartFilled", "", 2, undefined));
exports.PersonHeartRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonHeartRegular", "", 2, undefined));
exports.PersonHomeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonHomeFilled", "", 2, undefined));
exports.PersonHomeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonHomeRegular", "", 2, undefined));
exports.PersonInfoFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonInfoFilled", "", 2, undefined));
exports.PersonInfoRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonInfoRegular", "", 2, undefined));
exports.PersonKeyFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonKeyFilled", "", 2, undefined));
exports.PersonKeyRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonKeyRegular", "", 2, undefined));
exports.PersonLightbulbFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonLightbulbFilled", "", 2, undefined));
exports.PersonLightbulbRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonLightbulbRegular", "", 2, undefined));
exports.PersonLightningFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonLightningFilled", "", 2, undefined));
exports.PersonLightningRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonLightningRegular", "", 2, undefined));
exports.PersonLinkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonLinkFilled", "", 2, undefined));
exports.PersonLinkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonLinkRegular", "", 2, undefined));
exports.PersonLockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonLockFilled", "", 2, undefined));
exports.PersonLockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonLockRegular", "", 2, undefined));
exports.PersonMailFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonMailFilled", "", 2, undefined));
exports.PersonMailRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonMailRegular", "", 2, undefined));
exports.PersonMoneyFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonMoneyFilled", "", 2, undefined));
exports.PersonMoneyRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonMoneyRegular", "", 2, undefined));
exports.PersonNoteFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonNoteFilled", "", 2, undefined));
exports.PersonNoteRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonNoteRegular", "", 2, undefined));
exports.PersonPasskeyFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonPasskeyFilled", "", 2, undefined));
exports.PersonPasskeyRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonPasskeyRegular", "", 2, undefined));
exports.PersonPillFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonPillFilled", "", 2, undefined));
exports.PersonPillRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonPillRegular", "", 2, undefined));
exports.PersonProhibitedFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonProhibitedFilled", "", 2, undefined));
exports.PersonProhibitedRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonProhibitedRegular", "", 2, undefined));
exports.PersonQuestionMarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonQuestionMarkFilled", "", 2, undefined));
exports.PersonQuestionMarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonQuestionMarkRegular", "", 2, undefined));
exports.PersonRibbonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonRibbonFilled", "", 2, undefined));
exports.PersonRibbonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonRibbonRegular", "", 2, undefined));
exports.PersonRunningFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonRunningFilled", "", 2, undefined));
exports.PersonRunningRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonRunningRegular", "", 2, undefined));
exports.PersonSearchFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonSearchFilled", "", 2, undefined));
exports.PersonSearchRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonSearchRegular", "", 2, undefined));
exports.PersonSettingsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonSettingsFilled", "", 2, undefined));
exports.PersonSettingsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonSettingsRegular", "", 2, undefined));
exports.PersonShieldFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonShieldFilled", "", 2, undefined));
exports.PersonShieldRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonShieldRegular", "", 2, undefined));
exports.PersonSoundSpatialFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonSoundSpatialFilled", "", 2, undefined));
exports.PersonSoundSpatialRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonSoundSpatialRegular", "", 2, undefined));
exports.PersonSquareFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonSquareFilled", "", 2, undefined));
exports.PersonSquareRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonSquareRegular", "", 2, undefined));
exports.PersonSquareAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonSquareAddFilled", "", 2, undefined));
exports.PersonSquareAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonSquareAddRegular", "", 2, undefined));
exports.PersonSquareCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonSquareCheckmarkFilled", "", 2, undefined));
exports.PersonSquareCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonSquareCheckmarkRegular", "", 2, undefined));
exports.PersonStarFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonStarFilled", "", 2, undefined));
exports.PersonStarRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonStarRegular", "", 2, undefined));
exports.PersonStarburstFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonStarburstFilled", "", 2, undefined));
exports.PersonStarburstRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonStarburstRegular", "", 2, undefined));
exports.PersonSubtractFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonSubtractFilled", "", 2, undefined));
exports.PersonSubtractRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonSubtractRegular", "", 2, undefined));
exports.PersonSupportFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonSupportFilled", "", 2, undefined));
exports.PersonSupportRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonSupportRegular", "", 2, undefined));
exports.PersonSwapFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonSwapFilled", "", 2, undefined));
exports.PersonSwapRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonSwapRegular", "", 2, undefined));
exports.PersonSyncFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonSyncFilled", "", 2, undefined));
exports.PersonSyncRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonSyncRegular", "", 2, undefined));
exports.PersonTagFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonTagFilled", "", 2, undefined));
exports.PersonTagRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonTagRegular", "", 2, undefined));
exports.PersonTentativeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonTentativeFilled", "", 2, undefined));
exports.PersonTentativeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonTentativeRegular", "", 2, undefined));
exports.PersonVoiceFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonVoiceFilled", "", 2, undefined));
exports.PersonVoiceRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonVoiceRegular", "", 2, undefined));
exports.PersonWalkingFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonWalkingFilled", "", 2, undefined));
exports.PersonWalkingRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonWalkingRegular", "", 2, undefined));
exports.PersonWarningFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonWarningFilled", "", 2, undefined));
exports.PersonWarningRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonWarningRegular", "", 2, undefined));
exports.PersonWrenchFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonWrenchFilled", "", 2, undefined));
exports.PersonWrenchRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PersonWrenchRegular", "", 2, undefined));
exports.PhoneFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneFilled", "", 2, undefined));
exports.PhoneRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneRegular", "", 2, undefined));
exports.PhoneAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneAddFilled", "", 2, undefined));
exports.PhoneAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneAddRegular", "", 2, undefined));
exports.PhoneArrowRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneArrowRightFilled", "", 2, undefined));
exports.PhoneArrowRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneArrowRightRegular", "", 2, undefined));
exports.PhoneChatFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneChatFilled", "", 2, undefined));
exports.PhoneChatRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneChatRegular", "", 2, undefined));
exports.PhoneCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneCheckmarkFilled", "", 2, undefined));
exports.PhoneCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneCheckmarkRegular", "", 2, undefined));
exports.PhoneDesktopFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneDesktopFilled", "", 2, undefined));
exports.PhoneDesktopRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneDesktopRegular", "", 2, undefined));
exports.PhoneDesktopAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneDesktopAddFilled", "", 2, undefined));
exports.PhoneDesktopAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneDesktopAddRegular", "", 2, undefined));
exports.PhoneDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneDismissFilled", "", 2, undefined));
exports.PhoneDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneDismissRegular", "", 2, undefined));
exports.PhoneEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneEditFilled", "", 2, undefined));
exports.PhoneEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneEditRegular", "", 2, undefined));
exports.PhoneEraserFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneEraserFilled", "", 2, undefined));
exports.PhoneEraserRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneEraserRegular", "", 2, undefined));
exports.PhoneFooterArrowDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneFooterArrowDownFilled", "", 2, undefined));
exports.PhoneFooterArrowDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneFooterArrowDownRegular", "", 2, undefined));
exports.PhoneHeaderArrowUpFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneHeaderArrowUpFilled", "", 2, undefined));
exports.PhoneHeaderArrowUpRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneHeaderArrowUpRegular", "", 2, undefined));
exports.PhoneKeyFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneKeyFilled", "", 2, undefined));
exports.PhoneKeyRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneKeyRegular", "", 2, undefined));
exports.PhoneLaptopFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneLaptopFilled", "", 2, undefined));
exports.PhoneLaptopRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneLaptopRegular", "", 2, undefined));
exports.PhoneLinkSetupFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneLinkSetupFilled", "", 2, undefined));
exports.PhoneLinkSetupRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneLinkSetupRegular", "", 2, undefined));
exports.PhoneLockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneLockFilled", "", 2, undefined));
exports.PhoneLockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneLockRegular", "", 2, undefined));
exports.PhonePageHeaderFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhonePageHeaderFilled", "", 2, undefined));
exports.PhonePageHeaderRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhonePageHeaderRegular", "", 2, undefined));
exports.PhonePaginationFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhonePaginationFilled", "", 2, undefined));
exports.PhonePaginationRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhonePaginationRegular", "", 2, undefined));
exports.PhoneScreenTimeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneScreenTimeFilled", "", 2, undefined));
exports.PhoneScreenTimeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneScreenTimeRegular", "", 2, undefined));
exports.PhoneShakeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneShakeFilled", "", 2, undefined));
exports.PhoneShakeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneShakeRegular", "", 2, undefined));
exports.PhoneSpanInFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneSpanInFilled", "", 2, undefined));
exports.PhoneSpanInRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneSpanInRegular", "", 2, undefined));
exports.PhoneSpanOutFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneSpanOutFilled", "", 2, undefined));
exports.PhoneSpanOutRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneSpanOutRegular", "", 2, undefined));
exports.PhoneSpeakerFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneSpeakerFilled", "", 2, undefined));
exports.PhoneSpeakerRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneSpeakerRegular", "", 2, undefined));
exports.PhoneStatusBarFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneStatusBarFilled", "", 2, undefined));
exports.PhoneStatusBarRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneStatusBarRegular", "", 2, undefined));
exports.PhoneTabletFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneTabletFilled", "", 2, undefined));
exports.PhoneTabletRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneTabletRegular", "", 2, undefined));
exports.PhoneUpdateFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneUpdateFilled", "", 2, undefined));
exports.PhoneUpdateRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneUpdateRegular", "", 2, undefined));
exports.PhoneUpdateCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneUpdateCheckmarkFilled", "", 2, undefined));
exports.PhoneUpdateCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("PhoneUpdateCheckmarkRegular", "", 2, undefined));
