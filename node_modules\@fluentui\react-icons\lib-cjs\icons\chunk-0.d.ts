import type { FluentIcon } from "../utils/createFluentIcon";
export declare const AccessTimeFilled: FluentIcon;
export declare const AccessTimeRegular: FluentIcon;
export declare const AccessibilityFilled: FluentIcon;
export declare const AccessibilityRegular: FluentIcon;
export declare const AccessibilityCheckmarkFilled: FluentIcon;
export declare const AccessibilityCheckmarkRegular: FluentIcon;
export declare const AccessibilityErrorFilled: FluentIcon;
export declare const AccessibilityErrorRegular: FluentIcon;
export declare const AccessibilityMoreFilled: FluentIcon;
export declare const AccessibilityMoreRegular: FluentIcon;
export declare const AccessibilityQuestionMarkFilled: FluentIcon;
export declare const AccessibilityQuestionMarkRegular: FluentIcon;
export declare const AddFilled: FluentIcon;
export declare const AddRegular: FluentIcon;
export declare const AddCircleColor: FluentIcon;
export declare const AddCircleFilled: FluentIcon;
export declare const AddCircleRegular: FluentIcon;
export declare const AddSquareFilled: FluentIcon;
export declare const AddSquareRegular: FluentIcon;
export declare const AddSquareMultipleFilled: FluentIcon;
export declare const AddSquareMultipleRegular: FluentIcon;
export declare const AddStarburstColor: FluentIcon;
export declare const AddStarburstFilled: FluentIcon;
export declare const AddStarburstRegular: FluentIcon;
export declare const AddSubtractCircleFilled: FluentIcon;
export declare const AddSubtractCircleRegular: FluentIcon;
export declare const AgentsColor: FluentIcon;
export declare const AgentsFilled: FluentIcon;
export declare const AgentsRegular: FluentIcon;
export declare const AgentsAddFilled: FluentIcon;
export declare const AgentsAddRegular: FluentIcon;
export declare const AirplaneFilled: FluentIcon;
export declare const AirplaneRegular: FluentIcon;
export declare const AirplaneLandingFilled: FluentIcon;
export declare const AirplaneLandingRegular: FluentIcon;
export declare const AirplaneTakeOffFilled: FluentIcon;
export declare const AirplaneTakeOffRegular: FluentIcon;
export declare const AlbumFilled: FluentIcon;
export declare const AlbumRegular: FluentIcon;
export declare const AlbumAddFilled: FluentIcon;
export declare const AlbumAddRegular: FluentIcon;
export declare const AlertColor: FluentIcon;
export declare const AlertFilled: FluentIcon;
export declare const AlertRegular: FluentIcon;
export declare const AlertBadgeColor: FluentIcon;
export declare const AlertBadgeFilled: FluentIcon;
export declare const AlertBadgeRegular: FluentIcon;
export declare const AlertOffFilled: FluentIcon;
export declare const AlertOffRegular: FluentIcon;
export declare const AlertOnFilled: FluentIcon;
export declare const AlertOnRegular: FluentIcon;
export declare const AlertSnoozeFilled: FluentIcon;
export declare const AlertSnoozeRegular: FluentIcon;
export declare const AlertUrgentColor: FluentIcon;
export declare const AlertUrgentFilled: FluentIcon;
export declare const AlertUrgentRegular: FluentIcon;
export declare const AlignBottomFilled: FluentIcon;
export declare const AlignBottomRegular: FluentIcon;
export declare const AlignCenterHorizontalFilled: FluentIcon;
export declare const AlignCenterHorizontalRegular: FluentIcon;
export declare const AlignCenterVerticalFilled: FluentIcon;
export declare const AlignCenterVerticalRegular: FluentIcon;
export declare const AlignEndHorizontalFilled: FluentIcon;
export declare const AlignEndHorizontalRegular: FluentIcon;
export declare const AlignEndVerticalFilled: FluentIcon;
export declare const AlignEndVerticalRegular: FluentIcon;
export declare const AlignLeftFilled: FluentIcon;
export declare const AlignLeftRegular: FluentIcon;
export declare const AlignRightFilled: FluentIcon;
export declare const AlignRightRegular: FluentIcon;
export declare const AlignSpaceAroundHorizontalFilled: FluentIcon;
export declare const AlignSpaceAroundHorizontalRegular: FluentIcon;
export declare const AlignSpaceAroundVerticalFilled: FluentIcon;
export declare const AlignSpaceAroundVerticalRegular: FluentIcon;
export declare const AlignSpaceBetweenHorizontalFilled: FluentIcon;
export declare const AlignSpaceBetweenHorizontalRegular: FluentIcon;
export declare const AlignSpaceBetweenVerticalFilled: FluentIcon;
export declare const AlignSpaceBetweenVerticalRegular: FluentIcon;
export declare const AlignSpaceEvenlyHorizontalFilled: FluentIcon;
export declare const AlignSpaceEvenlyHorizontalRegular: FluentIcon;
export declare const AlignSpaceEvenlyVerticalFilled: FluentIcon;
export declare const AlignSpaceEvenlyVerticalRegular: FluentIcon;
export declare const AlignSpaceFitVerticalFilled: FluentIcon;
export declare const AlignSpaceFitVerticalRegular: FluentIcon;
export declare const AlignStartHorizontalFilled: FluentIcon;
export declare const AlignStartHorizontalRegular: FluentIcon;
export declare const AlignStartVerticalFilled: FluentIcon;
export declare const AlignStartVerticalRegular: FluentIcon;
export declare const AlignStraightenFilled: FluentIcon;
export declare const AlignStraightenRegular: FluentIcon;
export declare const AlignStretchHorizontalFilled: FluentIcon;
export declare const AlignStretchHorizontalRegular: FluentIcon;
export declare const AlignStretchVerticalFilled: FluentIcon;
export declare const AlignStretchVerticalRegular: FluentIcon;
export declare const AlignTopFilled: FluentIcon;
export declare const AlignTopRegular: FluentIcon;
export declare const AnimalCatFilled: FluentIcon;
export declare const AnimalCatRegular: FluentIcon;
export declare const AnimalDogFilled: FluentIcon;
export declare const AnimalDogRegular: FluentIcon;
export declare const AnimalPawPrintColor: FluentIcon;
export declare const AnimalPawPrintFilled: FluentIcon;
export declare const AnimalPawPrintRegular: FluentIcon;
export declare const AnimalRabbitFilled: FluentIcon;
export declare const AnimalRabbitRegular: FluentIcon;
export declare const AnimalRabbitOffFilled: FluentIcon;
export declare const AnimalRabbitOffRegular: FluentIcon;
export declare const AnimalTurtleFilled: FluentIcon;
export declare const AnimalTurtleRegular: FluentIcon;
export declare const AppFolderFilled: FluentIcon;
export declare const AppFolderRegular: FluentIcon;
export declare const AppGenericFilled: FluentIcon;
export declare const AppGenericRegular: FluentIcon;
export declare const AppRecentFilled: FluentIcon;
export declare const AppRecentRegular: FluentIcon;
export declare const AppTitleFilled: FluentIcon;
export declare const AppTitleRegular: FluentIcon;
export declare const ApprovalsAppColor: FluentIcon;
export declare const ApprovalsAppFilled: FluentIcon;
export declare const ApprovalsAppRegular: FluentIcon;
export declare const AppsColor: FluentIcon;
export declare const AppsFilled: FluentIcon;
export declare const AppsRegular: FluentIcon;
export declare const AppsAddInFilled: FluentIcon;
export declare const AppsAddInRegular: FluentIcon;
export declare const AppsAddInOffFilled: FluentIcon;
export declare const AppsAddInOffRegular: FluentIcon;
export declare const AppsListColor: FluentIcon;
export declare const AppsListFilled: FluentIcon;
export declare const AppsListRegular: FluentIcon;
export declare const AppsListDetailColor: FluentIcon;
export declare const AppsListDetailFilled: FluentIcon;
export declare const AppsListDetailRegular: FluentIcon;
export declare const AppsSettingsFilled: FluentIcon;
export declare const AppsSettingsRegular: FluentIcon;
export declare const AppsShieldFilled: FluentIcon;
export declare const AppsShieldRegular: FluentIcon;
export declare const ArchiveFilled: FluentIcon;
export declare const ArchiveRegular: FluentIcon;
export declare const ArchiveArrowBackFilled: FluentIcon;
export declare const ArchiveArrowBackRegular: FluentIcon;
export declare const ArchiveMultipleFilled: FluentIcon;
export declare const ArchiveMultipleRegular: FluentIcon;
export declare const ArchiveSettingsFilled: FluentIcon;
export declare const ArchiveSettingsRegular: FluentIcon;
export declare const ArrowAutofitContentFilled: FluentIcon;
export declare const ArrowAutofitContentRegular: FluentIcon;
export declare const ArrowAutofitDownFilled: FluentIcon;
export declare const ArrowAutofitDownRegular: FluentIcon;
export declare const ArrowAutofitHeightFilled: FluentIcon;
export declare const ArrowAutofitHeightRegular: FluentIcon;
export declare const ArrowAutofitHeightDottedFilled: FluentIcon;
export declare const ArrowAutofitHeightDottedRegular: FluentIcon;
export declare const ArrowAutofitHeightInFilled: FluentIcon;
export declare const ArrowAutofitHeightInRegular: FluentIcon;
export declare const ArrowAutofitUpFilled: FluentIcon;
export declare const ArrowAutofitUpRegular: FluentIcon;
export declare const ArrowAutofitWidthFilled: FluentIcon;
export declare const ArrowAutofitWidthRegular: FluentIcon;
export declare const ArrowAutofitWidthDottedFilled: FluentIcon;
export declare const ArrowAutofitWidthDottedRegular: FluentIcon;
export declare const ArrowBetweenDownFilled: FluentIcon;
export declare const ArrowBetweenDownRegular: FluentIcon;
export declare const ArrowBetweenUpFilled: FluentIcon;
export declare const ArrowBetweenUpRegular: FluentIcon;
export declare const ArrowBidirectionalLeftRightFilled: FluentIcon;
export declare const ArrowBidirectionalLeftRightRegular: FluentIcon;
export declare const ArrowBidirectionalUpDownFilled: FluentIcon;
export declare const ArrowBidirectionalUpDownRegular: FluentIcon;
export declare const ArrowBounceFilled: FluentIcon;
export declare const ArrowBounceRegular: FluentIcon;
export declare const ArrowCircleDownFilled: FluentIcon;
export declare const ArrowCircleDownRegular: FluentIcon;
export declare const ArrowCircleDownDoubleFilled: FluentIcon;
export declare const ArrowCircleDownDoubleRegular: FluentIcon;
export declare const ArrowCircleDownRightFilled: FluentIcon;
export declare const ArrowCircleDownRightRegular: FluentIcon;
export declare const ArrowCircleDownSplitFilled: FluentIcon;
export declare const ArrowCircleDownSplitRegular: FluentIcon;
export declare const ArrowCircleDownUpFilled: FluentIcon;
export declare const ArrowCircleDownUpRegular: FluentIcon;
export declare const ArrowCircleLeftFilled: FluentIcon;
export declare const ArrowCircleLeftRegular: FluentIcon;
export declare const ArrowCircleRightFilled: FluentIcon;
export declare const ArrowCircleRightRegular: FluentIcon;
export declare const ArrowCircleUpFilled: FluentIcon;
export declare const ArrowCircleUpRegular: FluentIcon;
export declare const ArrowCircleUpLeftFilled: FluentIcon;
export declare const ArrowCircleUpLeftRegular: FluentIcon;
export declare const ArrowCircleUpRightFilled: FluentIcon;
export declare const ArrowCircleUpRightRegular: FluentIcon;
export declare const ArrowCircleUpSparkleFilled: FluentIcon;
export declare const ArrowCircleUpSparkleRegular: FluentIcon;
export declare const ArrowClockwiseFilled: FluentIcon;
export declare const ArrowClockwiseRegular: FluentIcon;
export declare const ArrowClockwiseDashesColor: FluentIcon;
export declare const ArrowClockwiseDashesFilled: FluentIcon;
export declare const ArrowClockwiseDashesRegular: FluentIcon;
export declare const ArrowClockwiseDashesSettingsColor: FluentIcon;
export declare const ArrowClockwiseDashesSettingsFilled: FluentIcon;
export declare const ArrowClockwiseDashesSettingsRegular: FluentIcon;
export declare const ArrowCollapseAllFilled: FluentIcon;
export declare const ArrowCollapseAllRegular: FluentIcon;
export declare const ArrowCounterclockwiseFilled: FluentIcon;
export declare const ArrowCounterclockwiseRegular: FluentIcon;
export declare const ArrowCounterclockwiseDashesFilled: FluentIcon;
export declare const ArrowCounterclockwiseDashesRegular: FluentIcon;
export declare const ArrowCounterclockwiseInfoFilled: FluentIcon;
export declare const ArrowCounterclockwiseInfoRegular: FluentIcon;
export declare const ArrowCurveDownLeftFilled: FluentIcon;
export declare const ArrowCurveDownLeftRegular: FluentIcon;
export declare const ArrowCurveDownRightFilled: FluentIcon;
export declare const ArrowCurveDownRightRegular: FluentIcon;
export declare const ArrowCurveUpLeftFilled: FluentIcon;
export declare const ArrowCurveUpLeftRegular: FluentIcon;
export declare const ArrowCurveUpRightFilled: FluentIcon;
export declare const ArrowCurveUpRightRegular: FluentIcon;
export declare const ArrowDownFilled: FluentIcon;
export declare const ArrowDownRegular: FluentIcon;
export declare const ArrowDownExclamationFilled: FluentIcon;
export declare const ArrowDownExclamationRegular: FluentIcon;
export declare const ArrowDownLeftFilled: FluentIcon;
export declare const ArrowDownLeftRegular: FluentIcon;
export declare const ArrowDownRightFilled: FluentIcon;
export declare const ArrowDownRightRegular: FluentIcon;
export declare const ArrowDownloadFilled: FluentIcon;
export declare const ArrowDownloadRegular: FluentIcon;
export declare const ArrowDownloadOffFilled: FluentIcon;
export declare const ArrowDownloadOffRegular: FluentIcon;
export declare const ArrowEjectFilled: FluentIcon;
export declare const ArrowEjectRegular: FluentIcon;
export declare const ArrowEnterFilled: FluentIcon;
export declare const ArrowEnterRegular: FluentIcon;
export declare const ArrowEnterLeftFilled: FluentIcon;
export declare const ArrowEnterLeftRegular: FluentIcon;
export declare const ArrowEnterUpFilled: FluentIcon;
export declare const ArrowEnterUpRegular: FluentIcon;
export declare const ArrowExitFilled: FluentIcon;
export declare const ArrowExitRegular: FluentIcon;
export declare const ArrowExpandFilled: FluentIcon;
export declare const ArrowExpandRegular: FluentIcon;
export declare const ArrowExpandAllFilled: FluentIcon;
export declare const ArrowExpandAllRegular: FluentIcon;
export declare const ArrowExportFilled: FluentIcon;
export declare const ArrowExportRegular: FluentIcon;
export declare const ArrowExportLtrFilled: FluentIcon;
export declare const ArrowExportLtrRegular: FluentIcon;
export declare const ArrowExportRtlFilled: FluentIcon;
export declare const ArrowExportRtlRegular: FluentIcon;
export declare const ArrowExportUpFilled: FluentIcon;
export declare const ArrowExportUpRegular: FluentIcon;
export declare const ArrowFitFilled: FluentIcon;
export declare const ArrowFitRegular: FluentIcon;
export declare const ArrowFitInFilled: FluentIcon;
export declare const ArrowFitInRegular: FluentIcon;
export declare const ArrowFlowDiagonalUpRightFilled: FluentIcon;
export declare const ArrowFlowDiagonalUpRightRegular: FluentIcon;
export declare const ArrowFlowUpRightFilled: FluentIcon;
export declare const ArrowFlowUpRightRegular: FluentIcon;
export declare const ArrowFlowUpRightRectangleMultipleFilled: FluentIcon;
export declare const ArrowFlowUpRightRectangleMultipleRegular: FluentIcon;
export declare const ArrowForwardFilled: FluentIcon;
export declare const ArrowForwardRegular: FluentIcon;
export declare const ArrowForwardDownLightningFilled: FluentIcon;
export declare const ArrowForwardDownLightningRegular: FluentIcon;
export declare const ArrowForwardDownPersonFilled: FluentIcon;
export declare const ArrowForwardDownPersonRegular: FluentIcon;
export declare const ArrowHookDownLeftFilled: FluentIcon;
export declare const ArrowHookDownLeftRegular: FluentIcon;
export declare const ArrowHookDownRightFilled: FluentIcon;
export declare const ArrowHookDownRightRegular: FluentIcon;
export declare const ArrowHookUpLeftFilled: FluentIcon;
export declare const ArrowHookUpLeftRegular: FluentIcon;
export declare const ArrowHookUpRightFilled: FluentIcon;
export declare const ArrowHookUpRightRegular: FluentIcon;
export declare const ArrowImportFilled: FluentIcon;
export declare const ArrowImportRegular: FluentIcon;
export declare const ArrowJoinFilled: FluentIcon;
export declare const ArrowJoinRegular: FluentIcon;
export declare const ArrowLeftFilled: FluentIcon;
export declare const ArrowLeftRegular: FluentIcon;
export declare const ArrowMaximizeFilled: FluentIcon;
export declare const ArrowMaximizeRegular: FluentIcon;
export declare const ArrowMaximizeVerticalFilled: FluentIcon;
export declare const ArrowMaximizeVerticalRegular: FluentIcon;
export declare const ArrowMinimizeFilled: FluentIcon;
export declare const ArrowMinimizeRegular: FluentIcon;
export declare const ArrowMinimizeVerticalFilled: FluentIcon;
export declare const ArrowMinimizeVerticalRegular: FluentIcon;
export declare const ArrowMoveFilled: FluentIcon;
export declare const ArrowMoveRegular: FluentIcon;
export declare const ArrowMoveInwardFilled: FluentIcon;
export declare const ArrowMoveInwardRegular: FluentIcon;
export declare const ArrowNextFilled: FluentIcon;
export declare const ArrowNextRegular: FluentIcon;
export declare const ArrowOutlineDownLeftFilled: FluentIcon;
export declare const ArrowOutlineDownLeftRegular: FluentIcon;
export declare const ArrowOutlineUpRightFilled: FluentIcon;
export declare const ArrowOutlineUpRightRegular: FluentIcon;
export declare const ArrowParagraphFilled: FluentIcon;
export declare const ArrowParagraphRegular: FluentIcon;
export declare const ArrowPreviousFilled: FluentIcon;
export declare const ArrowPreviousRegular: FluentIcon;
export declare const ArrowRedoFilled: FluentIcon;
export declare const ArrowRedoRegular: FluentIcon;
export declare const ArrowRepeat1Filled: FluentIcon;
export declare const ArrowRepeat1Regular: FluentIcon;
export declare const ArrowRepeatAllFilled: FluentIcon;
export declare const ArrowRepeatAllRegular: FluentIcon;
export declare const ArrowRepeatAllOffFilled: FluentIcon;
export declare const ArrowRepeatAllOffRegular: FluentIcon;
export declare const ArrowReplyFilled: FluentIcon;
export declare const ArrowReplyRegular: FluentIcon;
export declare const ArrowReplyAllFilled: FluentIcon;
export declare const ArrowReplyAllRegular: FluentIcon;
export declare const ArrowReplyDownFilled: FluentIcon;
export declare const ArrowReplyDownRegular: FluentIcon;
export declare const ArrowResetFilled: FluentIcon;
export declare const ArrowResetRegular: FluentIcon;
export declare const ArrowRightFilled: FluentIcon;
export declare const ArrowRightRegular: FluentIcon;
export declare const ArrowRotateClockwiseFilled: FluentIcon;
export declare const ArrowRotateClockwiseRegular: FluentIcon;
export declare const ArrowRotateCounterclockwiseFilled: FluentIcon;
export declare const ArrowRotateCounterclockwiseRegular: FluentIcon;
export declare const ArrowRoutingFilled: FluentIcon;
export declare const ArrowRoutingRegular: FluentIcon;
export declare const ArrowRoutingRectangleMultipleFilled: FluentIcon;
export declare const ArrowRoutingRectangleMultipleRegular: FluentIcon;
export declare const ArrowShuffleFilled: FluentIcon;
export declare const ArrowShuffleRegular: FluentIcon;
export declare const ArrowShuffleOffFilled: FluentIcon;
export declare const ArrowShuffleOffRegular: FluentIcon;
export declare const ArrowSortFilled: FluentIcon;
export declare const ArrowSortRegular: FluentIcon;
export declare const ArrowSortDownFilled: FluentIcon;
export declare const ArrowSortDownRegular: FluentIcon;
export declare const ArrowSortDownLinesFilled: FluentIcon;
export declare const ArrowSortDownLinesRegular: FluentIcon;
export declare const ArrowSortUpFilled: FluentIcon;
export declare const ArrowSortUpRegular: FluentIcon;
export declare const ArrowSortUpLinesFilled: FluentIcon;
export declare const ArrowSortUpLinesRegular: FluentIcon;
export declare const ArrowSplitFilled: FluentIcon;
export declare const ArrowSplitRegular: FluentIcon;
export declare const ArrowSprintFilled: FluentIcon;
export declare const ArrowSprintRegular: FluentIcon;
export declare const ArrowSquareColor: FluentIcon;
export declare const ArrowSquareDownColor: FluentIcon;
export declare const ArrowSquareDownFilled: FluentIcon;
export declare const ArrowSquareDownRegular: FluentIcon;
export declare const ArrowSquareUpRightFilled: FluentIcon;
export declare const ArrowSquareUpRightRegular: FluentIcon;
export declare const ArrowStepBackFilled: FluentIcon;
export declare const ArrowStepBackRegular: FluentIcon;
export declare const ArrowStepInFilled: FluentIcon;
export declare const ArrowStepInRegular: FluentIcon;
export declare const ArrowStepInDiagonalDownLeftFilled: FluentIcon;
export declare const ArrowStepInDiagonalDownLeftRegular: FluentIcon;
export declare const ArrowStepInLeftFilled: FluentIcon;
export declare const ArrowStepInLeftRegular: FluentIcon;
export declare const ArrowStepInRightFilled: FluentIcon;
export declare const ArrowStepInRightRegular: FluentIcon;
export declare const ArrowStepOutFilled: FluentIcon;
export declare const ArrowStepOutRegular: FluentIcon;
export declare const ArrowStepOverFilled: FluentIcon;
export declare const ArrowStepOverRegular: FluentIcon;
export declare const ArrowSwapFilled: FluentIcon;
export declare const ArrowSwapRegular: FluentIcon;
export declare const ArrowSyncColor: FluentIcon;
export declare const ArrowSyncFilled: FluentIcon;
export declare const ArrowSyncRegular: FluentIcon;
export declare const ArrowSyncCheckmarkFilled: FluentIcon;
export declare const ArrowSyncCheckmarkRegular: FluentIcon;
export declare const ArrowSyncCircleFilled: FluentIcon;
export declare const ArrowSyncCircleRegular: FluentIcon;
export declare const ArrowSyncDismissFilled: FluentIcon;
export declare const ArrowSyncDismissRegular: FluentIcon;
export declare const ArrowSyncOffFilled: FluentIcon;
export declare const ArrowSyncOffRegular: FluentIcon;
export declare const ArrowTrendingFilled: FluentIcon;
export declare const ArrowTrendingRegular: FluentIcon;
export declare const ArrowTrendingCheckmarkFilled: FluentIcon;
export declare const ArrowTrendingCheckmarkRegular: FluentIcon;
export declare const ArrowTrendingDownFilled: FluentIcon;
export declare const ArrowTrendingDownRegular: FluentIcon;
export declare const ArrowTrendingLinesColor: FluentIcon;
export declare const ArrowTrendingLinesFilled: FluentIcon;
export declare const ArrowTrendingLinesRegular: FluentIcon;
export declare const ArrowTrendingSettingsFilled: FluentIcon;
export declare const ArrowTrendingSettingsRegular: FluentIcon;
export declare const ArrowTrendingSparkleFilled: FluentIcon;
export declare const ArrowTrendingSparkleRegular: FluentIcon;
export declare const ArrowTrendingTextFilled: FluentIcon;
export declare const ArrowTrendingTextRegular: FluentIcon;
export declare const ArrowTrendingWrenchFilled: FluentIcon;
export declare const ArrowTrendingWrenchRegular: FluentIcon;
export declare const ArrowTurnBidirectionalDownRightFilled: FluentIcon;
export declare const ArrowTurnBidirectionalDownRightRegular: FluentIcon;
export declare const ArrowTurnDownLeftFilled: FluentIcon;
export declare const ArrowTurnDownLeftRegular: FluentIcon;
export declare const ArrowTurnDownRightFilled: FluentIcon;
export declare const ArrowTurnDownRightRegular: FluentIcon;
export declare const ArrowTurnDownUpFilled: FluentIcon;
export declare const ArrowTurnDownUpRegular: FluentIcon;
export declare const ArrowTurnLeftDownFilled: FluentIcon;
export declare const ArrowTurnLeftDownRegular: FluentIcon;
export declare const ArrowTurnLeftRightFilled: FluentIcon;
export declare const ArrowTurnLeftRightRegular: FluentIcon;
export declare const ArrowTurnLeftUpFilled: FluentIcon;
export declare const ArrowTurnLeftUpRegular: FluentIcon;
export declare const ArrowTurnRightFilled: FluentIcon;
export declare const ArrowTurnRightRegular: FluentIcon;
export declare const ArrowTurnRightDownFilled: FluentIcon;
export declare const ArrowTurnRightDownRegular: FluentIcon;
export declare const ArrowTurnRightLeftFilled: FluentIcon;
export declare const ArrowTurnRightLeftRegular: FluentIcon;
export declare const ArrowTurnRightUpFilled: FluentIcon;
export declare const ArrowTurnRightUpRegular: FluentIcon;
export declare const ArrowTurnUpDownFilled: FluentIcon;
export declare const ArrowTurnUpDownRegular: FluentIcon;
export declare const ArrowTurnUpLeftFilled: FluentIcon;
export declare const ArrowTurnUpLeftRegular: FluentIcon;
export declare const ArrowUndoFilled: FluentIcon;
export declare const ArrowUndoRegular: FluentIcon;
export declare const ArrowUpFilled: FluentIcon;
export declare const ArrowUpRegular: FluentIcon;
export declare const ArrowUpExclamationFilled: FluentIcon;
export declare const ArrowUpExclamationRegular: FluentIcon;
export declare const ArrowUpLeftFilled: FluentIcon;
export declare const ArrowUpLeftRegular: FluentIcon;
export declare const ArrowUpRightFilled: FluentIcon;
export declare const ArrowUpRightRegular: FluentIcon;
export declare const ArrowUpRightDashesFilled: FluentIcon;
export declare const ArrowUpRightDashesRegular: FluentIcon;
export declare const ArrowUploadFilled: FluentIcon;
export declare const ArrowUploadRegular: FluentIcon;
export declare const ArrowWrapFilled: FluentIcon;
export declare const ArrowWrapRegular: FluentIcon;
export declare const ArrowWrapOffFilled: FluentIcon;
export declare const ArrowWrapOffRegular: FluentIcon;
export declare const ArrowWrapUpToDownFilled: FluentIcon;
export declare const ArrowWrapUpToDownRegular: FluentIcon;
export declare const ArrowsBidirectionalFilled: FluentIcon;
export declare const ArrowsBidirectionalRegular: FluentIcon;
export declare const AttachFilled: FluentIcon;
export declare const AttachRegular: FluentIcon;
export declare const AttachArrowRightFilled: FluentIcon;
export declare const AttachArrowRightRegular: FluentIcon;
export declare const AttachTextFilled: FluentIcon;
export declare const AttachTextRegular: FluentIcon;
export declare const AutoFitHeightFilled: FluentIcon;
export declare const AutoFitHeightRegular: FluentIcon;
export declare const AutoFitWidthFilled: FluentIcon;
export declare const AutoFitWidthRegular: FluentIcon;
export declare const AutocorrectFilled: FluentIcon;
export declare const AutocorrectRegular: FluentIcon;
export declare const AutosumFilled: FluentIcon;
export declare const AutosumRegular: FluentIcon;
export declare const BackpackFilled: FluentIcon;
export declare const BackpackRegular: FluentIcon;
export declare const BackpackAddFilled: FluentIcon;
export declare const BackpackAddRegular: FluentIcon;
export declare const BackspaceFilled: FluentIcon;
export declare const BackspaceRegular: FluentIcon;
export declare const BadgeFilled: FluentIcon;
export declare const BadgeRegular: FluentIcon;
export declare const BalloonFilled: FluentIcon;
export declare const BalloonRegular: FluentIcon;
export declare const BarcodeScannerFilled: FluentIcon;
export declare const BarcodeScannerRegular: FluentIcon;
export declare const BarcodeScannerAddFilled: FluentIcon;
export declare const BarcodeScannerAddRegular: FluentIcon;
export declare const BarcodeScannerDismissFilled: FluentIcon;
export declare const BarcodeScannerDismissRegular: FluentIcon;
export declare const Battery0Filled: FluentIcon;
export declare const Battery0Regular: FluentIcon;
export declare const Battery10Filled: FluentIcon;
export declare const Battery10Regular: FluentIcon;
export declare const Battery1Filled: FluentIcon;
export declare const Battery1Regular: FluentIcon;
export declare const Battery2Filled: FluentIcon;
export declare const Battery2Regular: FluentIcon;
export declare const Battery3Filled: FluentIcon;
export declare const Battery3Regular: FluentIcon;
export declare const Battery4Filled: FluentIcon;
export declare const Battery4Regular: FluentIcon;
export declare const Battery5Filled: FluentIcon;
export declare const Battery5Regular: FluentIcon;
export declare const Battery6Filled: FluentIcon;
export declare const Battery6Regular: FluentIcon;
export declare const Battery7Filled: FluentIcon;
export declare const Battery7Regular: FluentIcon;
export declare const Battery8Filled: FluentIcon;
export declare const Battery8Regular: FluentIcon;
export declare const Battery9Filled: FluentIcon;
export declare const Battery9Regular: FluentIcon;
export declare const BatteryChargeFilled: FluentIcon;
export declare const BatteryChargeRegular: FluentIcon;
export declare const BatteryCheckmarkFilled: FluentIcon;
export declare const BatteryCheckmarkRegular: FluentIcon;
export declare const BatterySaverFilled: FluentIcon;
export declare const BatterySaverRegular: FluentIcon;
export declare const BatteryWarningFilled: FluentIcon;
export declare const BatteryWarningRegular: FluentIcon;
export declare const BeachColor: FluentIcon;
export declare const BeachFilled: FluentIcon;
export declare const BeachRegular: FluentIcon;
export declare const BeakerFilled: FluentIcon;
export declare const BeakerRegular: FluentIcon;
export declare const BeakerAddFilled: FluentIcon;
export declare const BeakerAddRegular: FluentIcon;
export declare const BeakerDismissFilled: FluentIcon;
export declare const BeakerDismissRegular: FluentIcon;
export declare const BeakerEditFilled: FluentIcon;
export declare const BeakerEditRegular: FluentIcon;
export declare const BeakerOffFilled: FluentIcon;
export declare const BeakerOffRegular: FluentIcon;
export declare const BeakerSettingsFilled: FluentIcon;
export declare const BeakerSettingsRegular: FluentIcon;
export declare const BedFilled: FluentIcon;
export declare const BedRegular: FluentIcon;
export declare const BenchFilled: FluentIcon;
export declare const BenchRegular: FluentIcon;
export declare const BezierCurveSquareFilled: FluentIcon;
export declare const BezierCurveSquareRegular: FluentIcon;
export declare const BinFullFilled: FluentIcon;
export declare const BinFullRegular: FluentIcon;
export declare const BinRecycleFilled: FluentIcon;
export declare const BinRecycleRegular: FluentIcon;
export declare const BinRecycleFullFilled: FluentIcon;
export declare const BinRecycleFullRegular: FluentIcon;
export declare const BinderTriangleFilled: FluentIcon;
export declare const BinderTriangleRegular: FluentIcon;
export declare const BluetoothFilled: FluentIcon;
export declare const BluetoothRegular: FluentIcon;
export declare const BluetoothConnectedFilled: FluentIcon;
export declare const BluetoothConnectedRegular: FluentIcon;
export declare const BluetoothDisabledFilled: FluentIcon;
export declare const BluetoothDisabledRegular: FluentIcon;
export declare const BluetoothSearchingFilled: FluentIcon;
export declare const BluetoothSearchingRegular: FluentIcon;
export declare const BlurFilled: FluentIcon;
export declare const BlurRegular: FluentIcon;
export declare const BoardColor: FluentIcon;
export declare const BoardFilled: FluentIcon;
export declare const BoardRegular: FluentIcon;
export declare const BoardGamesFilled: FluentIcon;
export declare const BoardGamesRegular: FluentIcon;
export declare const BoardHeartFilled: FluentIcon;
export declare const BoardHeartRegular: FluentIcon;
export declare const BoardSplitFilled: FluentIcon;
export declare const BoardSplitRegular: FluentIcon;
export declare const BookColor: FluentIcon;
export declare const BookFilled: FluentIcon;
export declare const BookRegular: FluentIcon;
export declare const BookAddFilled: FluentIcon;
export declare const BookAddRegular: FluentIcon;
export declare const BookArrowClockwiseFilled: FluentIcon;
export declare const BookArrowClockwiseRegular: FluentIcon;
export declare const BookClockFilled: FluentIcon;
export declare const BookClockRegular: FluentIcon;
export declare const BookCoinsFilled: FluentIcon;
export declare const BookCoinsRegular: FluentIcon;
export declare const BookCompassFilled: FluentIcon;
export declare const BookCompassRegular: FluentIcon;
export declare const BookContactsColor: FluentIcon;
export declare const BookContactsFilled: FluentIcon;
export declare const BookContactsRegular: FluentIcon;
export declare const BookDatabaseColor: FluentIcon;
export declare const BookDatabaseFilled: FluentIcon;
export declare const BookDatabaseRegular: FluentIcon;
export declare const BookDefaultFilled: FluentIcon;
export declare const BookDismissFilled: FluentIcon;
export declare const BookDismissRegular: FluentIcon;
export declare const BookExclamationMarkFilled: FluentIcon;
export declare const BookExclamationMarkRegular: FluentIcon;
export declare const BookGlobeFilled: FluentIcon;
export declare const BookGlobeRegular: FluentIcon;
export declare const BookInformationFilled: FluentIcon;
export declare const BookInformationRegular: FluentIcon;
export declare const BookLetterFilled: FluentIcon;
export declare const BookLetterRegular: FluentIcon;
export declare const BookNumberFilled: FluentIcon;
export declare const BookNumberRegular: FluentIcon;
export declare const BookOpenColor: FluentIcon;
export declare const BookOpenFilled: FluentIcon;
export declare const BookOpenRegular: FluentIcon;
export declare const BookOpenGlobeFilled: FluentIcon;
export declare const BookOpenGlobeRegular: FluentIcon;
export declare const BookOpenLightbulbColor: FluentIcon;
export declare const BookOpenLightbulbFilled: FluentIcon;
export declare const BookOpenLightbulbRegular: FluentIcon;
export declare const BookOpenMicrophoneFilled: FluentIcon;
export declare const BookOpenMicrophoneRegular: FluentIcon;
export declare const BookPulseFilled: FluentIcon;
export declare const BookPulseRegular: FluentIcon;
export declare const BookQuestionMarkFilled: FluentIcon;
export declare const BookQuestionMarkRegular: FluentIcon;
export declare const BookQuestionMarkRtlFilled: FluentIcon;
export declare const BookQuestionMarkRtlRegular: FluentIcon;
export declare const BookSearchFilled: FluentIcon;
export declare const BookSearchRegular: FluentIcon;
export declare const BookStarColor: FluentIcon;
export declare const BookStarFilled: FluentIcon;
export declare const BookStarRegular: FluentIcon;
export declare const BookTemplateFilled: FluentIcon;
export declare const BookTemplateRegular: FluentIcon;
export declare const BookThetaFilled: FluentIcon;
export declare const BookThetaRegular: FluentIcon;
export declare const BookToolboxFilled: FluentIcon;
export declare const BookToolboxRegular: FluentIcon;
export declare const BookmarkColor: FluentIcon;
export declare const BookmarkFilled: FluentIcon;
export declare const BookmarkRegular: FluentIcon;
export declare const BookmarkAddFilled: FluentIcon;
export declare const BookmarkAddRegular: FluentIcon;
export declare const BookmarkMultipleFilled: FluentIcon;
export declare const BookmarkMultipleRegular: FluentIcon;
export declare const BookmarkOffFilled: FluentIcon;
export declare const BookmarkOffRegular: FluentIcon;
export declare const BookmarkSearchFilled: FluentIcon;
export declare const BookmarkSearchRegular: FluentIcon;
export declare const BorderAllFilled: FluentIcon;
export declare const BorderAllRegular: FluentIcon;
export declare const BorderBottomFilled: FluentIcon;
export declare const BorderBottomRegular: FluentIcon;
export declare const BorderBottomDoubleFilled: FluentIcon;
export declare const BorderBottomDoubleRegular: FluentIcon;
export declare const BorderBottomThickFilled: FluentIcon;
export declare const BorderBottomThickRegular: FluentIcon;
export declare const BorderInsideFilled: FluentIcon;
export declare const BorderInsideRegular: FluentIcon;
export declare const BorderLeftFilled: FluentIcon;
export declare const BorderLeftRegular: FluentIcon;
export declare const BorderLeftRightFilled: FluentIcon;
export declare const BorderLeftRightRegular: FluentIcon;
export declare const BorderNoneFilled: FluentIcon;
export declare const BorderNoneRegular: FluentIcon;
export declare const BorderOutsideFilled: FluentIcon;
export declare const BorderOutsideRegular: FluentIcon;
export declare const BorderOutsideThickFilled: FluentIcon;
export declare const BorderOutsideThickRegular: FluentIcon;
export declare const BorderRightFilled: FluentIcon;
export declare const BorderRightRegular: FluentIcon;
export declare const BorderTopFilled: FluentIcon;
export declare const BorderTopRegular: FluentIcon;
export declare const BorderTopBottomFilled: FluentIcon;
export declare const BorderTopBottomRegular: FluentIcon;
export declare const BorderTopBottomDoubleFilled: FluentIcon;
export declare const BorderTopBottomDoubleRegular: FluentIcon;
export declare const BorderTopBottomThickFilled: FluentIcon;
export declare const BorderTopBottomThickRegular: FluentIcon;
export declare const BotColor: FluentIcon;
export declare const BotFilled: FluentIcon;
export declare const BotRegular: FluentIcon;
export declare const BotAddFilled: FluentIcon;
export declare const BotAddRegular: FluentIcon;
export declare const BotSparkleColor: FluentIcon;
export declare const BotSparkleFilled: FluentIcon;
export declare const BotSparkleRegular: FluentIcon;
export declare const BowTieFilled: FluentIcon;
export declare const BowTieRegular: FluentIcon;
export declare const BowlChopsticksFilled: FluentIcon;
export declare const BowlChopsticksRegular: FluentIcon;
export declare const BowlSaladFilled: FluentIcon;
export declare const BowlSaladRegular: FluentIcon;
export declare const BoxFilled: FluentIcon;
export declare const BoxRegular: FluentIcon;
export declare const BoxArrowLeftFilled: FluentIcon;
export declare const BoxArrowLeftRegular: FluentIcon;
export declare const BoxArrowUpFilled: FluentIcon;
export declare const BoxArrowUpRegular: FluentIcon;
export declare const BoxCheckmarkFilled: FluentIcon;
export declare const BoxCheckmarkRegular: FluentIcon;
export declare const BoxDismissFilled: FluentIcon;
export declare const BoxDismissRegular: FluentIcon;
export declare const BoxEditFilled: FluentIcon;
export declare const BoxEditRegular: FluentIcon;
export declare const BoxMultipleFilled: FluentIcon;
export declare const BoxMultipleRegular: FluentIcon;
export declare const BoxMultipleArrowLeftFilled: FluentIcon;
export declare const BoxMultipleArrowLeftRegular: FluentIcon;
export declare const BoxMultipleArrowRightFilled: FluentIcon;
export declare const BoxMultipleArrowRightRegular: FluentIcon;
export declare const BoxMultipleCheckmarkFilled: FluentIcon;
export declare const BoxMultipleCheckmarkRegular: FluentIcon;
export declare const BoxMultipleSearchFilled: FluentIcon;
export declare const BoxMultipleSearchRegular: FluentIcon;
export declare const BoxSearchFilled: FluentIcon;
export declare const BoxSearchRegular: FluentIcon;
export declare const BoxToolboxFilled: FluentIcon;
export declare const BoxToolboxRegular: FluentIcon;
export declare const BracesFilled: FluentIcon;
export declare const BracesRegular: FluentIcon;
export declare const BracesVariableFilled: FluentIcon;
export declare const BracesVariableRegular: FluentIcon;
export declare const BrainFilled: FluentIcon;
export declare const BrainRegular: FluentIcon;
export declare const BrainCircuitFilled: FluentIcon;
export declare const BrainCircuitRegular: FluentIcon;
export declare const BrainSparkleFilled: FluentIcon;
export declare const BrainSparkleRegular: FluentIcon;
export declare const BranchFilled: FluentIcon;
export declare const BranchRegular: FluentIcon;
export declare const BranchCompareFilled: FluentIcon;
export declare const BranchCompareRegular: FluentIcon;
export declare const BranchForkFilled: FluentIcon;
export declare const BranchForkRegular: FluentIcon;
export declare const BranchForkHintFilled: FluentIcon;
export declare const BranchForkHintRegular: FluentIcon;
export declare const BranchForkLinkFilled: FluentIcon;
export declare const BranchForkLinkRegular: FluentIcon;
export declare const BranchRequestFilled: FluentIcon;
export declare const BranchRequestRegular: FluentIcon;
export declare const BranchRequestClosedFilled: FluentIcon;
export declare const BranchRequestClosedRegular: FluentIcon;
export declare const BranchRequestDraftFilled: FluentIcon;
export declare const BranchRequestDraftRegular: FluentIcon;
export declare const BreakoutRoomFilled: FluentIcon;
export declare const BreakoutRoomRegular: FluentIcon;
export declare const BriefcaseColor: FluentIcon;
export declare const BriefcaseFilled: FluentIcon;
export declare const BriefcaseRegular: FluentIcon;
export declare const BriefcaseMedicalFilled: FluentIcon;
export declare const BriefcaseMedicalRegular: FluentIcon;
export declare const BriefcaseOffFilled: FluentIcon;
export declare const BriefcaseOffRegular: FluentIcon;
export declare const BriefcaseSearchFilled: FluentIcon;
export declare const BriefcaseSearchRegular: FluentIcon;
export declare const BrightnessHighFilled: FluentIcon;
export declare const BrightnessHighRegular: FluentIcon;
export declare const BrightnessLowFilled: FluentIcon;
export declare const BrightnessLowRegular: FluentIcon;
export declare const BroadActivityFeedFilled: FluentIcon;
export declare const BroadActivityFeedRegular: FluentIcon;
export declare const BroomFilled: FluentIcon;
export declare const BroomRegular: FluentIcon;
export declare const BroomSparkleFilled: FluentIcon;
export declare const BroomSparkleRegular: FluentIcon;
export declare const BubbleMultipleFilled: FluentIcon;
export declare const BubbleMultipleRegular: FluentIcon;
export declare const BugFilled: FluentIcon;
export declare const BugRegular: FluentIcon;
export declare const BugArrowCounterclockwiseFilled: FluentIcon;
export declare const BugArrowCounterclockwiseRegular: FluentIcon;
export declare const BugProhibitedFilled: FluentIcon;
export declare const BugProhibitedRegular: FluentIcon;
export declare const BuildingColor: FluentIcon;
export declare const BuildingFilled: FluentIcon;
export declare const BuildingRegular: FluentIcon;
export declare const BuildingBankFilled: FluentIcon;
export declare const BuildingBankRegular: FluentIcon;
export declare const BuildingBankLinkFilled: FluentIcon;
export declare const BuildingBankLinkRegular: FluentIcon;
export declare const BuildingBankToolboxFilled: FluentIcon;
export declare const BuildingBankToolboxRegular: FluentIcon;
export declare const BuildingCheckmarkFilled: FluentIcon;
export declare const BuildingCheckmarkRegular: FluentIcon;
export declare const BuildingDesktopFilled: FluentIcon;
export declare const BuildingDesktopRegular: FluentIcon;
export declare const BuildingFactoryFilled: FluentIcon;
export declare const BuildingFactoryRegular: FluentIcon;
export declare const BuildingGovernmentColor: FluentIcon;
export declare const BuildingGovernmentFilled: FluentIcon;
export declare const BuildingGovernmentRegular: FluentIcon;
export declare const BuildingGovernmentSearchColor: FluentIcon;
export declare const BuildingGovernmentSearchFilled: FluentIcon;
export declare const BuildingGovernmentSearchRegular: FluentIcon;
export declare const BuildingHomeColor: FluentIcon;
export declare const BuildingHomeFilled: FluentIcon;
export declare const BuildingHomeRegular: FluentIcon;
export declare const BuildingLighthouseFilled: FluentIcon;
export declare const BuildingLighthouseRegular: FluentIcon;
export declare const BuildingMosqueFilled: FluentIcon;
export declare const BuildingMosqueRegular: FluentIcon;
export declare const BuildingMultipleColor: FluentIcon;
export declare const BuildingMultipleFilled: FluentIcon;
export declare const BuildingMultipleRegular: FluentIcon;
export declare const BuildingPeopleColor: FluentIcon;
export declare const BuildingPeopleFilled: FluentIcon;
export declare const BuildingPeopleRegular: FluentIcon;
export declare const BuildingRetailFilled: FluentIcon;
export declare const BuildingRetailRegular: FluentIcon;
export declare const BuildingRetailMoneyFilled: FluentIcon;
export declare const BuildingRetailMoneyRegular: FluentIcon;
export declare const BuildingRetailMoreFilled: FluentIcon;
export declare const BuildingRetailMoreRegular: FluentIcon;
export declare const BuildingRetailShieldFilled: FluentIcon;
export declare const BuildingRetailShieldRegular: FluentIcon;
export declare const BuildingRetailToolboxFilled: FluentIcon;
export declare const BuildingRetailToolboxRegular: FluentIcon;
export declare const BuildingShopFilled: FluentIcon;
export declare const BuildingShopRegular: FluentIcon;
export declare const BuildingSkyscraperFilled: FluentIcon;
export declare const BuildingSkyscraperRegular: FluentIcon;
export declare const BuildingStoreColor: FluentIcon;
export declare const BuildingSwapFilled: FluentIcon;
export declare const BuildingSwapRegular: FluentIcon;
export declare const BuildingTownhouseFilled: FluentIcon;
export declare const BuildingTownhouseRegular: FluentIcon;
export declare const ButtonFilled: FluentIcon;
export declare const ButtonRegular: FluentIcon;
export declare const CalculatorFilled: FluentIcon;
export declare const CalculatorRegular: FluentIcon;
export declare const CalculatorArrowClockwiseFilled: FluentIcon;
export declare const CalculatorArrowClockwiseRegular: FluentIcon;
export declare const CalculatorMultipleFilled: FluentIcon;
export declare const CalculatorMultipleRegular: FluentIcon;
export declare const CalendarColor: FluentIcon;
export declare const CalendarFilled: FluentIcon;
export declare const CalendarRegular: FluentIcon;
export declare const Calendar3DayFilled: FluentIcon;
export declare const Calendar3DayRegular: FluentIcon;
export declare const CalendarAddFilled: FluentIcon;
export declare const CalendarAddRegular: FluentIcon;
export declare const CalendarAgendaFilled: FluentIcon;
export declare const CalendarAgendaRegular: FluentIcon;
export declare const CalendarArrowCounterclockwiseFilled: FluentIcon;
export declare const CalendarArrowCounterclockwiseRegular: FluentIcon;
export declare const CalendarArrowDownFilled: FluentIcon;
export declare const CalendarArrowDownRegular: FluentIcon;
export declare const CalendarArrowRepeatAllFilled: FluentIcon;
export declare const CalendarArrowRepeatAllRegular: FluentIcon;
export declare const CalendarArrowRightFilled: FluentIcon;
export declare const CalendarArrowRightRegular: FluentIcon;
export declare const CalendarAssistantFilled: FluentIcon;
export declare const CalendarAssistantRegular: FluentIcon;
export declare const CalendarCancelColor: FluentIcon;
export declare const CalendarCancelFilled: FluentIcon;
export declare const CalendarCancelRegular: FluentIcon;
export declare const CalendarChatFilled: FluentIcon;
export declare const CalendarChatRegular: FluentIcon;
export declare const CalendarCheckmarkColor: FluentIcon;
export declare const CalendarCheckmarkFilled: FluentIcon;
export declare const CalendarCheckmarkRegular: FluentIcon;
export declare const CalendarCheckmarkCenterFilled: FluentIcon;
export declare const CalendarCheckmarkCenterRegular: FluentIcon;
export declare const CalendarCheckmarkSparkleFilled: FluentIcon;
export declare const CalendarCheckmarkSparkleRegular: FluentIcon;
export declare const CalendarClockColor: FluentIcon;
export declare const CalendarClockFilled: FluentIcon;
export declare const CalendarClockRegular: FluentIcon;
export declare const CalendarDataBarColor: FluentIcon;
export declare const CalendarDataBarFilled: FluentIcon;
export declare const CalendarDataBarRegular: FluentIcon;
export declare const CalendarDateFilled: FluentIcon;
export declare const CalendarDateRegular: FluentIcon;
export declare const CalendarDayFilled: FluentIcon;
export declare const CalendarDayRegular: FluentIcon;
export declare const CalendarEditColor: FluentIcon;
export declare const CalendarEditFilled: FluentIcon;
export declare const CalendarEditRegular: FluentIcon;
export declare const CalendarEmptyFilled: FluentIcon;
export declare const CalendarEmptyRegular: FluentIcon;
export declare const CalendarErrorFilled: FluentIcon;
export declare const CalendarErrorRegular: FluentIcon;
export declare const CalendarEyeFilled: FluentIcon;
export declare const CalendarEyeRegular: FluentIcon;
export declare const CalendarInfoFilled: FluentIcon;
export declare const CalendarInfoRegular: FluentIcon;
export declare const CalendarLockFilled: FluentIcon;
export declare const CalendarLockRegular: FluentIcon;
export declare const CalendarLtrFilled: FluentIcon;
export declare const CalendarLtrRegular: FluentIcon;
export declare const CalendarMailFilled: FluentIcon;
export declare const CalendarMailRegular: FluentIcon;
export declare const CalendarMentionFilled: FluentIcon;
export declare const CalendarMentionRegular: FluentIcon;
export declare const CalendarMonthFilled: FluentIcon;
export declare const CalendarMonthRegular: FluentIcon;
export declare const CalendarMultipleFilled: FluentIcon;
export declare const CalendarMultipleRegular: FluentIcon;
export declare const CalendarNoteFilled: FluentIcon;
export declare const CalendarNoteRegular: FluentIcon;
export declare const CalendarPatternFilled: FluentIcon;
export declare const CalendarPatternRegular: FluentIcon;
export declare const CalendarPeopleColor: FluentIcon;
export declare const CalendarPersonFilled: FluentIcon;
export declare const CalendarPersonRegular: FluentIcon;
export declare const CalendarPhoneFilled: FluentIcon;
export declare const CalendarPhoneRegular: FluentIcon;
export declare const CalendarPlayFilled: FluentIcon;
export declare const CalendarPlayRegular: FluentIcon;
export declare const CalendarQuestionMarkFilled: FluentIcon;
export declare const CalendarQuestionMarkRegular: FluentIcon;
export declare const CalendarRecordFilled: FluentIcon;
export declare const CalendarRecordRegular: FluentIcon;
export declare const CalendarReplyFilled: FluentIcon;
export declare const CalendarReplyRegular: FluentIcon;
export declare const CalendarRtlFilled: FluentIcon;
export declare const CalendarRtlRegular: FluentIcon;
export declare const CalendarSearchFilled: FluentIcon;
export declare const CalendarSearchRegular: FluentIcon;
export declare const CalendarSettingsFilled: FluentIcon;
export declare const CalendarSettingsRegular: FluentIcon;
export declare const CalendarShieldFilled: FluentIcon;
export declare const CalendarShieldRegular: FluentIcon;
export declare const CalendarSparkleFilled: FluentIcon;
export declare const CalendarSparkleRegular: FluentIcon;
export declare const CalendarStarFilled: FluentIcon;
export declare const CalendarStarRegular: FluentIcon;
export declare const CalendarSyncColor: FluentIcon;
export declare const CalendarSyncFilled: FluentIcon;
export declare const CalendarSyncRegular: FluentIcon;
export declare const CalendarTemplateFilled: FluentIcon;
export declare const CalendarTemplateRegular: FluentIcon;
export declare const CalendarTodayFilled: FluentIcon;
export declare const CalendarTodayRegular: FluentIcon;
export declare const CalendarToolboxFilled: FluentIcon;
export declare const CalendarToolboxRegular: FluentIcon;
export declare const CalendarVideoFilled: FluentIcon;
export declare const CalendarVideoRegular: FluentIcon;
export declare const CalendarWeekNumbersFilled: FluentIcon;
export declare const CalendarWeekNumbersRegular: FluentIcon;
export declare const CalendarWeekStartFilled: FluentIcon;
export declare const CalendarWeekStartRegular: FluentIcon;
export declare const CalendarWorkWeekFilled: FluentIcon;
export declare const CalendarWorkWeekRegular: FluentIcon;
export declare const CallFilled: FluentIcon;
export declare const CallRegular: FluentIcon;
export declare const CallAddFilled: FluentIcon;
export declare const CallAddRegular: FluentIcon;
export declare const CallCheckmarkFilled: FluentIcon;
export declare const CallCheckmarkRegular: FluentIcon;
export declare const CallConnectingFilled: FluentIcon;
export declare const CallConnectingRegular: FluentIcon;
export declare const CallDismissFilled: FluentIcon;
export declare const CallDismissRegular: FluentIcon;
export declare const CallEndFilled: FluentIcon;
export declare const CallEndRegular: FluentIcon;
export declare const CallExclamationFilled: FluentIcon;
export declare const CallExclamationRegular: FluentIcon;
export declare const CallForwardFilled: FluentIcon;
export declare const CallForwardRegular: FluentIcon;
export declare const CallInboundFilled: FluentIcon;
export declare const CallInboundRegular: FluentIcon;
export declare const CallMissedFilled: FluentIcon;
export declare const CallMissedRegular: FluentIcon;
export declare const CallOutboundFilled: FluentIcon;
export declare const CallOutboundRegular: FluentIcon;
export declare const CallParkFilled: FluentIcon;
export declare const CallParkRegular: FluentIcon;
export declare const CallPauseFilled: FluentIcon;
export declare const CallPauseRegular: FluentIcon;
export declare const CallProhibitedFilled: FluentIcon;
export declare const CallProhibitedRegular: FluentIcon;
export declare const CallRectangleLandscapeFilled: FluentIcon;
export declare const CallRectangleLandscapeRegular: FluentIcon;
export declare const CallSquareFilled: FluentIcon;
export declare const CallSquareRegular: FluentIcon;
export declare const CallTransferFilled: FluentIcon;
export declare const CallTransferRegular: FluentIcon;
export declare const CallWarningFilled: FluentIcon;
export declare const CallWarningRegular: FluentIcon;
export declare const CalligraphyPenFilled: FluentIcon;
export declare const CalligraphyPenRegular: FluentIcon;
export declare const CalligraphyPenCheckmarkFilled: FluentIcon;
export declare const CalligraphyPenCheckmarkRegular: FluentIcon;
export declare const CalligraphyPenErrorFilled: FluentIcon;
export declare const CalligraphyPenErrorRegular: FluentIcon;
export declare const CalligraphyPenQuestionMarkFilled: FluentIcon;
export declare const CalligraphyPenQuestionMarkRegular: FluentIcon;
export declare const CameraColor: FluentIcon;
export declare const CameraFilled: FluentIcon;
export declare const CameraRegular: FluentIcon;
export declare const CameraAddFilled: FluentIcon;
export declare const CameraAddRegular: FluentIcon;
export declare const CameraArrowUpFilled: FluentIcon;
export declare const CameraArrowUpRegular: FluentIcon;
export declare const CameraDomeFilled: FluentIcon;
export declare const CameraDomeRegular: FluentIcon;
export declare const CameraEditFilled: FluentIcon;
export declare const CameraEditRegular: FluentIcon;
export declare const CameraOffFilled: FluentIcon;
export declare const CameraOffRegular: FluentIcon;
export declare const CameraSparklesFilled: FluentIcon;
export declare const CameraSparklesRegular: FluentIcon;
export declare const CameraSwitchFilled: FluentIcon;
export declare const CameraSwitchRegular: FluentIcon;
export declare const CardUiFilled: FluentIcon;
export declare const CardUiRegular: FluentIcon;
export declare const CardUiPortraitFlipFilled: FluentIcon;
export declare const CardUiPortraitFlipRegular: FluentIcon;
export declare const CaretDownFilled: FluentIcon;
export declare const CaretDownRegular: FluentIcon;
export declare const CaretDownRightFilled: FluentIcon;
export declare const CaretDownRightRegular: FluentIcon;
export declare const CaretLeftFilled: FluentIcon;
export declare const CaretLeftRegular: FluentIcon;
export declare const CaretRightFilled: FluentIcon;
export declare const CaretRightRegular: FluentIcon;
export declare const CaretUpFilled: FluentIcon;
export declare const CaretUpRegular: FluentIcon;
export declare const CartFilled: FluentIcon;
export declare const CartRegular: FluentIcon;
export declare const CastFilled: FluentIcon;
export declare const CastRegular: FluentIcon;
export declare const CastMultipleFilled: FluentIcon;
export declare const CastMultipleRegular: FluentIcon;
