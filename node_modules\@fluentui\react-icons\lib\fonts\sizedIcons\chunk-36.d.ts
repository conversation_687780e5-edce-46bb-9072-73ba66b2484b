import type { FluentFontIcon } from "../../utils/fonts/createFluentFontIcon";
export declare const VehicleTruckCheckmark48Regular: FluentFontIcon;
export declare const VehicleTruckProfile28Regular: FluentFontIcon;
export declare const VehicleTruckProfile32Regular: FluentFontIcon;
export declare const VehicleTruckProfile48Regular: FluentFontIcon;
export declare const VideoMultiple16Regular: FluentFontIcon;
export declare const VideoMultiple20Regular: FluentFontIcon;
export declare const VideoMultiple24Regular: FluentFontIcon;
export declare const VideoMultiple28Regular: FluentFontIcon;
export declare const VideoMultiple32Regular: FluentFontIcon;
export declare const VideoMultiple48Regular: FluentFontIcon;
export declare const VideoSettings16Regular: FluentFontIcon;
export declare const VideoSettings20Regular: FluentFontIcon;
export declare const VideoSettings24Regular: FluentFontIcon;
export declare const VideoSettings28Regular: FluentFontIcon;
export declare const VideoSettings32Regular: FluentFontIcon;
export declare const VideoSettings48Regular: FluentFontIcon;
export declare const Vote16Regular: FluentFontIcon;
export declare const WindowText16Regular: FluentFontIcon;
export declare const WindowText24Regular: FluentFontIcon;
export declare const WindowText28Regular: FluentFontIcon;
export declare const AddStarburst16Regular: FluentFontIcon;
export declare const AddStarburst20Regular: FluentFontIcon;
export declare const AddStarburst24Regular: FluentFontIcon;
export declare const AddStarburst28Regular: FluentFontIcon;
export declare const AddStarburst32Regular: FluentFontIcon;
export declare const AddStarburst48Regular: FluentFontIcon;
export declare const ArrowExit12Regular: FluentFontIcon;
export declare const ArrowExit16Regular: FluentFontIcon;
export declare const ArrowExit24Regular: FluentFontIcon;
export declare const ArrowExit28Regular: FluentFontIcon;
export declare const ArrowExit32Regular: FluentFontIcon;
export declare const ArrowExit48Regular: FluentFontIcon;
export declare const Box28Regular: FluentFontIcon;
export declare const Box32Regular: FluentFontIcon;
export declare const Box48Regular: FluentFontIcon;
export declare const BoxCheckmark16Regular: FluentFontIcon;
export declare const BoxCheckmark28Regular: FluentFontIcon;
export declare const BoxCheckmark32Regular: FluentFontIcon;
export declare const BoxCheckmark48Regular: FluentFontIcon;
export declare const DataLine16Regular: FluentFontIcon;
export declare const FlashPlay32Regular: FluentFontIcon;
export declare const Planet16Regular: FluentFontIcon;
export declare const Planet20Regular: FluentFontIcon;
export declare const Planet24Regular: FluentFontIcon;
export declare const Planet32Regular: FluentFontIcon;
export declare const RectanglePortrait12Regular: FluentFontIcon;
export declare const RectanglePortrait16Regular: FluentFontIcon;
export declare const RectanglePortrait20Regular: FluentFontIcon;
export declare const RectanglePortrait24Regular: FluentFontIcon;
export declare const RectanglePortrait28Regular: FluentFontIcon;
export declare const RectanglePortrait32Regular: FluentFontIcon;
export declare const RectanglePortrait48Regular: FluentFontIcon;
export declare const SlideTextTitle16Regular: FluentFontIcon;
export declare const SlideTextTitle20Regular: FluentFontIcon;
export declare const SlideTextTitle24Regular: FluentFontIcon;
export declare const SlideTextTitleAdd16Regular: FluentFontIcon;
export declare const SlideTextTitleAdd20Regular: FluentFontIcon;
export declare const SlideTextTitleAdd24Regular: FluentFontIcon;
export declare const SlideTextTitleCheckmark16Regular: FluentFontIcon;
export declare const SlideTextTitleCheckmark20Regular: FluentFontIcon;
export declare const SlideTextTitleCheckmark24Regular: FluentFontIcon;
export declare const SlideTextTitleEdit16Regular: FluentFontIcon;
export declare const SlideTextTitleEdit20Regular: FluentFontIcon;
export declare const SlideTextTitleEdit24Regular: FluentFontIcon;
export declare const TableArrowRepeatAll20Regular: FluentFontIcon;
export declare const TableArrowRepeatAll24Regular: FluentFontIcon;
export declare const TableArrowRepeatAll28Regular: FluentFontIcon;
export declare const TableCellCenter16Regular: FluentFontIcon;
export declare const TableCellCenter20Regular: FluentFontIcon;
export declare const TableCellCenter24Regular: FluentFontIcon;
export declare const TableCellCenter28Regular: FluentFontIcon;
export declare const TableCellCenterArrowRepeatAll20Regular: FluentFontIcon;
export declare const TableCellCenterArrowRepeatAll24Regular: FluentFontIcon;
export declare const TableCellCenterArrowRepeatAll28Regular: FluentFontIcon;
export declare const TableCellCenterEdit16Regular: FluentFontIcon;
export declare const TableCellCenterEdit20Regular: FluentFontIcon;
export declare const TableCellCenterEdit24Regular: FluentFontIcon;
export declare const TableCellCenterEdit28Regular: FluentFontIcon;
export declare const TableCellCenterLink20Regular: FluentFontIcon;
export declare const TableCellCenterLink24Regular: FluentFontIcon;
export declare const TableCellCenterLink28Regular: FluentFontIcon;
export declare const TableCellCenterSearch20Regular: FluentFontIcon;
export declare const TableCellCenterSearch24Regular: FluentFontIcon;
export declare const TableCellCenterSearch28Regular: FluentFontIcon;
export declare const TableColumnTopBottomArrowRepeatAll20Regular: FluentFontIcon;
export declare const TableColumnTopBottomArrowRepeatAll24Regular: FluentFontIcon;
export declare const TableColumnTopBottomArrowRepeatAll28Regular: FluentFontIcon;
export declare const TableColumnTopBottomLink20Regular: FluentFontIcon;
export declare const TableColumnTopBottomLink24Regular: FluentFontIcon;
export declare const TableColumnTopBottomLink28Regular: FluentFontIcon;
export declare const TableColumnTopBottomSearch20Regular: FluentFontIcon;
export declare const TableColumnTopBottomSearch24Regular: FluentFontIcon;
export declare const TableColumnTopBottomSearch28Regular: FluentFontIcon;
export declare const TableSearch24Regular: FluentFontIcon;
export declare const TableSearch28Regular: FluentFontIcon;
export declare const Tag48Regular: FluentFontIcon;
export declare const TagAdd16Regular: FluentFontIcon;
export declare const TagAdd20Regular: FluentFontIcon;
export declare const TagAdd24Regular: FluentFontIcon;
export declare const TagAdd28Regular: FluentFontIcon;
export declare const TagAdd32Regular: FluentFontIcon;
export declare const TagAdd48Regular: FluentFontIcon;
export declare const TagEdit16Regular: FluentFontIcon;
export declare const TagEdit20Regular: FluentFontIcon;
export declare const TagEdit24Regular: FluentFontIcon;
export declare const TagEdit28Regular: FluentFontIcon;
export declare const TagEdit32Regular: FluentFontIcon;
export declare const TagEdit48Regular: FluentFontIcon;
export declare const TagPercent16Regular: FluentFontIcon;
export declare const TagPercent20Regular: FluentFontIcon;
export declare const TagPercent24Regular: FluentFontIcon;
export declare const TagPercent28Regular: FluentFontIcon;
export declare const TagPercent32Regular: FluentFontIcon;
export declare const TagPercent48Regular: FluentFontIcon;
export declare const TextPercent16Regular: FluentFontIcon;
export declare const TextPercent20Regular: FluentFontIcon;
export declare const TextPercent24Regular: FluentFontIcon;
export declare const TextPercent28Regular: FluentFontIcon;
export declare const TextPercent32Regular: FluentFontIcon;
export declare const TextPercent48Regular: FluentFontIcon;
export declare const CircleMultipleHintCheckmark48Regular: FluentFontIcon;
export declare const Connected28Regular: FluentFontIcon;
export declare const Connected48Regular: FluentFontIcon;
export declare const MailTemplate28Regular: FluentFontIcon;
export declare const MailTemplate32Regular: FluentFontIcon;
export declare const MailTemplate48Regular: FluentFontIcon;
export declare const PeopleChat28Regular: FluentFontIcon;
export declare const PeopleChat32Regular: FluentFontIcon;
export declare const PeopleChat48Regular: FluentFontIcon;
export declare const PersonAccount16Regular: FluentFontIcon;
export declare const PersonAdd48Regular: FluentFontIcon;
export declare const PersonBriefcase16Regular: FluentFontIcon;
export declare const PersonBriefcase20Regular: FluentFontIcon;
export declare const PersonBriefcase24Regular: FluentFontIcon;
export declare const PersonError16Regular: FluentFontIcon;
export declare const PersonError20Regular: FluentFontIcon;
export declare const PersonError24Regular: FluentFontIcon;
export declare const PersonHeart28Regular: FluentFontIcon;
export declare const PersonHeart48Regular: FluentFontIcon;
export declare const ShareIos32Regular: FluentFontIcon;
export declare const TextHeader420Regular: FluentFontIcon;
export declare const TextHeader424Regular: FluentFontIcon;
export declare const TextHeader520Regular: FluentFontIcon;
export declare const TextHeader524Regular: FluentFontIcon;
export declare const TextHeader620Regular: FluentFontIcon;
export declare const TextHeader624Regular: FluentFontIcon;
export declare const BarcodeScanner16Regular: FluentFontIcon;
export declare const BarcodeScanner28Regular: FluentFontIcon;
export declare const BarcodeScanner32Regular: FluentFontIcon;
export declare const BarcodeScanner48Regular: FluentFontIcon;
export declare const BarcodeScannerAdd16Regular: FluentFontIcon;
export declare const BarcodeScannerAdd20Regular: FluentFontIcon;
export declare const BarcodeScannerAdd24Regular: FluentFontIcon;
export declare const BarcodeScannerAdd28Regular: FluentFontIcon;
export declare const BarcodeScannerAdd32Regular: FluentFontIcon;
export declare const BarcodeScannerAdd48Regular: FluentFontIcon;
export declare const BarcodeScannerDismiss16Regular: FluentFontIcon;
export declare const BarcodeScannerDismiss20Regular: FluentFontIcon;
export declare const BarcodeScannerDismiss24Regular: FluentFontIcon;
export declare const BarcodeScannerDismiss28Regular: FluentFontIcon;
export declare const BarcodeScannerDismiss32Regular: FluentFontIcon;
export declare const BarcodeScannerDismiss48Regular: FluentFontIcon;
export declare const DataBarVerticalEdit16Regular: FluentFontIcon;
export declare const DataBarVerticalEdit20Regular: FluentFontIcon;
export declare const DataBarVerticalEdit24Regular: FluentFontIcon;
export declare const Notepad48Regular: FluentFontIcon;
export declare const NotepadPerson28Regular: FluentFontIcon;
export declare const NotepadPerson32Regular: FluentFontIcon;
export declare const NotepadPerson48Regular: FluentFontIcon;
export declare const NotepadPersonOff16Regular: FluentFontIcon;
export declare const NotepadPersonOff20Regular: FluentFontIcon;
export declare const NotepadPersonOff24Regular: FluentFontIcon;
export declare const NotepadPersonOff28Regular: FluentFontIcon;
export declare const NotepadPersonOff32Regular: FluentFontIcon;
export declare const NotepadPersonOff48Regular: FluentFontIcon;
export declare const TaskListSquareSparkle16Regular: FluentFontIcon;
export declare const TaskListSquareSparkle20Regular: FluentFontIcon;
export declare const TaskListSquareSparkle24Regular: FluentFontIcon;
export declare const TextProofingToolsAbc16Regular: FluentFontIcon;
export declare const TextProofingToolsGaNaDa16Regular: FluentFontIcon;
export declare const TextProofingToolsZi16Regular: FluentFontIcon;
export declare const TooltipQuote16Regular: FluentFontIcon;
export declare const TooltipQuote28Regular: FluentFontIcon;
export declare const TooltipQuote32Regular: FluentFontIcon;
export declare const TooltipQuote48Regular: FluentFontIcon;
export declare const TooltipQuoteOff16Regular: FluentFontIcon;
export declare const TooltipQuoteOff20Regular: FluentFontIcon;
export declare const TooltipQuoteOff24Regular: FluentFontIcon;
export declare const TooltipQuoteOff28Regular: FluentFontIcon;
export declare const TooltipQuoteOff32Regular: FluentFontIcon;
export declare const TooltipQuoteOff48Regular: FluentFontIcon;
export declare const BroomSparkle20Regular: FluentFontIcon;
export declare const MathFormulaSparkle20Regular: FluentFontIcon;
export declare const ProjectionScreenTextSparkle20Regular: FluentFontIcon;
export declare const SparkleAction20Regular: FluentFontIcon;
export declare const ArrowCircleDownRight12Regular: FluentFontIcon;
export declare const ArrowClockwiseDashes12Regular: FluentFontIcon;
export declare const BookOpenLightbulb20Regular: FluentFontIcon;
export declare const BookOpenLightbulb24Regular: FluentFontIcon;
export declare const BookOpenLightbulb32Regular: FluentFontIcon;
export declare const BroomSparkle16Regular: FluentFontIcon;
export declare const BuildingMultiple16Regular: FluentFontIcon;
export declare const DiamondDismiss12Regular: FluentFontIcon;
export declare const FlowSparkle24Regular: FluentFontIcon;
export declare const Incognito32Regular: FluentFontIcon;
export declare const Incognito48Regular: FluentFontIcon;
export declare const MathFormulaSparkle16Regular: FluentFontIcon;
export declare const PauseCircle12Regular: FluentFontIcon;
export declare const PersonEdit32Regular: FluentFontIcon;
export declare const ProjectionScreenText16Regular: FluentFontIcon;
export declare const ProjectionScreenTextSparkle16Regular: FluentFontIcon;
export declare const ShieldSettings16Regular: FluentFontIcon;
export declare const ShieldSettings20Regular: FluentFontIcon;
export declare const ShieldSettings24Regular: FluentFontIcon;
export declare const ShieldSettings28Regular: FluentFontIcon;
export declare const SparkleAction16Regular: FluentFontIcon;
export declare const TextHeader4LinesCaret16Regular: FluentFontIcon;
export declare const TextHeader4LinesCaret20Regular: FluentFontIcon;
export declare const TextHeader4LinesCaret24Regular: FluentFontIcon;
export declare const ArrowHookDownLeft32Regular: FluentFontIcon;
export declare const ArrowHookDownRight32Regular: FluentFontIcon;
export declare const ArrowHookUpLeft32Regular: FluentFontIcon;
export declare const ArrowHookUpRight32Regular: FluentFontIcon;
export declare const AutoFitHeight28Regular: FluentFontIcon;
export declare const AutoFitHeight32Regular: FluentFontIcon;
export declare const AutoFitWidth28Regular: FluentFontIcon;
export declare const AutoFitWidth32Regular: FluentFontIcon;
export declare const BookContacts16Regular: FluentFontIcon;
export declare const BookContacts48Regular: FluentFontIcon;
export declare const Brain28Regular: FluentFontIcon;
export declare const Brain32Regular: FluentFontIcon;
export declare const Brain48Regular: FluentFontIcon;
export declare const BrainCircuit28Regular: FluentFontIcon;
export declare const BrainCircuit32Regular: FluentFontIcon;
export declare const BrainCircuit48Regular: FluentFontIcon;
export declare const BreakoutRoom16Regular: FluentFontIcon;
export declare const CloudDesktop24Regular: FluentFontIcon;
export declare const Cut28Regular: FluentFontIcon;
export declare const Cut32Regular: FluentFontIcon;
export declare const Cut48Regular: FluentFontIcon;
export declare const Door24Regular: FluentFontIcon;
export declare const Door32Regular: FluentFontIcon;
export declare const DoorArrowRight32Regular: FluentFontIcon;
export declare const ImmersiveReader32Regular: FluentFontIcon;
export declare const ImmersiveReader48Regular: FluentFontIcon;
export declare const PeopleSettings32Regular: FluentFontIcon;
export declare const Share32Regular: FluentFontIcon;
export declare const SkipBack1528Regular: FluentFontIcon;
export declare const SkipBack1532Regular: FluentFontIcon;
export declare const SkipBack1548Regular: FluentFontIcon;
export declare const SkipForward1528Regular: FluentFontIcon;
export declare const SkipForward1532Regular: FluentFontIcon;
export declare const SkipForward1548Regular: FluentFontIcon;
export declare const StarAdd32Regular: FluentFontIcon;
export declare const HexagonSparkle16Regular: FluentFontIcon;
export declare const HexagonSparkle28Regular: FluentFontIcon;
export declare const HexagonSparkle32Regular: FluentFontIcon;
export declare const HexagonSparkle48Regular: FluentFontIcon;
export declare const PeopleInterwoven16Regular: FluentFontIcon;
export declare const PeopleInterwoven20Regular: FluentFontIcon;
export declare const PeopleInterwoven24Regular: FluentFontIcon;
export declare const PeopleInterwoven28Regular: FluentFontIcon;
export declare const PeopleInterwoven32Regular: FluentFontIcon;
export declare const PeopleInterwoven48Regular: FluentFontIcon;
export declare const AgentsAdd20Regular: FluentFontIcon;
export declare const AgentsAdd24Regular: FluentFontIcon;
export declare const ArrowExpand28Regular: FluentFontIcon;
export declare const CalendarDay32Regular: FluentFontIcon;
export declare const CalendarWorkWeek32Regular: FluentFontIcon;
export declare const DeskSparkle20Regular: FluentFontIcon;
export declare const DeskSparkle24Regular: FluentFontIcon;
export declare const MailList32Regular: FluentFontIcon;
export declare const MegaphoneLoud48Regular: FluentFontIcon;
export declare const PasswordClock16Regular: FluentFontIcon;
export declare const PasswordClock20Regular: FluentFontIcon;
export declare const PasswordClock24Regular: FluentFontIcon;
export declare const PersonShield16Regular: FluentFontIcon;
export declare const PersonShield20Regular: FluentFontIcon;
export declare const PersonShield24Regular: FluentFontIcon;
export declare const PersonShield28Regular: FluentFontIcon;
export declare const PersonShield32Regular: FluentFontIcon;
export declare const PersonShield48Regular: FluentFontIcon;
export declare const PictureInPicture28Regular: FluentFontIcon;
export declare const PictureInPicture32Regular: FluentFontIcon;
export declare const ShieldError28Regular: FluentFontIcon;
export declare const ShieldError32Regular: FluentFontIcon;
export declare const ShieldError48Regular: FluentFontIcon;
export declare const Sticker16Regular: FluentFontIcon;
export declare const Sticker28Regular: FluentFontIcon;
export declare const Sticker32Regular: FluentFontIcon;
export declare const TargetSparkle16Regular: FluentFontIcon;
export declare const TargetSparkle20Regular: FluentFontIcon;
export declare const TargetSparkle24Regular: FluentFontIcon;
export declare const TextExpand28Regular: FluentFontIcon;
export declare const TextExpand32Regular: FluentFontIcon;
export declare const ZoomIn32Regular: FluentFontIcon;
export declare const ZoomOut32Regular: FluentFontIcon;
export declare const DeskSparkle16Regular: FluentFontIcon;
export declare const FlowDot20Regular: FluentFontIcon;
export declare const FlowDot24Regular: FluentFontIcon;
export declare const SlideTopicAdd16Regular: FluentFontIcon;
export declare const SlideTopicAdd20Regular: FluentFontIcon;
export declare const SlideTopicAdd32Regular: FluentFontIcon;
export declare const SparkleAction24Regular: FluentFontIcon;
export declare const CalendarCheckmarkSparkle16Regular: FluentFontIcon;
export declare const CalendarCheckmarkSparkle20Regular: FluentFontIcon;
export declare const CalendarCheckmarkSparkle24Regular: FluentFontIcon;
export declare const CalendarCheckmarkSparkle28Regular: FluentFontIcon;
export declare const CalendarCheckmarkSparkle32Regular: FluentFontIcon;
export declare const CalendarCheckmarkSparkle48Regular: FluentFontIcon;
export declare const CalendarMonth16Regular: FluentFontIcon;
export declare const DocumentSquare16Regular: FluentFontIcon;
export declare const DocumentSquare20Regular: FluentFontIcon;
export declare const DocumentSquare24Regular: FluentFontIcon;
export declare const DocumentSquare28Regular: FluentFontIcon;
export declare const DocumentSquare32Regular: FluentFontIcon;
export declare const DocumentSquare48Regular: FluentFontIcon;
export declare const FlowDot16Regular: FluentFontIcon;
export declare const FormMultipleCollection20Regular: FluentFontIcon;
export declare const FormMultipleCollection24Regular: FluentFontIcon;
export declare const FormMultipleCollection32Regular: FluentFontIcon;
export declare const TaskListSquareLtr48Regular: FluentFontIcon;
export declare const TaskListSquarePerson24Regular: FluentFontIcon;
export declare const TaskListSquarePerson48Regular: FluentFontIcon;
export declare const TextQuote28Regular: FluentFontIcon;
export declare const TextQuote32Regular: FluentFontIcon;
export declare const TextQuoteOpening16Regular: FluentFontIcon;
export declare const TextQuoteOpening20Regular: FluentFontIcon;
export declare const TextQuoteOpening24Regular: FluentFontIcon;
export declare const TextQuoteOpening28Regular: FluentFontIcon;
export declare const TextQuoteOpening32Regular: FluentFontIcon;
export declare const TopSpeed16Regular: FluentFontIcon;
export declare const TransparencySquare16Regular: FluentFontIcon;
export declare const Bot12Regular: FluentFontIcon;
export declare const CalendarCheckmarkCenter16Regular: FluentFontIcon;
export declare const CalendarCheckmarkCenter20Regular: FluentFontIcon;
export declare const CalendarCheckmarkCenter24Regular: FluentFontIcon;
export declare const CalendarCheckmarkCenter28Regular: FluentFontIcon;
export declare const CalendarCheckmarkCenter32Regular: FluentFontIcon;
export declare const CalendarCheckmarkCenter48Regular: FluentFontIcon;
export declare const CheckmarkCircleHint16Regular: FluentFontIcon;
export declare const CheckmarkCircleHint20Regular: FluentFontIcon;
export declare const CheckmarkCircleHint24Regular: FluentFontIcon;
export declare const Chess16Regular: FluentFontIcon;
export declare const Chess24Regular: FluentFontIcon;
export declare const CompassTrueNorth16Regular: FluentFontIcon;
export declare const CompassTrueNorth20Regular: FluentFontIcon;
export declare const CompassTrueNorth24Regular: FluentFontIcon;
export declare const DocumentCode16Regular: FluentFontIcon;
export declare const DocumentGlobe16Regular: FluentFontIcon;
export declare const DocumentPdf28Regular: FluentFontIcon;
export declare const LayoutDynamic20Regular: FluentFontIcon;
export declare const LayoutDynamic24Regular: FluentFontIcon;
export declare const MicRecord16Regular: FluentFontIcon;
export declare const MicSync16Regular: FluentFontIcon;
export declare const MicSync24Regular: FluentFontIcon;
export declare const MicSync28Regular: FluentFontIcon;
export declare const MicSync32Regular: FluentFontIcon;
export declare const MicSync48Regular: FluentFontIcon;
export declare const PanelLeftDefault28Regular: FluentFontIcon;
export declare const PanelRightContract28Regular: FluentFontIcon;
export declare const PanelRightDefault28Regular: FluentFontIcon;
export declare const PanelRightExpand28Regular: FluentFontIcon;
export declare const PeopleCheckmark32Regular: FluentFontIcon;
export declare const PersonGuest16Regular: FluentFontIcon;
export declare const PersonGuest20Regular: FluentFontIcon;
export declare const PersonGuest24Regular: FluentFontIcon;
export declare const RenameA20Regular: FluentFontIcon;
export declare const TooltipQuote12Regular: FluentFontIcon;
export declare const TooltipQuoteOff12Regular: FluentFontIcon;
export declare const WheelchairAccess16Regular: FluentFontIcon;
export declare const WheelchairAccess20Regular: FluentFontIcon;
export declare const WheelchairAccess24Regular: FluentFontIcon;
export declare const Drafts28Regular: FluentFontIcon;
export declare const EditLineHorizontal328Regular: FluentFontIcon;
export declare const PeopleCommunication16Regular: FluentFontIcon;
export declare const PeopleCommunication20Regular: FluentFontIcon;
export declare const PeopleCommunication24Regular: FluentFontIcon;
export declare const PeopleCommunication32Regular: FluentFontIcon;
export declare const TableFreezeColumnAndRowDismiss20Regular: FluentFontIcon;
export declare const TableFreezeColumnAndRowDismiss24Regular: FluentFontIcon;
export declare const TableFreezeColumnDismiss20Regular: FluentFontIcon;
export declare const TableFreezeColumnDismiss24Regular: FluentFontIcon;
export declare const TableFreezeRowDismiss20Regular: FluentFontIcon;
export declare const TableFreezeRowDismiss24Regular: FluentFontIcon;
