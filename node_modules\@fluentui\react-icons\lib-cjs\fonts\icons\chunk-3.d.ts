import type { FluentFontIcon } from "../../utils/fonts/createFluentFontIcon";
export declare const DataBarVerticalAddFilled: FluentFontIcon;
export declare const DataBarVerticalAddRegular: FluentFontIcon;
export declare const DataBarVerticalArrowDownFilled: FluentFontIcon;
export declare const DataBarVerticalArrowDownRegular: FluentFontIcon;
export declare const DataBarVerticalAscendingFilled: FluentFontIcon;
export declare const DataBarVerticalAscendingRegular: FluentFontIcon;
export declare const DataBarVerticalEditFilled: FluentFontIcon;
export declare const DataBarVerticalEditRegular: FluentFontIcon;
export declare const DataBarVerticalStarFilled: FluentFontIcon;
export declare const DataBarVerticalStarRegular: FluentFontIcon;
export declare const DataFunnelFilled: FluentFontIcon;
export declare const DataFunnelRegular: FluentFontIcon;
export declare const DataHistogramFilled: FluentFontIcon;
export declare const DataHistogramRegular: FluentFontIcon;
export declare const DataLineFilled: FluentFontIcon;
export declare const DataLineRegular: FluentFontIcon;
export declare const DataPieFilled: FluentFontIcon;
export declare const DataPieRegular: FluentFontIcon;
export declare const DataScatterFilled: FluentFontIcon;
export declare const DataScatterRegular: FluentFontIcon;
export declare const DataSunburstFilled: FluentFontIcon;
export declare const DataSunburstRegular: FluentFontIcon;
export declare const DataTreemapFilled: FluentFontIcon;
export declare const DataTreemapRegular: FluentFontIcon;
export declare const DataTrendingFilled: FluentFontIcon;
export declare const DataTrendingRegular: FluentFontIcon;
export declare const DataUsageFilled: FluentFontIcon;
export declare const DataUsageRegular: FluentFontIcon;
export declare const DataUsageCheckmarkFilled: FluentFontIcon;
export declare const DataUsageCheckmarkRegular: FluentFontIcon;
export declare const DataUsageEditFilled: FluentFontIcon;
export declare const DataUsageEditRegular: FluentFontIcon;
export declare const DataUsageSettingsFilled: FluentFontIcon;
export declare const DataUsageSettingsRegular: FluentFontIcon;
export declare const DataUsageSparkleFilled: FluentFontIcon;
export declare const DataUsageSparkleRegular: FluentFontIcon;
export declare const DataUsageToolboxFilled: FluentFontIcon;
export declare const DataUsageToolboxRegular: FluentFontIcon;
export declare const DataWaterfallFilled: FluentFontIcon;
export declare const DataWaterfallRegular: FluentFontIcon;
export declare const DataWhiskerFilled: FluentFontIcon;
export declare const DataWhiskerRegular: FluentFontIcon;
export declare const DatabaseFilled: FluentFontIcon;
export declare const DatabaseRegular: FluentFontIcon;
export declare const DatabaseArrowDownFilled: FluentFontIcon;
export declare const DatabaseArrowDownRegular: FluentFontIcon;
export declare const DatabaseArrowRightFilled: FluentFontIcon;
export declare const DatabaseArrowRightRegular: FluentFontIcon;
export declare const DatabaseArrowUpFilled: FluentFontIcon;
export declare const DatabaseArrowUpRegular: FluentFontIcon;
export declare const DatabaseCheckmarkFilled: FluentFontIcon;
export declare const DatabaseCheckmarkRegular: FluentFontIcon;
export declare const DatabaseLightningFilled: FluentFontIcon;
export declare const DatabaseLightningRegular: FluentFontIcon;
export declare const DatabaseLinkFilled: FluentFontIcon;
export declare const DatabaseLinkRegular: FluentFontIcon;
export declare const DatabaseMultipleFilled: FluentFontIcon;
export declare const DatabaseMultipleRegular: FluentFontIcon;
export declare const DatabasePersonFilled: FluentFontIcon;
export declare const DatabasePersonRegular: FluentFontIcon;
export declare const DatabasePlugConnectedFilled: FluentFontIcon;
export declare const DatabasePlugConnectedRegular: FluentFontIcon;
export declare const DatabaseSearchFilled: FluentFontIcon;
export declare const DatabaseSearchRegular: FluentFontIcon;
export declare const DatabaseSwitchFilled: FluentFontIcon;
export declare const DatabaseSwitchRegular: FluentFontIcon;
export declare const DatabaseWarningFilled: FluentFontIcon;
export declare const DatabaseWarningRegular: FluentFontIcon;
export declare const DatabaseWindowFilled: FluentFontIcon;
export declare const DatabaseWindowRegular: FluentFontIcon;
export declare const DecimalArrowLeftFilled: FluentFontIcon;
export declare const DecimalArrowLeftRegular: FluentFontIcon;
export declare const DecimalArrowRightFilled: FluentFontIcon;
export declare const DecimalArrowRightRegular: FluentFontIcon;
export declare const DeleteFilled: FluentFontIcon;
export declare const DeleteRegular: FluentFontIcon;
export declare const DeleteArrowBackFilled: FluentFontIcon;
export declare const DeleteArrowBackRegular: FluentFontIcon;
export declare const DeleteDismissFilled: FluentFontIcon;
export declare const DeleteDismissRegular: FluentFontIcon;
export declare const DeleteLinesFilled: FluentFontIcon;
export declare const DeleteLinesRegular: FluentFontIcon;
export declare const DeleteOffFilled: FluentFontIcon;
export declare const DeleteOffRegular: FluentFontIcon;
export declare const DentistFilled: FluentFontIcon;
export declare const DentistRegular: FluentFontIcon;
export declare const DesignIdeasFilled: FluentFontIcon;
export declare const DesignIdeasRegular: FluentFontIcon;
export declare const DeskFilled: FluentFontIcon;
export declare const DeskRegular: FluentFontIcon;
export declare const DeskMultipleFilled: FluentFontIcon;
export declare const DeskMultipleRegular: FluentFontIcon;
export declare const DeskSparkleFilled: FluentFontIcon;
export declare const DeskSparkleRegular: FluentFontIcon;
export declare const DesktopFilled: FluentFontIcon;
export declare const DesktopRegular: FluentFontIcon;
export declare const DesktopArrowDownFilled: FluentFontIcon;
export declare const DesktopArrowDownRegular: FluentFontIcon;
export declare const DesktopArrowDownOffFilled: FluentFontIcon;
export declare const DesktopArrowDownOffRegular: FluentFontIcon;
export declare const DesktopArrowRightFilled: FluentFontIcon;
export declare const DesktopArrowRightRegular: FluentFontIcon;
export declare const DesktopCheckmarkFilled: FluentFontIcon;
export declare const DesktopCheckmarkRegular: FluentFontIcon;
export declare const DesktopCursorFilled: FluentFontIcon;
export declare const DesktopCursorRegular: FluentFontIcon;
export declare const DesktopEditFilled: FluentFontIcon;
export declare const DesktopEditRegular: FluentFontIcon;
export declare const DesktopFlowFilled: FluentFontIcon;
export declare const DesktopFlowRegular: FluentFontIcon;
export declare const DesktopKeyboardFilled: FluentFontIcon;
export declare const DesktopKeyboardRegular: FluentFontIcon;
export declare const DesktopMacFilled: FluentFontIcon;
export declare const DesktopMacRegular: FluentFontIcon;
export declare const DesktopOffFilled: FluentFontIcon;
export declare const DesktopOffRegular: FluentFontIcon;
export declare const DesktopPulseFilled: FluentFontIcon;
export declare const DesktopPulseRegular: FluentFontIcon;
export declare const DesktopSignalFilled: FluentFontIcon;
export declare const DesktopSignalRegular: FluentFontIcon;
export declare const DesktopSpeakerFilled: FluentFontIcon;
export declare const DesktopSpeakerRegular: FluentFontIcon;
export declare const DesktopSpeakerOffFilled: FluentFontIcon;
export declare const DesktopSpeakerOffRegular: FluentFontIcon;
export declare const DesktopSyncFilled: FluentFontIcon;
export declare const DesktopSyncRegular: FluentFontIcon;
export declare const DesktopToolboxFilled: FluentFontIcon;
export declare const DesktopToolboxRegular: FluentFontIcon;
export declare const DesktopTowerFilled: FluentFontIcon;
export declare const DesktopTowerRegular: FluentFontIcon;
export declare const DeveloperBoardFilled: FluentFontIcon;
export declare const DeveloperBoardRegular: FluentFontIcon;
export declare const DeveloperBoardLightningFilled: FluentFontIcon;
export declare const DeveloperBoardLightningRegular: FluentFontIcon;
export declare const DeveloperBoardLightningToolboxFilled: FluentFontIcon;
export declare const DeveloperBoardLightningToolboxRegular: FluentFontIcon;
export declare const DeveloperBoardSearchFilled: FluentFontIcon;
export declare const DeveloperBoardSearchRegular: FluentFontIcon;
export declare const DeviceEqFilled: FluentFontIcon;
export declare const DeviceEqRegular: FluentFontIcon;
export declare const DeviceMeetingRoomFilled: FluentFontIcon;
export declare const DeviceMeetingRoomRegular: FluentFontIcon;
export declare const DeviceMeetingRoomRemoteFilled: FluentFontIcon;
export declare const DeviceMeetingRoomRemoteRegular: FluentFontIcon;
export declare const DiagramFilled: FluentFontIcon;
export declare const DiagramRegular: FluentFontIcon;
export declare const DialpadFilled: FluentFontIcon;
export declare const DialpadRegular: FluentFontIcon;
export declare const DialpadOffFilled: FluentFontIcon;
export declare const DialpadOffRegular: FluentFontIcon;
export declare const DialpadQuestionMarkFilled: FluentFontIcon;
export declare const DialpadQuestionMarkRegular: FluentFontIcon;
export declare const DiamondFilled: FluentFontIcon;
export declare const DiamondRegular: FluentFontIcon;
export declare const DiamondDismissFilled: FluentFontIcon;
export declare const DiamondDismissRegular: FluentFontIcon;
export declare const DirectionsFilled: FluentFontIcon;
export declare const DirectionsRegular: FluentFontIcon;
export declare const DishwasherFilled: FluentFontIcon;
export declare const DishwasherRegular: FluentFontIcon;
export declare const DismissFilled: FluentFontIcon;
export declare const DismissRegular: FluentFontIcon;
export declare const DismissCircleFilled: FluentFontIcon;
export declare const DismissCircleRegular: FluentFontIcon;
export declare const DismissSquareFilled: FluentFontIcon;
export declare const DismissSquareRegular: FluentFontIcon;
export declare const DismissSquareMultipleFilled: FluentFontIcon;
export declare const DismissSquareMultipleRegular: FluentFontIcon;
export declare const DiversityFilled: FluentFontIcon;
export declare const DiversityRegular: FluentFontIcon;
export declare const DividerShortFilled: FluentFontIcon;
export declare const DividerShortRegular: FluentFontIcon;
export declare const DividerTallFilled: FluentFontIcon;
export declare const DividerTallRegular: FluentFontIcon;
export declare const DockFilled: FluentFontIcon;
export declare const DockRegular: FluentFontIcon;
export declare const DockRowFilled: FluentFontIcon;
export declare const DockRowRegular: FluentFontIcon;
export declare const DoctorFilled: FluentFontIcon;
export declare const DoctorRegular: FluentFontIcon;
export declare const Document100Filled: FluentFontIcon;
export declare const Document100Regular: FluentFontIcon;
export declare const DocumentFilled: FluentFontIcon;
export declare const DocumentRegular: FluentFontIcon;
export declare const DocumentAddFilled: FluentFontIcon;
export declare const DocumentAddRegular: FluentFontIcon;
export declare const DocumentArrowDownFilled: FluentFontIcon;
export declare const DocumentArrowDownRegular: FluentFontIcon;
export declare const DocumentArrowLeftFilled: FluentFontIcon;
export declare const DocumentArrowLeftRegular: FluentFontIcon;
export declare const DocumentArrowRightFilled: FluentFontIcon;
export declare const DocumentArrowRightRegular: FluentFontIcon;
export declare const DocumentArrowUpFilled: FluentFontIcon;
export declare const DocumentArrowUpRegular: FluentFontIcon;
export declare const DocumentBorderFilled: FluentFontIcon;
export declare const DocumentBorderRegular: FluentFontIcon;
export declare const DocumentBorderPrintFilled: FluentFontIcon;
export declare const DocumentBorderPrintRegular: FluentFontIcon;
export declare const DocumentBriefcaseFilled: FluentFontIcon;
export declare const DocumentBriefcaseRegular: FluentFontIcon;
export declare const DocumentBulletListFilled: FluentFontIcon;
export declare const DocumentBulletListRegular: FluentFontIcon;
export declare const DocumentBulletListArrowLeftFilled: FluentFontIcon;
export declare const DocumentBulletListArrowLeftRegular: FluentFontIcon;
export declare const DocumentBulletListClockFilled: FluentFontIcon;
export declare const DocumentBulletListClockRegular: FluentFontIcon;
export declare const DocumentBulletListCubeFilled: FluentFontIcon;
export declare const DocumentBulletListCubeRegular: FluentFontIcon;
export declare const DocumentBulletListMultipleFilled: FluentFontIcon;
export declare const DocumentBulletListMultipleRegular: FluentFontIcon;
export declare const DocumentBulletListOffFilled: FluentFontIcon;
export declare const DocumentBulletListOffRegular: FluentFontIcon;
export declare const DocumentCatchUpFilled: FluentFontIcon;
export declare const DocumentCatchUpRegular: FluentFontIcon;
export declare const DocumentCheckmarkFilled: FluentFontIcon;
export declare const DocumentCheckmarkRegular: FluentFontIcon;
export declare const DocumentChevronDoubleFilled: FluentFontIcon;
export declare const DocumentChevronDoubleRegular: FluentFontIcon;
export declare const DocumentCopyFilled: FluentFontIcon;
export declare const DocumentCopyRegular: FluentFontIcon;
export declare const DocumentCssFilled: FluentFontIcon;
export declare const DocumentCssRegular: FluentFontIcon;
export declare const DocumentCubeFilled: FluentFontIcon;
export declare const DocumentCubeRegular: FluentFontIcon;
export declare const DocumentDataFilled: FluentFontIcon;
export declare const DocumentDataRegular: FluentFontIcon;
export declare const DocumentDataLinkFilled: FluentFontIcon;
export declare const DocumentDataLinkRegular: FluentFontIcon;
export declare const DocumentDataLockFilled: FluentFontIcon;
export declare const DocumentDataLockRegular: FluentFontIcon;
export declare const DocumentDatabaseFilled: FluentFontIcon;
export declare const DocumentDatabaseRegular: FluentFontIcon;
export declare const DocumentDismissFilled: FluentFontIcon;
export declare const DocumentDismissRegular: FluentFontIcon;
export declare const DocumentEditFilled: FluentFontIcon;
export declare const DocumentEditRegular: FluentFontIcon;
export declare const DocumentEndnoteFilled: FluentFontIcon;
export declare const DocumentEndnoteRegular: FluentFontIcon;
export declare const DocumentErrorFilled: FluentFontIcon;
export declare const DocumentErrorRegular: FluentFontIcon;
export declare const DocumentFitFilled: FluentFontIcon;
export declare const DocumentFitRegular: FluentFontIcon;
export declare const DocumentFlowchartFilled: FluentFontIcon;
export declare const DocumentFlowchartRegular: FluentFontIcon;
export declare const DocumentFolderFilled: FluentFontIcon;
export declare const DocumentFolderRegular: FluentFontIcon;
export declare const DocumentFooterFilled: FluentFontIcon;
export declare const DocumentFooterRegular: FluentFontIcon;
export declare const DocumentFooterDismissFilled: FluentFontIcon;
export declare const DocumentFooterDismissRegular: FluentFontIcon;
export declare const DocumentGlobeFilled: FluentFontIcon;
export declare const DocumentGlobeRegular: FluentFontIcon;
export declare const DocumentHeaderFilled: FluentFontIcon;
export declare const DocumentHeaderRegular: FluentFontIcon;
export declare const DocumentHeaderArrowDownFilled: FluentFontIcon;
export declare const DocumentHeaderArrowDownRegular: FluentFontIcon;
export declare const DocumentHeaderDismissFilled: FluentFontIcon;
export declare const DocumentHeaderDismissRegular: FluentFontIcon;
export declare const DocumentHeaderFooterFilled: FluentFontIcon;
export declare const DocumentHeaderFooterRegular: FluentFontIcon;
export declare const DocumentHeartFilled: FluentFontIcon;
export declare const DocumentHeartRegular: FluentFontIcon;
export declare const DocumentHeartPulseFilled: FluentFontIcon;
export declare const DocumentHeartPulseRegular: FluentFontIcon;
export declare const DocumentImageFilled: FluentFontIcon;
export declare const DocumentImageRegular: FluentFontIcon;
export declare const DocumentJavaFilled: FluentFontIcon;
export declare const DocumentJavaRegular: FluentFontIcon;
export declare const DocumentJavascriptFilled: FluentFontIcon;
export declare const DocumentJavascriptRegular: FluentFontIcon;
export declare const DocumentKeyFilled: FluentFontIcon;
export declare const DocumentKeyRegular: FluentFontIcon;
export declare const DocumentLandscapeFilled: FluentFontIcon;
export declare const DocumentLandscapeRegular: FluentFontIcon;
export declare const DocumentLandscapeDataFilled: FluentFontIcon;
export declare const DocumentLandscapeDataRegular: FluentFontIcon;
export declare const DocumentLandscapeSplitFilled: FluentFontIcon;
export declare const DocumentLandscapeSplitRegular: FluentFontIcon;
export declare const DocumentLandscapeSplitHintFilled: FluentFontIcon;
export declare const DocumentLandscapeSplitHintRegular: FluentFontIcon;
export declare const DocumentLightningFilled: FluentFontIcon;
export declare const DocumentLightningRegular: FluentFontIcon;
export declare const DocumentLinkFilled: FluentFontIcon;
export declare const DocumentLinkRegular: FluentFontIcon;
export declare const DocumentLockFilled: FluentFontIcon;
export declare const DocumentLockRegular: FluentFontIcon;
export declare const DocumentMarginsFilled: FluentFontIcon;
export declare const DocumentMarginsRegular: FluentFontIcon;
export declare const DocumentMentionFilled: FluentFontIcon;
export declare const DocumentMentionRegular: FluentFontIcon;
export declare const DocumentMultipleFilled: FluentFontIcon;
export declare const DocumentMultipleRegular: FluentFontIcon;
export declare const DocumentMultiplePercentFilled: FluentFontIcon;
export declare const DocumentMultiplePercentRegular: FluentFontIcon;
export declare const DocumentMultipleProhibitedFilled: FluentFontIcon;
export declare const DocumentMultipleProhibitedRegular: FluentFontIcon;
export declare const DocumentMultipleSyncFilled: FluentFontIcon;
export declare const DocumentMultipleSyncRegular: FluentFontIcon;
export declare const DocumentOnePageFilled: FluentFontIcon;
export declare const DocumentOnePageRegular: FluentFontIcon;
export declare const DocumentOnePageAddFilled: FluentFontIcon;
export declare const DocumentOnePageAddRegular: FluentFontIcon;
export declare const DocumentOnePageColumnsFilled: FluentFontIcon;
export declare const DocumentOnePageColumnsRegular: FluentFontIcon;
export declare const DocumentOnePageLinkFilled: FluentFontIcon;
export declare const DocumentOnePageLinkRegular: FluentFontIcon;
export declare const DocumentOnePageMultipleFilled: FluentFontIcon;
export declare const DocumentOnePageMultipleRegular: FluentFontIcon;
export declare const DocumentOnePageMultipleSparkleFilled: FluentFontIcon;
export declare const DocumentOnePageMultipleSparkleRegular: FluentFontIcon;
export declare const DocumentOnePageSparkleFilled: FluentFontIcon;
export declare const DocumentOnePageSparkleRegular: FluentFontIcon;
export declare const DocumentPageBottomCenterFilled: FluentFontIcon;
export declare const DocumentPageBottomCenterRegular: FluentFontIcon;
export declare const DocumentPageBottomLeftFilled: FluentFontIcon;
export declare const DocumentPageBottomLeftRegular: FluentFontIcon;
export declare const DocumentPageBottomRightFilled: FluentFontIcon;
export declare const DocumentPageBottomRightRegular: FluentFontIcon;
export declare const DocumentPageBreakFilled: FluentFontIcon;
export declare const DocumentPageBreakRegular: FluentFontIcon;
export declare const DocumentPageNumberFilled: FluentFontIcon;
export declare const DocumentPageNumberRegular: FluentFontIcon;
export declare const DocumentPageTopCenterFilled: FluentFontIcon;
export declare const DocumentPageTopCenterRegular: FluentFontIcon;
export declare const DocumentPageTopLeftFilled: FluentFontIcon;
export declare const DocumentPageTopLeftRegular: FluentFontIcon;
export declare const DocumentPageTopRightFilled: FluentFontIcon;
export declare const DocumentPageTopRightRegular: FluentFontIcon;
export declare const DocumentPdfFilled: FluentFontIcon;
export declare const DocumentPdfRegular: FluentFontIcon;
export declare const DocumentPercentFilled: FluentFontIcon;
export declare const DocumentPercentRegular: FluentFontIcon;
export declare const DocumentPersonFilled: FluentFontIcon;
export declare const DocumentPersonRegular: FluentFontIcon;
export declare const DocumentPillFilled: FluentFontIcon;
export declare const DocumentPillRegular: FluentFontIcon;
export declare const DocumentPrintFilled: FluentFontIcon;
export declare const DocumentPrintRegular: FluentFontIcon;
export declare const DocumentProhibitedFilled: FluentFontIcon;
export declare const DocumentProhibitedRegular: FluentFontIcon;
export declare const DocumentQuestionMarkFilled: FluentFontIcon;
export declare const DocumentQuestionMarkRegular: FluentFontIcon;
export declare const DocumentQueueFilled: FluentFontIcon;
export declare const DocumentQueueRegular: FluentFontIcon;
export declare const DocumentQueueAddFilled: FluentFontIcon;
export declare const DocumentQueueAddRegular: FluentFontIcon;
export declare const DocumentQueueMultipleFilled: FluentFontIcon;
export declare const DocumentQueueMultipleRegular: FluentFontIcon;
export declare const DocumentRibbonFilled: FluentFontIcon;
export declare const DocumentRibbonRegular: FluentFontIcon;
export declare const DocumentSassFilled: FluentFontIcon;
export declare const DocumentSassRegular: FluentFontIcon;
export declare const DocumentSaveFilled: FluentFontIcon;
export declare const DocumentSaveRegular: FluentFontIcon;
export declare const DocumentSearchFilled: FluentFontIcon;
export declare const DocumentSearchRegular: FluentFontIcon;
export declare const DocumentSettingsFilled: FluentFontIcon;
export declare const DocumentSettingsRegular: FluentFontIcon;
export declare const DocumentSignatureFilled: FluentFontIcon;
export declare const DocumentSignatureRegular: FluentFontIcon;
export declare const DocumentSparkleFilled: FluentFontIcon;
export declare const DocumentSparkleRegular: FluentFontIcon;
export declare const DocumentSplitHintFilled: FluentFontIcon;
export declare const DocumentSplitHintRegular: FluentFontIcon;
export declare const DocumentSplitHintOffFilled: FluentFontIcon;
export declare const DocumentSplitHintOffRegular: FluentFontIcon;
export declare const DocumentSquareFilled: FluentFontIcon;
export declare const DocumentSquareRegular: FluentFontIcon;
export declare const DocumentSyncFilled: FluentFontIcon;
export declare const DocumentSyncRegular: FluentFontIcon;
export declare const DocumentTableFilled: FluentFontIcon;
export declare const DocumentTableRegular: FluentFontIcon;
export declare const DocumentTableArrowRightFilled: FluentFontIcon;
export declare const DocumentTableArrowRightRegular: FluentFontIcon;
export declare const DocumentTableCheckmarkFilled: FluentFontIcon;
export declare const DocumentTableCheckmarkRegular: FluentFontIcon;
export declare const DocumentTableCubeFilled: FluentFontIcon;
export declare const DocumentTableCubeRegular: FluentFontIcon;
export declare const DocumentTableSearchFilled: FluentFontIcon;
export declare const DocumentTableSearchRegular: FluentFontIcon;
export declare const DocumentTableTruckFilled: FluentFontIcon;
export declare const DocumentTableTruckRegular: FluentFontIcon;
export declare const DocumentTargetFilled: FluentFontIcon;
export declare const DocumentTargetRegular: FluentFontIcon;
export declare const DocumentTextFilled: FluentFontIcon;
export declare const DocumentTextRegular: FluentFontIcon;
export declare const DocumentTextClockFilled: FluentFontIcon;
export declare const DocumentTextClockRegular: FluentFontIcon;
export declare const DocumentTextExtractFilled: FluentFontIcon;
export declare const DocumentTextExtractRegular: FluentFontIcon;
export declare const DocumentTextLinkFilled: FluentFontIcon;
export declare const DocumentTextLinkRegular: FluentFontIcon;
export declare const DocumentTextToolboxFilled: FluentFontIcon;
export declare const DocumentTextToolboxRegular: FluentFontIcon;
export declare const DocumentToolboxFilled: FluentFontIcon;
export declare const DocumentToolboxRegular: FluentFontIcon;
export declare const DocumentWidthFilled: FluentFontIcon;
export declare const DocumentWidthRegular: FluentFontIcon;
export declare const DocumentYmlFilled: FluentFontIcon;
export declare const DocumentYmlRegular: FluentFontIcon;
export declare const DoorFilled: FluentFontIcon;
export declare const DoorRegular: FluentFontIcon;
export declare const DoorArrowLeftFilled: FluentFontIcon;
export declare const DoorArrowLeftRegular: FluentFontIcon;
export declare const DoorArrowRightFilled: FluentFontIcon;
export declare const DoorArrowRightRegular: FluentFontIcon;
export declare const DoorTagFilled: FluentFontIcon;
export declare const DoorTagRegular: FluentFontIcon;
export declare const DoubleSwipeDownFilled: FluentFontIcon;
export declare const DoubleSwipeDownRegular: FluentFontIcon;
export declare const DoubleSwipeUpFilled: FluentFontIcon;
export declare const DoubleSwipeUpRegular: FluentFontIcon;
export declare const DoubleTapSwipeDownFilled: FluentFontIcon;
export declare const DoubleTapSwipeDownRegular: FluentFontIcon;
export declare const DoubleTapSwipeUpFilled: FluentFontIcon;
export declare const DoubleTapSwipeUpRegular: FluentFontIcon;
export declare const DraftsFilled: FluentFontIcon;
export declare const DraftsRegular: FluentFontIcon;
export declare const DragFilled: FluentFontIcon;
export declare const DragRegular: FluentFontIcon;
export declare const DrawImageFilled: FluentFontIcon;
export declare const DrawImageRegular: FluentFontIcon;
export declare const DrawShapeFilled: FluentFontIcon;
export declare const DrawShapeRegular: FluentFontIcon;
export declare const DrawTextFilled: FluentFontIcon;
export declare const DrawTextRegular: FluentFontIcon;
export declare const DrawerFilled: FluentFontIcon;
export declare const DrawerRegular: FluentFontIcon;
export declare const DrawerAddFilled: FluentFontIcon;
export declare const DrawerAddRegular: FluentFontIcon;
export declare const DrawerArrowDownloadFilled: FluentFontIcon;
export declare const DrawerArrowDownloadRegular: FluentFontIcon;
export declare const DrawerDismissFilled: FluentFontIcon;
export declare const DrawerDismissRegular: FluentFontIcon;
export declare const DrawerPlayFilled: FluentFontIcon;
export declare const DrawerPlayRegular: FluentFontIcon;
export declare const DrawerSubtractFilled: FluentFontIcon;
export declare const DrawerSubtractRegular: FluentFontIcon;
export declare const DrinkBeerFilled: FluentFontIcon;
export declare const DrinkBeerRegular: FluentFontIcon;
export declare const DrinkBottleFilled: FluentFontIcon;
export declare const DrinkBottleRegular: FluentFontIcon;
export declare const DrinkBottleOffFilled: FluentFontIcon;
export declare const DrinkBottleOffRegular: FluentFontIcon;
export declare const DrinkCoffeeFilled: FluentFontIcon;
export declare const DrinkCoffeeRegular: FluentFontIcon;
export declare const DrinkMargaritaFilled: FluentFontIcon;
export declare const DrinkMargaritaRegular: FluentFontIcon;
export declare const DrinkToGoFilled: FluentFontIcon;
export declare const DrinkToGoRegular: FluentFontIcon;
export declare const DrinkWineFilled: FluentFontIcon;
export declare const DrinkWineRegular: FluentFontIcon;
export declare const DriveTrainFilled: FluentFontIcon;
export declare const DriveTrainRegular: FluentFontIcon;
export declare const DropFilled: FluentFontIcon;
export declare const DropRegular: FluentFontIcon;
export declare const DualScreenFilled: FluentFontIcon;
export declare const DualScreenRegular: FluentFontIcon;
export declare const DualScreenAddFilled: FluentFontIcon;
export declare const DualScreenAddRegular: FluentFontIcon;
export declare const DualScreenArrowRightFilled: FluentFontIcon;
export declare const DualScreenArrowRightRegular: FluentFontIcon;
export declare const DualScreenArrowUpFilled: FluentFontIcon;
export declare const DualScreenArrowUpRegular: FluentFontIcon;
export declare const DualScreenClockFilled: FluentFontIcon;
export declare const DualScreenClockRegular: FluentFontIcon;
export declare const DualScreenClosedAlertFilled: FluentFontIcon;
export declare const DualScreenClosedAlertRegular: FluentFontIcon;
export declare const DualScreenDesktopFilled: FluentFontIcon;
export declare const DualScreenDesktopRegular: FluentFontIcon;
export declare const DualScreenDismissFilled: FluentFontIcon;
export declare const DualScreenDismissRegular: FluentFontIcon;
export declare const DualScreenGroupFilled: FluentFontIcon;
export declare const DualScreenGroupRegular: FluentFontIcon;
export declare const DualScreenHeaderFilled: FluentFontIcon;
export declare const DualScreenHeaderRegular: FluentFontIcon;
export declare const DualScreenLockFilled: FluentFontIcon;
export declare const DualScreenLockRegular: FluentFontIcon;
export declare const DualScreenMirrorFilled: FluentFontIcon;
export declare const DualScreenMirrorRegular: FluentFontIcon;
export declare const DualScreenPaginationFilled: FluentFontIcon;
export declare const DualScreenPaginationRegular: FluentFontIcon;
export declare const DualScreenSettingsFilled: FluentFontIcon;
export declare const DualScreenSettingsRegular: FluentFontIcon;
export declare const DualScreenSpanFilled: FluentFontIcon;
export declare const DualScreenSpanRegular: FluentFontIcon;
export declare const DualScreenSpeakerFilled: FluentFontIcon;
export declare const DualScreenSpeakerRegular: FluentFontIcon;
export declare const DualScreenStatusBarFilled: FluentFontIcon;
export declare const DualScreenStatusBarRegular: FluentFontIcon;
export declare const DualScreenTabletFilled: FluentFontIcon;
export declare const DualScreenTabletRegular: FluentFontIcon;
export declare const DualScreenUpdateFilled: FluentFontIcon;
export declare const DualScreenUpdateRegular: FluentFontIcon;
export declare const DualScreenVerticalScrollFilled: FluentFontIcon;
export declare const DualScreenVerticalScrollRegular: FluentFontIcon;
export declare const DualScreenVibrateFilled: FluentFontIcon;
export declare const DualScreenVibrateRegular: FluentFontIcon;
export declare const DumbbellFilled: FluentFontIcon;
export declare const DumbbellRegular: FluentFontIcon;
