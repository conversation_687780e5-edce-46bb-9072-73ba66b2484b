import type { FluentIcon } from "../utils/createFluentIcon";
export declare const DoubleTapSwipeUpFilled: FluentIcon;
export declare const DoubleTapSwipeUpRegular: FluentIcon;
export declare const DraftsColor: FluentIcon;
export declare const DraftsFilled: FluentIcon;
export declare const DraftsRegular: FluentIcon;
export declare const DragFilled: FluentIcon;
export declare const DragRegular: FluentIcon;
export declare const DrawImageFilled: FluentIcon;
export declare const DrawImageRegular: FluentIcon;
export declare const DrawShapeFilled: FluentIcon;
export declare const DrawShapeRegular: FluentIcon;
export declare const DrawTextFilled: FluentIcon;
export declare const DrawTextRegular: FluentIcon;
export declare const DrawerFilled: FluentIcon;
export declare const DrawerRegular: FluentIcon;
export declare const DrawerAddFilled: FluentIcon;
export declare const DrawerAddRegular: FluentIcon;
export declare const DrawerArrowDownloadFilled: FluentIcon;
export declare const DrawerArrowDownloadRegular: FluentIcon;
export declare const DrawerDismissFilled: FluentIcon;
export declare const DrawerDismissRegular: FluentIcon;
export declare const DrawerPlayFilled: FluentIcon;
export declare const DrawerPlayRegular: FluentIcon;
export declare const DrawerSubtractFilled: FluentIcon;
export declare const DrawerSubtractRegular: FluentIcon;
export declare const DrinkBeerFilled: FluentIcon;
export declare const DrinkBeerRegular: FluentIcon;
export declare const DrinkBottleFilled: FluentIcon;
export declare const DrinkBottleRegular: FluentIcon;
export declare const DrinkBottleOffFilled: FluentIcon;
export declare const DrinkBottleOffRegular: FluentIcon;
export declare const DrinkCoffeeFilled: FluentIcon;
export declare const DrinkCoffeeRegular: FluentIcon;
export declare const DrinkMargaritaFilled: FluentIcon;
export declare const DrinkMargaritaRegular: FluentIcon;
export declare const DrinkToGoFilled: FluentIcon;
export declare const DrinkToGoRegular: FluentIcon;
export declare const DrinkWineFilled: FluentIcon;
export declare const DrinkWineRegular: FluentIcon;
export declare const DriveTrainFilled: FluentIcon;
export declare const DriveTrainRegular: FluentIcon;
export declare const DropFilled: FluentIcon;
export declare const DropRegular: FluentIcon;
export declare const DualScreenFilled: FluentIcon;
export declare const DualScreenRegular: FluentIcon;
export declare const DualScreenAddFilled: FluentIcon;
export declare const DualScreenAddRegular: FluentIcon;
export declare const DualScreenArrowRightFilled: FluentIcon;
export declare const DualScreenArrowRightRegular: FluentIcon;
export declare const DualScreenArrowUpFilled: FluentIcon;
export declare const DualScreenArrowUpRegular: FluentIcon;
export declare const DualScreenClockFilled: FluentIcon;
export declare const DualScreenClockRegular: FluentIcon;
export declare const DualScreenClosedAlertFilled: FluentIcon;
export declare const DualScreenClosedAlertRegular: FluentIcon;
export declare const DualScreenDesktopFilled: FluentIcon;
export declare const DualScreenDesktopRegular: FluentIcon;
export declare const DualScreenDismissFilled: FluentIcon;
export declare const DualScreenDismissRegular: FluentIcon;
export declare const DualScreenGroupFilled: FluentIcon;
export declare const DualScreenGroupRegular: FluentIcon;
export declare const DualScreenHeaderFilled: FluentIcon;
export declare const DualScreenHeaderRegular: FluentIcon;
export declare const DualScreenLockFilled: FluentIcon;
export declare const DualScreenLockRegular: FluentIcon;
export declare const DualScreenMirrorFilled: FluentIcon;
export declare const DualScreenMirrorRegular: FluentIcon;
export declare const DualScreenPaginationFilled: FluentIcon;
export declare const DualScreenPaginationRegular: FluentIcon;
export declare const DualScreenSettingsFilled: FluentIcon;
export declare const DualScreenSettingsRegular: FluentIcon;
export declare const DualScreenSpanFilled: FluentIcon;
export declare const DualScreenSpanRegular: FluentIcon;
export declare const DualScreenSpeakerFilled: FluentIcon;
export declare const DualScreenSpeakerRegular: FluentIcon;
export declare const DualScreenStatusBarFilled: FluentIcon;
export declare const DualScreenStatusBarRegular: FluentIcon;
export declare const DualScreenTabletFilled: FluentIcon;
export declare const DualScreenTabletRegular: FluentIcon;
export declare const DualScreenUpdateFilled: FluentIcon;
export declare const DualScreenUpdateRegular: FluentIcon;
export declare const DualScreenVerticalScrollFilled: FluentIcon;
export declare const DualScreenVerticalScrollRegular: FluentIcon;
export declare const DualScreenVibrateFilled: FluentIcon;
export declare const DualScreenVibrateRegular: FluentIcon;
export declare const DumbbellFilled: FluentIcon;
export declare const DumbbellRegular: FluentIcon;
export declare const DustFilled: FluentIcon;
export declare const DustRegular: FluentIcon;
export declare const EarthFilled: FluentIcon;
export declare const EarthRegular: FluentIcon;
export declare const EarthLeafFilled: FluentIcon;
export declare const EarthLeafRegular: FluentIcon;
export declare const EditColor: FluentIcon;
export declare const EditFilled: FluentIcon;
export declare const EditRegular: FluentIcon;
export declare const EditArrowBackFilled: FluentIcon;
export declare const EditArrowBackRegular: FluentIcon;
export declare const EditLineHorizontal3Filled: FluentIcon;
export declare const EditLineHorizontal3Regular: FluentIcon;
export declare const EditLockFilled: FluentIcon;
export declare const EditLockRegular: FluentIcon;
export declare const EditOffFilled: FluentIcon;
export declare const EditOffRegular: FluentIcon;
export declare const EditPersonFilled: FluentIcon;
export declare const EditPersonRegular: FluentIcon;
export declare const EditProhibitedFilled: FluentIcon;
export declare const EditProhibitedRegular: FluentIcon;
export declare const EditSettingsFilled: FluentIcon;
export declare const EditSettingsRegular: FluentIcon;
export declare const ElevatorFilled: FluentIcon;
export declare const ElevatorRegular: FluentIcon;
export declare const EmojiFilled: FluentIcon;
export declare const EmojiRegular: FluentIcon;
export declare const EmojiAddFilled: FluentIcon;
export declare const EmojiAddRegular: FluentIcon;
export declare const EmojiAngryFilled: FluentIcon;
export declare const EmojiAngryRegular: FluentIcon;
export declare const EmojiEditFilled: FluentIcon;
export declare const EmojiEditRegular: FluentIcon;
export declare const EmojiHandFilled: FluentIcon;
export declare const EmojiHandRegular: FluentIcon;
export declare const EmojiHintFilled: FluentIcon;
export declare const EmojiHintRegular: FluentIcon;
export declare const EmojiLaughFilled: FluentIcon;
export declare const EmojiLaughRegular: FluentIcon;
export declare const EmojiMehFilled: FluentIcon;
export declare const EmojiMehRegular: FluentIcon;
export declare const EmojiMemeFilled: FluentIcon;
export declare const EmojiMemeRegular: FluentIcon;
export declare const EmojiMultipleFilled: FluentIcon;
export declare const EmojiMultipleRegular: FluentIcon;
export declare const EmojiSadFilled: FluentIcon;
export declare const EmojiSadRegular: FluentIcon;
export declare const EmojiSadSlightFilled: FluentIcon;
export declare const EmojiSadSlightRegular: FluentIcon;
export declare const EmojiSmileSlightFilled: FluentIcon;
export declare const EmojiSmileSlightRegular: FluentIcon;
export declare const EmojiSparkleFilled: FluentIcon;
export declare const EmojiSparkleRegular: FluentIcon;
export declare const EmojiSurpriseFilled: FluentIcon;
export declare const EmojiSurpriseRegular: FluentIcon;
export declare const EngineFilled: FluentIcon;
export declare const EngineRegular: FluentIcon;
export declare const EqualCircleFilled: FluentIcon;
export declare const EqualCircleRegular: FluentIcon;
export declare const EqualOffFilled: FluentIcon;
export declare const EqualOffRegular: FluentIcon;
export declare const EraserFilled: FluentIcon;
export declare const EraserRegular: FluentIcon;
export declare const EraserMediumFilled: FluentIcon;
export declare const EraserMediumRegular: FluentIcon;
export declare const EraserSegmentFilled: FluentIcon;
export declare const EraserSegmentRegular: FluentIcon;
export declare const EraserSmallFilled: FluentIcon;
export declare const EraserSmallRegular: FluentIcon;
export declare const EraserToolFilled: FluentIcon;
export declare const EraserToolRegular: FluentIcon;
export declare const ErrorCircleColor: FluentIcon;
export declare const ErrorCircleFilled: FluentIcon;
export declare const ErrorCircleRegular: FluentIcon;
export declare const ErrorCircleSettingsFilled: FluentIcon;
export declare const ErrorCircleSettingsRegular: FluentIcon;
export declare const ExpandUpLeftFilled: FluentIcon;
export declare const ExpandUpLeftRegular: FluentIcon;
export declare const ExpandUpRightFilled: FluentIcon;
export declare const ExpandUpRightRegular: FluentIcon;
export declare const ExtendedDockFilled: FluentIcon;
export declare const ExtendedDockRegular: FluentIcon;
export declare const EyeFilled: FluentIcon;
export declare const EyeRegular: FluentIcon;
export declare const EyeLinesFilled: FluentIcon;
export declare const EyeLinesRegular: FluentIcon;
export declare const EyeOffFilled: FluentIcon;
export declare const EyeOffRegular: FluentIcon;
export declare const EyeTrackingFilled: FluentIcon;
export declare const EyeTrackingRegular: FluentIcon;
export declare const EyeTrackingOffFilled: FluentIcon;
export declare const EyeTrackingOffRegular: FluentIcon;
export declare const EyedropperFilled: FluentIcon;
export declare const EyedropperRegular: FluentIcon;
export declare const EyedropperOffFilled: FluentIcon;
export declare const EyedropperOffRegular: FluentIcon;
export declare const FStopFilled: FluentIcon;
export declare const FStopRegular: FluentIcon;
export declare const FastAccelerationFilled: FluentIcon;
export declare const FastAccelerationRegular: FluentIcon;
export declare const FastForwardFilled: FluentIcon;
export declare const FastForwardRegular: FluentIcon;
export declare const FaxFilled: FluentIcon;
export declare const FaxRegular: FluentIcon;
export declare const FeedFilled: FluentIcon;
export declare const FeedRegular: FluentIcon;
export declare const FilmstripFilled: FluentIcon;
export declare const FilmstripRegular: FluentIcon;
export declare const FilmstripImageFilled: FluentIcon;
export declare const FilmstripImageRegular: FluentIcon;
export declare const FilmstripPlayFilled: FluentIcon;
export declare const FilmstripPlayRegular: FluentIcon;
export declare const FilmstripSplitFilled: FluentIcon;
export declare const FilmstripSplitRegular: FluentIcon;
export declare const FilterFilled: FluentIcon;
export declare const FilterRegular: FluentIcon;
export declare const FilterAddFilled: FluentIcon;
export declare const FilterAddRegular: FluentIcon;
export declare const FilterDismissFilled: FluentIcon;
export declare const FilterDismissRegular: FluentIcon;
export declare const FilterSyncFilled: FluentIcon;
export declare const FilterSyncRegular: FluentIcon;
export declare const FingerprintFilled: FluentIcon;
export declare const FingerprintRegular: FluentIcon;
export declare const FireFilled: FluentIcon;
export declare const FireRegular: FluentIcon;
export declare const FireplaceFilled: FluentIcon;
export declare const FireplaceRegular: FluentIcon;
export declare const FixedWidthFilled: FluentIcon;
export declare const FixedWidthRegular: FluentIcon;
export declare const FlagColor: FluentIcon;
export declare const FlagFilled: FluentIcon;
export declare const FlagRegular: FluentIcon;
export declare const FlagCheckeredFilled: FluentIcon;
export declare const FlagCheckeredRegular: FluentIcon;
export declare const FlagClockFilled: FluentIcon;
export declare const FlagClockRegular: FluentIcon;
export declare const FlagOffFilled: FluentIcon;
export declare const FlagOffRegular: FluentIcon;
export declare const FlagPrideFilled: FluentIcon;
export declare const FlagPrideIntersexInclusiveProgressFilled: FluentIcon;
export declare const FlagPridePhiladelphiaFilled: FluentIcon;
export declare const FlagPrideProgressFilled: FluentIcon;
export declare const FlashFilled: FluentIcon;
export declare const FlashRegular: FluentIcon;
export declare const FlashAddFilled: FluentIcon;
export declare const FlashAddRegular: FluentIcon;
export declare const FlashAutoFilled: FluentIcon;
export declare const FlashAutoRegular: FluentIcon;
export declare const FlashCheckmarkFilled: FluentIcon;
export declare const FlashCheckmarkRegular: FluentIcon;
export declare const FlashFlowFilled: FluentIcon;
export declare const FlashFlowRegular: FluentIcon;
export declare const FlashOffFilled: FluentIcon;
export declare const FlashOffRegular: FluentIcon;
export declare const FlashPlayFilled: FluentIcon;
export declare const FlashPlayRegular: FluentIcon;
export declare const FlashSettingsFilled: FluentIcon;
export declare const FlashSettingsRegular: FluentIcon;
export declare const FlashSparkleFilled: FluentIcon;
export declare const FlashSparkleRegular: FluentIcon;
export declare const FlashlightFilled: FluentIcon;
export declare const FlashlightRegular: FluentIcon;
export declare const FlashlightOffFilled: FluentIcon;
export declare const FlashlightOffRegular: FluentIcon;
export declare const FlipHorizontalFilled: FluentIcon;
export declare const FlipHorizontalRegular: FluentIcon;
export declare const FlipVerticalFilled: FluentIcon;
export declare const FlipVerticalRegular: FluentIcon;
export declare const FlowFilled: FluentIcon;
export declare const FlowRegular: FluentIcon;
export declare const FlowDotFilled: FluentIcon;
export declare const FlowDotRegular: FluentIcon;
export declare const FlowSparkleFilled: FluentIcon;
export declare const FlowSparkleRegular: FluentIcon;
export declare const FlowchartFilled: FluentIcon;
export declare const FlowchartRegular: FluentIcon;
export declare const FlowchartCircleFilled: FluentIcon;
export declare const FlowchartCircleRegular: FluentIcon;
export declare const FluentFilled: FluentIcon;
export declare const FluentRegular: FluentIcon;
export declare const FluidFilled: FluentIcon;
export declare const FluidRegular: FluentIcon;
export declare const FolderFilled: FluentIcon;
export declare const FolderRegular: FluentIcon;
export declare const FolderAddFilled: FluentIcon;
export declare const FolderAddRegular: FluentIcon;
export declare const FolderArrowLeftFilled: FluentIcon;
export declare const FolderArrowLeftRegular: FluentIcon;
export declare const FolderArrowRightFilled: FluentIcon;
export declare const FolderArrowRightRegular: FluentIcon;
export declare const FolderArrowUpFilled: FluentIcon;
export declare const FolderArrowUpRegular: FluentIcon;
export declare const FolderBriefcaseFilled: FluentIcon;
export declare const FolderBriefcaseRegular: FluentIcon;
export declare const FolderDocumentFilled: FluentIcon;
export declare const FolderDocumentRegular: FluentIcon;
export declare const FolderGlobeFilled: FluentIcon;
export declare const FolderGlobeRegular: FluentIcon;
export declare const FolderLightningFilled: FluentIcon;
export declare const FolderLightningRegular: FluentIcon;
export declare const FolderLinkFilled: FluentIcon;
export declare const FolderLinkRegular: FluentIcon;
export declare const FolderListFilled: FluentIcon;
export declare const FolderListRegular: FluentIcon;
export declare const FolderMailFilled: FluentIcon;
export declare const FolderMailRegular: FluentIcon;
export declare const FolderOpenFilled: FluentIcon;
export declare const FolderOpenRegular: FluentIcon;
export declare const FolderOpenDownFilled: FluentIcon;
export declare const FolderOpenDownRegular: FluentIcon;
export declare const FolderOpenVerticalFilled: FluentIcon;
export declare const FolderOpenVerticalRegular: FluentIcon;
export declare const FolderPeopleFilled: FluentIcon;
export declare const FolderPeopleRegular: FluentIcon;
export declare const FolderPersonFilled: FluentIcon;
export declare const FolderPersonRegular: FluentIcon;
export declare const FolderProhibitedFilled: FluentIcon;
export declare const FolderProhibitedRegular: FluentIcon;
export declare const FolderSearchFilled: FluentIcon;
export declare const FolderSearchRegular: FluentIcon;
export declare const FolderSwapFilled: FluentIcon;
export declare const FolderSwapRegular: FluentIcon;
export declare const FolderSyncFilled: FluentIcon;
export declare const FolderSyncRegular: FluentIcon;
export declare const FolderZipFilled: FluentIcon;
export declare const FolderZipRegular: FluentIcon;
export declare const FontDecreaseFilled: FluentIcon;
export declare const FontDecreaseRegular: FluentIcon;
export declare const FontIncreaseFilled: FluentIcon;
export declare const FontIncreaseRegular: FluentIcon;
export declare const FontSpaceTrackingInFilled: FluentIcon;
export declare const FontSpaceTrackingInRegular: FluentIcon;
export declare const FontSpaceTrackingOutFilled: FluentIcon;
export declare const FontSpaceTrackingOutRegular: FluentIcon;
export declare const FoodColor: FluentIcon;
export declare const FoodFilled: FluentIcon;
export declare const FoodRegular: FluentIcon;
export declare const FoodAppleFilled: FluentIcon;
export declare const FoodAppleRegular: FluentIcon;
export declare const FoodCakeFilled: FluentIcon;
export declare const FoodCakeRegular: FluentIcon;
export declare const FoodCarrotFilled: FluentIcon;
export declare const FoodCarrotRegular: FluentIcon;
export declare const FoodChickenLegFilled: FluentIcon;
export declare const FoodChickenLegRegular: FluentIcon;
export declare const FoodEggFilled: FluentIcon;
export declare const FoodEggRegular: FluentIcon;
export declare const FoodFishFilled: FluentIcon;
export declare const FoodFishRegular: FluentIcon;
export declare const FoodGrainsFilled: FluentIcon;
export declare const FoodGrainsRegular: FluentIcon;
export declare const FoodPizzaFilled: FluentIcon;
export declare const FoodPizzaRegular: FluentIcon;
export declare const FoodToastFilled: FluentIcon;
export declare const FoodToastRegular: FluentIcon;
export declare const FormColor: FluentIcon;
export declare const FormFilled: FluentIcon;
export declare const FormRegular: FluentIcon;
export declare const FormMultipleFilled: FluentIcon;
export declare const FormMultipleRegular: FluentIcon;
export declare const FormMultipleCollectionFilled: FluentIcon;
export declare const FormMultipleCollectionRegular: FluentIcon;
export declare const FormNewFilled: FluentIcon;
export declare const FormNewRegular: FluentIcon;
export declare const FormSparkleFilled: FluentIcon;
export declare const FormSparkleRegular: FluentIcon;
export declare const Fps120Filled: FluentIcon;
export declare const Fps120Regular: FluentIcon;
export declare const Fps124Filled: FluentIcon;
export declare const Fps124Regular: FluentIcon;
export declare const Fps240Filled: FluentIcon;
export declare const Fps240Regular: FluentIcon;
export declare const Fps30Filled: FluentIcon;
export declare const Fps30Regular: FluentIcon;
export declare const Fps60Filled: FluentIcon;
export declare const Fps60Regular: FluentIcon;
export declare const Fps960Filled: FluentIcon;
export declare const Fps960Regular: FluentIcon;
export declare const FrameFilled: FluentIcon;
export declare const FrameRegular: FluentIcon;
export declare const FullScreenMaximizeFilled: FluentIcon;
export declare const FullScreenMaximizeRegular: FluentIcon;
export declare const FullScreenMinimizeFilled: FluentIcon;
export declare const FullScreenMinimizeRegular: FluentIcon;
export declare const GameChatColor: FluentIcon;
export declare const GameChatFilled: FluentIcon;
export declare const GameChatRegular: FluentIcon;
export declare const GamesFilled: FluentIcon;
export declare const GamesRegular: FluentIcon;
export declare const GanttChartFilled: FluentIcon;
export declare const GanttChartRegular: FluentIcon;
export declare const GasFilled: FluentIcon;
export declare const GasRegular: FluentIcon;
export declare const GasPumpFilled: FluentIcon;
export declare const GasPumpRegular: FluentIcon;
export declare const GatherFilled: FluentIcon;
export declare const GatherRegular: FluentIcon;
export declare const GaugeColor: FluentIcon;
export declare const GaugeFilled: FluentIcon;
export declare const GaugeRegular: FluentIcon;
export declare const GaugeAddFilled: FluentIcon;
export declare const GaugeAddRegular: FluentIcon;
export declare const GavelFilled: FluentIcon;
export declare const GavelRegular: FluentIcon;
export declare const GavelProhibitedFilled: FluentIcon;
export declare const GavelProhibitedRegular: FluentIcon;
export declare const GestureFilled: FluentIcon;
export declare const GestureRegular: FluentIcon;
export declare const GifFilled: FluentIcon;
export declare const GifRegular: FluentIcon;
export declare const GiftColor: FluentIcon;
export declare const GiftFilled: FluentIcon;
export declare const GiftRegular: FluentIcon;
export declare const GiftCardColor: FluentIcon;
export declare const GiftCardFilled: FluentIcon;
export declare const GiftCardRegular: FluentIcon;
export declare const GiftCardAddFilled: FluentIcon;
export declare const GiftCardAddRegular: FluentIcon;
export declare const GiftCardArrowRightFilled: FluentIcon;
export declare const GiftCardArrowRightRegular: FluentIcon;
export declare const GiftCardMoneyFilled: FluentIcon;
export declare const GiftCardMoneyRegular: FluentIcon;
export declare const GiftCardMultipleFilled: FluentIcon;
export declare const GiftCardMultipleRegular: FluentIcon;
export declare const GiftOpenFilled: FluentIcon;
export declare const GiftOpenRegular: FluentIcon;
export declare const GlanceFilled: FluentIcon;
export declare const GlanceRegular: FluentIcon;
export declare const GlanceHorizontalFilled: FluentIcon;
export declare const GlanceHorizontalRegular: FluentIcon;
export declare const GlanceHorizontalSparklesFilled: FluentIcon;
export declare const GlanceHorizontalSparklesRegular: FluentIcon;
export declare const GlassesFilled: FluentIcon;
export declare const GlassesRegular: FluentIcon;
export declare const GlassesOffFilled: FluentIcon;
export declare const GlassesOffRegular: FluentIcon;
export declare const GlobeColor: FluentIcon;
export declare const GlobeFilled: FluentIcon;
export declare const GlobeRegular: FluentIcon;
export declare const GlobeAddFilled: FluentIcon;
export declare const GlobeAddRegular: FluentIcon;
export declare const GlobeArrowForwardFilled: FluentIcon;
export declare const GlobeArrowForwardRegular: FluentIcon;
export declare const GlobeArrowUpFilled: FluentIcon;
export declare const GlobeArrowUpRegular: FluentIcon;
export declare const GlobeClockFilled: FluentIcon;
export declare const GlobeClockRegular: FluentIcon;
export declare const GlobeDesktopFilled: FluentIcon;
export declare const GlobeDesktopRegular: FluentIcon;
export declare const GlobeErrorFilled: FluentIcon;
export declare const GlobeErrorRegular: FluentIcon;
export declare const GlobeLocationFilled: FluentIcon;
export declare const GlobeLocationRegular: FluentIcon;
export declare const GlobeOffFilled: FluentIcon;
export declare const GlobeOffRegular: FluentIcon;
export declare const GlobePersonFilled: FluentIcon;
export declare const GlobePersonRegular: FluentIcon;
export declare const GlobeProhibitedFilled: FluentIcon;
export declare const GlobeProhibitedRegular: FluentIcon;
export declare const GlobeSearchFilled: FluentIcon;
export declare const GlobeSearchRegular: FluentIcon;
export declare const GlobeShieldColor: FluentIcon;
export declare const GlobeShieldFilled: FluentIcon;
export declare const GlobeShieldRegular: FluentIcon;
export declare const GlobeStarFilled: FluentIcon;
export declare const GlobeStarRegular: FluentIcon;
export declare const GlobeSurfaceFilled: FluentIcon;
export declare const GlobeSurfaceRegular: FluentIcon;
export declare const GlobeSyncFilled: FluentIcon;
export declare const GlobeSyncRegular: FluentIcon;
export declare const GlobeVideoFilled: FluentIcon;
export declare const GlobeVideoRegular: FluentIcon;
export declare const GlobeWarningFilled: FluentIcon;
export declare const GlobeWarningRegular: FluentIcon;
export declare const GridFilled: FluentIcon;
export declare const GridRegular: FluentIcon;
export declare const GridDotsFilled: FluentIcon;
export declare const GridDotsRegular: FluentIcon;
export declare const GridKanbanFilled: FluentIcon;
export declare const GridKanbanRegular: FluentIcon;
export declare const GroupFilled: FluentIcon;
export declare const GroupRegular: FluentIcon;
export declare const GroupDismissFilled: FluentIcon;
export declare const GroupDismissRegular: FluentIcon;
export declare const GroupListFilled: FluentIcon;
export declare const GroupListRegular: FluentIcon;
export declare const GroupReturnFilled: FluentIcon;
export declare const GroupReturnRegular: FluentIcon;
export declare const GuardianFilled: FluentIcon;
export declare const GuardianRegular: FluentIcon;
export declare const GuestColor: FluentIcon;
export declare const GuestFilled: FluentIcon;
export declare const GuestRegular: FluentIcon;
export declare const GuestAddFilled: FluentIcon;
export declare const GuestAddRegular: FluentIcon;
export declare const GuitarFilled: FluentIcon;
export declare const GuitarRegular: FluentIcon;
export declare const HandDrawFilled: FluentIcon;
export declare const HandDrawRegular: FluentIcon;
export declare const HandLeftFilled: FluentIcon;
export declare const HandLeftRegular: FluentIcon;
export declare const HandLeftChatFilled: FluentIcon;
export declare const HandLeftChatRegular: FluentIcon;
export declare const HandMultipleFilled: FluentIcon;
export declare const HandMultipleRegular: FluentIcon;
export declare const HandOpenHeartFilled: FluentIcon;
export declare const HandOpenHeartRegular: FluentIcon;
export declare const HandPointFilled: FluentIcon;
export declare const HandPointRegular: FluentIcon;
export declare const HandRightFilled: FluentIcon;
export declare const HandRightRegular: FluentIcon;
export declare const HandRightOffFilled: FluentIcon;
export declare const HandRightOffRegular: FluentIcon;
export declare const HandWaveFilled: FluentIcon;
export declare const HandWaveRegular: FluentIcon;
export declare const HandshakeFilled: FluentIcon;
export declare const HandshakeRegular: FluentIcon;
export declare const HapticStrongFilled: FluentIcon;
export declare const HapticStrongRegular: FluentIcon;
export declare const HapticWeakFilled: FluentIcon;
export declare const HapticWeakRegular: FluentIcon;
export declare const HardDriveFilled: FluentIcon;
export declare const HardDriveRegular: FluentIcon;
export declare const HatGraduationFilled: FluentIcon;
export declare const HatGraduationRegular: FluentIcon;
export declare const HatGraduationAddFilled: FluentIcon;
export declare const HatGraduationAddRegular: FluentIcon;
export declare const HatGraduationSparkleFilled: FluentIcon;
export declare const HatGraduationSparkleRegular: FluentIcon;
export declare const HdFilled: FluentIcon;
export declare const HdRegular: FluentIcon;
export declare const HdOffFilled: FluentIcon;
export declare const HdOffRegular: FluentIcon;
export declare const HdrFilled: FluentIcon;
export declare const HdrRegular: FluentIcon;
export declare const HdrOffFilled: FluentIcon;
export declare const HdrOffRegular: FluentIcon;
export declare const HeadphonesColor: FluentIcon;
export declare const HeadphonesFilled: FluentIcon;
export declare const HeadphonesRegular: FluentIcon;
export declare const HeadphonesSoundWaveFilled: FluentIcon;
export declare const HeadphonesSoundWaveRegular: FluentIcon;
export declare const HeadsetColor: FluentIcon;
export declare const HeadsetFilled: FluentIcon;
export declare const HeadsetRegular: FluentIcon;
export declare const HeadsetAddFilled: FluentIcon;
export declare const HeadsetAddRegular: FluentIcon;
export declare const HeadsetVrFilled: FluentIcon;
export declare const HeadsetVrRegular: FluentIcon;
export declare const HeartColor: FluentIcon;
export declare const HeartFilled: FluentIcon;
export declare const HeartRegular: FluentIcon;
export declare const HeartBrokenFilled: FluentIcon;
export declare const HeartBrokenRegular: FluentIcon;
export declare const HeartCircleFilled: FluentIcon;
export declare const HeartCircleRegular: FluentIcon;
export declare const HeartCircleHintFilled: FluentIcon;
export declare const HeartCircleHintRegular: FluentIcon;
export declare const HeartOffFilled: FluentIcon;
export declare const HeartOffRegular: FluentIcon;
export declare const HeartPulseFilled: FluentIcon;
export declare const HeartPulseRegular: FluentIcon;
export declare const HeartPulseCheckmarkFilled: FluentIcon;
export declare const HeartPulseCheckmarkRegular: FluentIcon;
export declare const HeartPulseErrorFilled: FluentIcon;
export declare const HeartPulseErrorRegular: FluentIcon;
export declare const HeartPulseWarningFilled: FluentIcon;
export declare const HeartPulseWarningRegular: FluentIcon;
export declare const HexagonFilled: FluentIcon;
export declare const HexagonRegular: FluentIcon;
export declare const HexagonSparkleFilled: FluentIcon;
export declare const HexagonSparkleRegular: FluentIcon;
export declare const HexagonThreeFilled: FluentIcon;
export declare const HexagonThreeRegular: FluentIcon;
export declare const HighlightFilled: FluentIcon;
export declare const HighlightRegular: FluentIcon;
export declare const HighlightAccentFilled: FluentIcon;
export declare const HighlightLinkFilled: FluentIcon;
export declare const HighlightLinkRegular: FluentIcon;
export declare const HighwayFilled: FluentIcon;
export declare const HighwayRegular: FluentIcon;
export declare const HistoryColor: FluentIcon;
export declare const HistoryFilled: FluentIcon;
export declare const HistoryRegular: FluentIcon;
export declare const HistoryDismissFilled: FluentIcon;
export declare const HistoryDismissRegular: FluentIcon;
export declare const HomeColor: FluentIcon;
export declare const HomeFilled: FluentIcon;
export declare const HomeRegular: FluentIcon;
export declare const HomeAddFilled: FluentIcon;
export declare const HomeAddRegular: FluentIcon;
export declare const HomeCheckmarkFilled: FluentIcon;
export declare const HomeCheckmarkRegular: FluentIcon;
export declare const HomeDatabaseFilled: FluentIcon;
export declare const HomeDatabaseRegular: FluentIcon;
export declare const HomeEmptyFilled: FluentIcon;
export declare const HomeEmptyRegular: FluentIcon;
export declare const HomeGarageFilled: FluentIcon;
export declare const HomeGarageRegular: FluentIcon;
export declare const HomeHeartFilled: FluentIcon;
export declare const HomeHeartRegular: FluentIcon;
export declare const HomeMoreFilled: FluentIcon;
export declare const HomeMoreRegular: FluentIcon;
export declare const HomePersonFilled: FluentIcon;
export declare const HomePersonRegular: FluentIcon;
export declare const HomeSplitFilled: FluentIcon;
export declare const HomeSplitRegular: FluentIcon;
export declare const HourglassFilled: FluentIcon;
export declare const HourglassRegular: FluentIcon;
export declare const HourglassHalfFilled: FluentIcon;
export declare const HourglassHalfRegular: FluentIcon;
export declare const HourglassOneQuarterFilled: FluentIcon;
export declare const HourglassOneQuarterRegular: FluentIcon;
export declare const HourglassThreeQuarterFilled: FluentIcon;
export declare const HourglassThreeQuarterRegular: FluentIcon;
export declare const IconsFilled: FluentIcon;
export declare const IconsRegular: FluentIcon;
export declare const ImageColor: FluentIcon;
export declare const ImageFilled: FluentIcon;
export declare const ImageRegular: FluentIcon;
export declare const ImageAddFilled: FluentIcon;
export declare const ImageAddRegular: FluentIcon;
export declare const ImageAltTextFilled: FluentIcon;
export declare const ImageAltTextRegular: FluentIcon;
export declare const ImageArrowBackFilled: FluentIcon;
export declare const ImageArrowBackRegular: FluentIcon;
export declare const ImageArrowCounterclockwiseFilled: FluentIcon;
export declare const ImageArrowCounterclockwiseRegular: FluentIcon;
export declare const ImageArrowForwardFilled: FluentIcon;
export declare const ImageArrowForwardRegular: FluentIcon;
export declare const ImageBorderFilled: FluentIcon;
export declare const ImageBorderRegular: FluentIcon;
export declare const ImageCircleFilled: FluentIcon;
export declare const ImageCircleRegular: FluentIcon;
export declare const ImageCopyFilled: FluentIcon;
export declare const ImageCopyRegular: FluentIcon;
export declare const ImageEditFilled: FluentIcon;
export declare const ImageEditRegular: FluentIcon;
export declare const ImageGlobeFilled: FluentIcon;
export declare const ImageGlobeRegular: FluentIcon;
export declare const ImageMultipleFilled: FluentIcon;
export declare const ImageMultipleRegular: FluentIcon;
export declare const ImageMultipleOffFilled: FluentIcon;
export declare const ImageMultipleOffRegular: FluentIcon;
export declare const ImageOffColor: FluentIcon;
export declare const ImageOffFilled: FluentIcon;
export declare const ImageOffRegular: FluentIcon;
export declare const ImageProhibitedFilled: FluentIcon;
export declare const ImageProhibitedRegular: FluentIcon;
export declare const ImageReflectionFilled: FluentIcon;
export declare const ImageReflectionRegular: FluentIcon;
export declare const ImageSearchFilled: FluentIcon;
export declare const ImageSearchRegular: FluentIcon;
export declare const ImageShadowFilled: FluentIcon;
export declare const ImageShadowRegular: FluentIcon;
export declare const ImageSparkleFilled: FluentIcon;
export declare const ImageSparkleRegular: FluentIcon;
export declare const ImageSplitFilled: FluentIcon;
export declare const ImageSplitRegular: FluentIcon;
export declare const ImageStackFilled: FluentIcon;
export declare const ImageStackRegular: FluentIcon;
export declare const ImageTableFilled: FluentIcon;
export declare const ImageTableRegular: FluentIcon;
export declare const ImmersiveReaderFilled: FluentIcon;
export declare const ImmersiveReaderRegular: FluentIcon;
export declare const ImportantFilled: FluentIcon;
export declare const ImportantRegular: FluentIcon;
export declare const IncognitoFilled: FluentIcon;
export declare const IncognitoRegular: FluentIcon;
export declare const InfoFilled: FluentIcon;
export declare const InfoRegular: FluentIcon;
export declare const InfoShieldFilled: FluentIcon;
export declare const InfoShieldRegular: FluentIcon;
export declare const InfoSparkleFilled: FluentIcon;
export declare const InfoSparkleRegular: FluentIcon;
export declare const InkStrokeFilled: FluentIcon;
export declare const InkStrokeRegular: FluentIcon;
export declare const InkStrokeArrowDownFilled: FluentIcon;
export declare const InkStrokeArrowDownRegular: FluentIcon;
export declare const InkStrokeArrowUpDownFilled: FluentIcon;
export declare const InkStrokeArrowUpDownRegular: FluentIcon;
export declare const InkingToolFilled: FluentIcon;
export declare const InkingToolRegular: FluentIcon;
export declare const InkingToolAccentFilled: FluentIcon;
export declare const InprivateAccountFilled: FluentIcon;
export declare const InprivateAccountRegular: FluentIcon;
export declare const InsertFilled: FluentIcon;
export declare const InsertRegular: FluentIcon;
export declare const IosChevronRightFilled: FluentIcon;
export declare const IosChevronRightRegular: FluentIcon;
export declare const IotFilled: FluentIcon;
export declare const IotRegular: FluentIcon;
export declare const IotAlertFilled: FluentIcon;
export declare const IotAlertRegular: FluentIcon;
export declare const JavascriptFilled: FluentIcon;
export declare const JavascriptRegular: FluentIcon;
export declare const JoystickFilled: FluentIcon;
export declare const JoystickRegular: FluentIcon;
export declare const KeyFilled: FluentIcon;
export declare const KeyRegular: FluentIcon;
export declare const KeyCommandFilled: FluentIcon;
export declare const KeyCommandRegular: FluentIcon;
export declare const KeyMultipleFilled: FluentIcon;
export declare const KeyMultipleRegular: FluentIcon;
export declare const KeyResetFilled: FluentIcon;
export declare const KeyResetRegular: FluentIcon;
export declare const Keyboard123Filled: FluentIcon;
export declare const Keyboard123Regular: FluentIcon;
export declare const KeyboardFilled: FluentIcon;
export declare const KeyboardRegular: FluentIcon;
export declare const KeyboardDockFilled: FluentIcon;
export declare const KeyboardDockRegular: FluentIcon;
export declare const KeyboardLayoutFloatFilled: FluentIcon;
export declare const KeyboardLayoutFloatRegular: FluentIcon;
export declare const KeyboardLayoutOneHandedLeftFilled: FluentIcon;
export declare const KeyboardLayoutOneHandedLeftRegular: FluentIcon;
export declare const KeyboardLayoutResizeFilled: FluentIcon;
export declare const KeyboardLayoutResizeRegular: FluentIcon;
export declare const KeyboardLayoutSplitFilled: FluentIcon;
export declare const KeyboardLayoutSplitRegular: FluentIcon;
export declare const KeyboardShiftFilled: FluentIcon;
export declare const KeyboardShiftRegular: FluentIcon;
export declare const KeyboardShiftUppercaseFilled: FluentIcon;
export declare const KeyboardShiftUppercaseRegular: FluentIcon;
export declare const KeyboardTabFilled: FluentIcon;
export declare const KeyboardTabRegular: FluentIcon;
export declare const LaptopColor: FluentIcon;
export declare const LaptopFilled: FluentIcon;
export declare const LaptopRegular: FluentIcon;
export declare const LaptopBriefcaseFilled: FluentIcon;
export declare const LaptopBriefcaseRegular: FluentIcon;
export declare const LaptopDismissFilled: FluentIcon;
export declare const LaptopDismissRegular: FluentIcon;
export declare const LaptopPersonFilled: FluentIcon;
export declare const LaptopPersonRegular: FluentIcon;
export declare const LaptopSettingsFilled: FluentIcon;
export declare const LaptopSettingsRegular: FluentIcon;
export declare const LaptopShieldFilled: FluentIcon;
export declare const LaptopShieldRegular: FluentIcon;
export declare const LaserToolFilled: FluentIcon;
export declare const LaserToolRegular: FluentIcon;
export declare const LassoFilled: FluentIcon;
export declare const LassoRegular: FluentIcon;
export declare const LauncherSettingsFilled: FluentIcon;
export declare const LauncherSettingsRegular: FluentIcon;
export declare const LayerFilled: FluentIcon;
export declare const LayerRegular: FluentIcon;
export declare const LayerDiagonalFilled: FluentIcon;
export declare const LayerDiagonalRegular: FluentIcon;
export declare const LayerDiagonalAddFilled: FluentIcon;
export declare const LayerDiagonalAddRegular: FluentIcon;
export declare const LayerDiagonalPersonColor: FluentIcon;
export declare const LayerDiagonalPersonFilled: FluentIcon;
export declare const LayerDiagonalPersonRegular: FluentIcon;
export declare const LayerDiagonalSparkleFilled: FluentIcon;
export declare const LayerDiagonalSparkleRegular: FluentIcon;
export declare const LayoutCellFourFilled: FluentIcon;
export declare const LayoutCellFourRegular: FluentIcon;
export declare const LayoutCellFourFocusBottomLeftFilled: FluentIcon;
export declare const LayoutCellFourFocusBottomRightFilled: FluentIcon;
export declare const LayoutCellFourFocusTopLeftFilled: FluentIcon;
export declare const LayoutCellFourFocusTopRightFilled: FluentIcon;
export declare const LayoutColumnFourFilled: FluentIcon;
export declare const LayoutColumnFourRegular: FluentIcon;
export declare const LayoutColumnFourFocusCenterLeftFilled: FluentIcon;
export declare const LayoutColumnFourFocusCenterRightFilled: FluentIcon;
export declare const LayoutColumnFourFocusLeftFilled: FluentIcon;
export declare const LayoutColumnFourFocusRightFilled: FluentIcon;
export declare const LayoutColumnOneThirdLeftFilled: FluentIcon;
export declare const LayoutColumnOneThirdLeftRegular: FluentIcon;
export declare const LayoutColumnOneThirdRightFilled: FluentIcon;
export declare const LayoutColumnOneThirdRightRegular: FluentIcon;
export declare const LayoutColumnOneThirdRightHintFilled: FluentIcon;
export declare const LayoutColumnOneThirdRightHintRegular: FluentIcon;
export declare const LayoutColumnThreeFilled: FluentIcon;
export declare const LayoutColumnThreeRegular: FluentIcon;
export declare const LayoutColumnThreeFocusCenterFilled: FluentIcon;
export declare const LayoutColumnThreeFocusLeftFilled: FluentIcon;
export declare const LayoutColumnThreeFocusRightFilled: FluentIcon;
export declare const LayoutColumnTwoFilled: FluentIcon;
export declare const LayoutColumnTwoRegular: FluentIcon;
export declare const LayoutColumnTwoFocusLeftFilled: FluentIcon;
export declare const LayoutColumnTwoFocusRightFilled: FluentIcon;
export declare const LayoutColumnTwoSplitLeftFilled: FluentIcon;
export declare const LayoutColumnTwoSplitLeftRegular: FluentIcon;
export declare const LayoutColumnTwoSplitLeftFocusBottomLeftFilled: FluentIcon;
export declare const LayoutColumnTwoSplitLeftFocusRightFilled: FluentIcon;
export declare const LayoutColumnTwoSplitLeftFocusTopLeftFilled: FluentIcon;
export declare const LayoutColumnTwoSplitRightFilled: FluentIcon;
export declare const LayoutColumnTwoSplitRightRegular: FluentIcon;
export declare const LayoutColumnTwoSplitRightFocusBottomRightFilled: FluentIcon;
export declare const LayoutColumnTwoSplitRightFocusLeftFilled: FluentIcon;
export declare const LayoutColumnTwoSplitRightFocusTopRightFilled: FluentIcon;
export declare const LayoutDynamicFilled: FluentIcon;
export declare const LayoutDynamicRegular: FluentIcon;
export declare const LayoutRowFourFilled: FluentIcon;
export declare const LayoutRowFourRegular: FluentIcon;
export declare const LayoutRowFourFocusBottomFilled: FluentIcon;
export declare const LayoutRowFourFocusCenterBottomFilled: FluentIcon;
export declare const LayoutRowFourFocusCenterTopFilled: FluentIcon;
export declare const LayoutRowFourFocusTopFilled: FluentIcon;
export declare const LayoutRowThreeFilled: FluentIcon;
export declare const LayoutRowThreeRegular: FluentIcon;
export declare const LayoutRowThreeFocusBottomFilled: FluentIcon;
export declare const LayoutRowThreeFocusCenterFilled: FluentIcon;
export declare const LayoutRowThreeFocusTopFilled: FluentIcon;
export declare const LayoutRowTwoFilled: FluentIcon;
export declare const LayoutRowTwoRegular: FluentIcon;
export declare const LayoutRowTwoFocusBottomFilled: FluentIcon;
export declare const LayoutRowTwoFocusTopFilled: FluentIcon;
export declare const LayoutRowTwoFocusTopSettingsFilled: FluentIcon;
export declare const LayoutRowTwoSettingsFilled: FluentIcon;
export declare const LayoutRowTwoSettingsRegular: FluentIcon;
export declare const LayoutRowTwoSplitBottomFilled: FluentIcon;
export declare const LayoutRowTwoSplitBottomRegular: FluentIcon;
export declare const LayoutRowTwoSplitBottomFocusBottomLeftFilled: FluentIcon;
export declare const LayoutRowTwoSplitBottomFocusBottomRightFilled: FluentIcon;
export declare const LayoutRowTwoSplitBottomFocusTopFilled: FluentIcon;
export declare const LayoutRowTwoSplitTopFilled: FluentIcon;
export declare const LayoutRowTwoSplitTopRegular: FluentIcon;
export declare const LayoutRowTwoSplitTopFocusBottomFilled: FluentIcon;
export declare const LayoutRowTwoSplitTopFocusTopLeftFilled: FluentIcon;
export declare const LayoutRowTwoSplitTopFocusTopRightFilled: FluentIcon;
export declare const LeafOneFilled: FluentIcon;
export declare const LeafOneRegular: FluentIcon;
export declare const LeafThreeFilled: FluentIcon;
export declare const LeafThreeRegular: FluentIcon;
export declare const LeafTwoFilled: FluentIcon;
export declare const LeafTwoRegular: FluentIcon;
export declare const LearningAppFilled: FluentIcon;
export declare const LearningAppRegular: FluentIcon;
export declare const LibraryColor: FluentIcon;
export declare const LibraryFilled: FluentIcon;
export declare const LibraryRegular: FluentIcon;
export declare const LightbulbColor: FluentIcon;
export declare const LightbulbFilled: FluentIcon;
export declare const LightbulbRegular: FluentIcon;
export declare const LightbulbCheckmarkColor: FluentIcon;
export declare const LightbulbCheckmarkFilled: FluentIcon;
export declare const LightbulbCheckmarkRegular: FluentIcon;
export declare const LightbulbCircleFilled: FluentIcon;
export declare const LightbulbCircleRegular: FluentIcon;
export declare const LightbulbFilamentColor: FluentIcon;
export declare const LightbulbFilamentFilled: FluentIcon;
export declare const LightbulbFilamentRegular: FluentIcon;
export declare const LightbulbPersonFilled: FluentIcon;
export declare const LightbulbPersonRegular: FluentIcon;
export declare const LikertFilled: FluentIcon;
export declare const LikertRegular: FluentIcon;
export declare const LineFilled: FluentIcon;
export declare const LineRegular: FluentIcon;
export declare const LineDashesFilled: FluentIcon;
export declare const LineDashesRegular: FluentIcon;
export declare const LineFlowDiagonalUpRightFilled: FluentIcon;
export declare const LineFlowDiagonalUpRightRegular: FluentIcon;
export declare const LineHorizontal1Filled: FluentIcon;
export declare const LineHorizontal1Regular: FluentIcon;
export declare const LineHorizontal1DashDotDashFilled: FluentIcon;
export declare const LineHorizontal1DashDotDashRegular: FluentIcon;
export declare const LineHorizontal1DashesFilled: FluentIcon;
export declare const LineHorizontal1DashesRegular: FluentIcon;
export declare const LineHorizontal1DotFilled: FluentIcon;
export declare const LineHorizontal1DotRegular: FluentIcon;
export declare const LineHorizontal2DashesSolidFilled: FluentIcon;
export declare const LineHorizontal2DashesSolidRegular: FluentIcon;
export declare const LineHorizontal3Filled: FluentIcon;
export declare const LineHorizontal3Regular: FluentIcon;
export declare const LineHorizontal4Filled: FluentIcon;
export declare const LineHorizontal4Regular: FluentIcon;
export declare const LineHorizontal4SearchFilled: FluentIcon;
export declare const LineHorizontal4SearchRegular: FluentIcon;
export declare const LineHorizontal5Filled: FluentIcon;
export declare const LineHorizontal5Regular: FluentIcon;
export declare const LineHorizontal5ErrorFilled: FluentIcon;
export declare const LineHorizontal5ErrorRegular: FluentIcon;
export declare const LineStyleFilled: FluentIcon;
export declare const LineStyleRegular: FluentIcon;
export declare const LineStyleSketchFilled: FluentIcon;
export declare const LineStyleSketchRegular: FluentIcon;
export declare const LineThicknessFilled: FluentIcon;
export declare const LineThicknessRegular: FluentIcon;
export declare const LinkColor: FluentIcon;
export declare const LinkFilled: FluentIcon;
export declare const LinkRegular: FluentIcon;
export declare const LinkAddFilled: FluentIcon;
export declare const LinkAddRegular: FluentIcon;
export declare const LinkDismissFilled: FluentIcon;
export declare const LinkDismissRegular: FluentIcon;
export declare const LinkEditFilled: FluentIcon;
export declare const LinkEditRegular: FluentIcon;
export declare const LinkMultipleColor: FluentIcon;
export declare const LinkMultipleFilled: FluentIcon;
export declare const LinkMultipleRegular: FluentIcon;
export declare const LinkPersonFilled: FluentIcon;
export declare const LinkPersonRegular: FluentIcon;
export declare const LinkSquareFilled: FluentIcon;
export declare const LinkSquareRegular: FluentIcon;
export declare const LinkToolboxFilled: FluentIcon;
export declare const LinkToolboxRegular: FluentIcon;
export declare const ListFilled: FluentIcon;
export declare const ListRegular: FluentIcon;
export declare const ListBarColor: FluentIcon;
export declare const ListBarFilled: FluentIcon;
export declare const ListBarRegular: FluentIcon;
export declare const ListBarTreeFilled: FluentIcon;
export declare const ListBarTreeRegular: FluentIcon;
export declare const ListBarTreeOffsetFilled: FluentIcon;
export declare const ListBarTreeOffsetRegular: FluentIcon;
export declare const ListRtlFilled: FluentIcon;
export declare const ListRtlRegular: FluentIcon;
export declare const LiveFilled: FluentIcon;
export declare const LiveRegular: FluentIcon;
export declare const LiveOffFilled: FluentIcon;
export declare const LiveOffRegular: FluentIcon;
export declare const LocalLanguageFilled: FluentIcon;
export declare const LocalLanguageRegular: FluentIcon;
export declare const LocationFilled: FluentIcon;
export declare const LocationRegular: FluentIcon;
export declare const LocationAddFilled: FluentIcon;
export declare const LocationAddRegular: FluentIcon;
export declare const LocationAddLeftFilled: FluentIcon;
export declare const LocationAddLeftRegular: FluentIcon;
export declare const LocationAddRightFilled: FluentIcon;
export declare const LocationAddRightRegular: FluentIcon;
export declare const LocationAddUpFilled: FluentIcon;
export declare const LocationAddUpRegular: FluentIcon;
export declare const LocationArrowFilled: FluentIcon;
export declare const LocationArrowRegular: FluentIcon;
export declare const LocationArrowLeftFilled: FluentIcon;
export declare const LocationArrowLeftRegular: FluentIcon;
export declare const LocationArrowRightFilled: FluentIcon;
export declare const LocationArrowRightRegular: FluentIcon;
export declare const LocationArrowUpFilled: FluentIcon;
export declare const LocationArrowUpRegular: FluentIcon;
export declare const LocationCheckmarkFilled: FluentIcon;
export declare const LocationCheckmarkRegular: FluentIcon;
export declare const LocationDismissFilled: FluentIcon;
export declare const LocationDismissRegular: FluentIcon;
export declare const LocationLiveFilled: FluentIcon;
export declare const LocationLiveRegular: FluentIcon;
export declare const LocationOffFilled: FluentIcon;
export declare const LocationOffRegular: FluentIcon;
export declare const LocationRippleColor: FluentIcon;
export declare const LocationRippleFilled: FluentIcon;
export declare const LocationRippleRegular: FluentIcon;
export declare const LocationSettingsFilled: FluentIcon;
export declare const LocationSettingsRegular: FluentIcon;
export declare const LocationTargetSquareFilled: FluentIcon;
export declare const LocationTargetSquareRegular: FluentIcon;
export declare const LockClosedColor: FluentIcon;
export declare const LockClosedFilled: FluentIcon;
export declare const LockClosedRegular: FluentIcon;
export declare const LockClosedKeyFilled: FluentIcon;
export declare const LockClosedKeyRegular: FluentIcon;
export declare const LockClosedRibbonFilled: FluentIcon;
export declare const LockClosedRibbonRegular: FluentIcon;
export declare const LockMultipleFilled: FluentIcon;
export declare const LockMultipleRegular: FluentIcon;
export declare const LockOpenFilled: FluentIcon;
export declare const LockOpenRegular: FluentIcon;
export declare const LockShieldColor: FluentIcon;
export declare const LockShieldFilled: FluentIcon;
export declare const LockShieldRegular: FluentIcon;
export declare const LotteryFilled: FluentIcon;
export declare const LotteryRegular: FluentIcon;
export declare const LuggageFilled: FluentIcon;
export declare const LuggageRegular: FluentIcon;
export declare const MailColor: FluentIcon;
export declare const MailFilled: FluentIcon;
export declare const MailRegular: FluentIcon;
export declare const MailAddFilled: FluentIcon;
export declare const MailAddRegular: FluentIcon;
export declare const MailAlertColor: FluentIcon;
export declare const MailAlertFilled: FluentIcon;
export declare const MailAlertRegular: FluentIcon;
export declare const MailAllReadFilled: FluentIcon;
export declare const MailAllReadRegular: FluentIcon;
export declare const MailAllUnreadFilled: FluentIcon;
export declare const MailAllUnreadRegular: FluentIcon;
export declare const MailArrowClockwiseFilled: FluentIcon;
export declare const MailArrowClockwiseRegular: FluentIcon;
export declare const MailArrowDoubleBackFilled: FluentIcon;
export declare const MailArrowDoubleBackRegular: FluentIcon;
export declare const MailArrowDownFilled: FluentIcon;
export declare const MailArrowDownRegular: FluentIcon;
export declare const MailArrowForwardFilled: FluentIcon;
export declare const MailArrowForwardRegular: FluentIcon;
export declare const MailArrowUpFilled: FluentIcon;
export declare const MailArrowUpRegular: FluentIcon;
export declare const MailAttachFilled: FluentIcon;
export declare const MailAttachRegular: FluentIcon;
export declare const MailCheckmarkFilled: FluentIcon;
export declare const MailCheckmarkRegular: FluentIcon;
export declare const MailClockColor: FluentIcon;
export declare const MailClockFilled: FluentIcon;
export declare const MailClockRegular: FluentIcon;
export declare const MailCopyFilled: FluentIcon;
export declare const MailCopyRegular: FluentIcon;
export declare const MailDataBarFilled: FluentIcon;
export declare const MailDataBarRegular: FluentIcon;
export declare const MailDismissFilled: FluentIcon;
export declare const MailDismissRegular: FluentIcon;
export declare const MailEditFilled: FluentIcon;
export declare const MailEditRegular: FluentIcon;
export declare const MailErrorFilled: FluentIcon;
export declare const MailErrorRegular: FluentIcon;
export declare const MailFishHookFilled: FluentIcon;
export declare const MailFishHookRegular: FluentIcon;
export declare const MailInboxFilled: FluentIcon;
export declare const MailInboxRegular: FluentIcon;
export declare const MailInboxAddFilled: FluentIcon;
export declare const MailInboxAddRegular: FluentIcon;
