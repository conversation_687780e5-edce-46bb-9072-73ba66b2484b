/*
 * ATTENTION: The "eval" devtool has been used (maybe by default in mode: "development").
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
var pcf_tools_652ac3f36e1e4bca82eb3c1dc44e6fad;
/******/ (() => { // webpackBootstrap
/******/ 	"use strict";
/******/ 	var __webpack_modules__ = ({

/***/ "./TimeTracking/TimeTrackingPanel.tsx":
/*!********************************************!*\
  !*** ./TimeTracking/TimeTrackingPanel.tsx ***!
  \********************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

eval("{\n\nvar __extends = this && this.__extends || function () {\n  var _extendStatics = function extendStatics(d, b) {\n    _extendStatics = Object.setPrototypeOf || {\n      __proto__: []\n    } instanceof Array && function (d, b) {\n      d.__proto__ = b;\n    } || function (d, b) {\n      for (var p in b) if (Object.prototype.hasOwnProperty.call(b, p)) d[p] = b[p];\n    };\n    return _extendStatics(d, b);\n  };\n  return function (d, b) {\n    if (typeof b !== \"function\" && b !== null) throw new TypeError(\"Class extends value \" + String(b) + \" is not a constructor or null\");\n    _extendStatics(d, b);\n    function __() {\n      this.constructor = d;\n    }\n    d.prototype = b === null ? Object.create(b) : (__.prototype = b.prototype, new __());\n  };\n}();\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.TimeTrackingPanel = void 0;\nvar React = __webpack_require__(/*! react */ \"react\");\nvar react_components_1 = __webpack_require__(/*! @fluentui/react-components */ \"@fluentui/react-components\");\nvar useStyles = (0, react_components_1.makeStyles)({\n  container: {\n    display: 'flex',\n    flexDirection: 'column',\n    gap: react_components_1.tokens.spacingVerticalM,\n    padding: react_components_1.tokens.spacingVerticalL,\n    maxWidth: '400px'\n  },\n  dropdownField: {\n    display: 'flex',\n    flexDirection: 'column',\n    gap: react_components_1.tokens.spacingVerticalXS\n  },\n  buttonContainer: {\n    display: 'flex',\n    gap: react_components_1.tokens.spacingHorizontalM,\n    marginTop: react_components_1.tokens.spacingVerticalM\n  },\n  card: {\n    marginTop: react_components_1.tokens.spacingVerticalM,\n    padding: react_components_1.tokens.spacingVerticalM\n  }\n});\nvar TimeTrackingPanel = /** @class */function (_super) {\n  __extends(TimeTrackingPanel, _super);\n  function TimeTrackingPanel(props) {\n    var _this = _super.call(this, props) || this;\n    _this.handleProjectChange = function (_event, data) {\n      _this.setState({\n        selectedProject: data.optionValue || ''\n      });\n    };\n    _this.handleTaskChange = function (_event, data) {\n      _this.setState({\n        selectedTask: data.optionValue || ''\n      });\n    };\n    _this.handleStatusChange = function (_event, data) {\n      _this.setState({\n        selectedStatus: data.optionValue || ''\n      });\n    };\n    _this.handleStartTracking = function () {\n      console.log('Starting time tracking:', {\n        project: _this.state.selectedProject,\n        task: _this.state.selectedTask,\n        status: _this.state.selectedStatus\n      });\n      // Add your time tracking logic here\n    };\n    _this.handleStopTracking = function () {\n      console.log('Stopping time tracking');\n      // Add your stop tracking logic here\n    };\n    _this.state = {\n      selectedProject: '',\n      selectedTask: '',\n      selectedStatus: ''\n    };\n    return _this;\n  }\n  TimeTrackingPanel.prototype.render = function () {\n    var styles = useStyles();\n    return React.createElement(\"div\", {\n      className: styles.container\n    }, React.createElement(react_components_1.Text, {\n      size: 500,\n      weight: \"semibold\"\n    }, \"Time Tracking - Hello \", this.props.name, \"!\"), React.createElement(react_components_1.Field, {\n      label: \"Select Project\",\n      className: styles.dropdownField\n    }, React.createElement(react_components_1.Dropdown, {\n      placeholder: \"Choose a project...\",\n      value: this.state.selectedProject,\n      onOptionSelect: this.handleProjectChange\n    }, React.createElement(react_components_1.Option, {\n      value: \"project1\"\n    }, \"CoreFin Development\"), React.createElement(react_components_1.Option, {\n      value: \"project2\"\n    }, \"Time Tracking Module\"), React.createElement(react_components_1.Option, {\n      value: \"project3\"\n    }, \"User Interface Updates\"), React.createElement(react_components_1.Option, {\n      value: \"project4\"\n    }, \"Bug Fixes\"), React.createElement(react_components_1.Option, {\n      value: \"project5\"\n    }, \"Documentation\"))), React.createElement(react_components_1.Field, {\n      label: \"Select Task Type\",\n      className: styles.dropdownField\n    }, React.createElement(react_components_1.Dropdown, {\n      placeholder: \"Choose a task type...\",\n      value: this.state.selectedTask,\n      onOptionSelect: this.handleTaskChange\n    }, React.createElement(react_components_1.Option, {\n      value: \"development\"\n    }, \"Development\"), React.createElement(react_components_1.Option, {\n      value: \"testing\"\n    }, \"Testing\"), React.createElement(react_components_1.Option, {\n      value: \"debugging\"\n    }, \"Debugging\"), React.createElement(react_components_1.Option, {\n      value: \"documentation\"\n    }, \"Documentation\"), React.createElement(react_components_1.Option, {\n      value: \"meeting\"\n    }, \"Meeting\"), React.createElement(react_components_1.Option, {\n      value: \"research\"\n    }, \"Research\"), React.createElement(react_components_1.Option, {\n      value: \"review\"\n    }, \"Code Review\"))), React.createElement(react_components_1.Field, {\n      label: \"Status\",\n      className: styles.dropdownField\n    }, React.createElement(react_components_1.Dropdown, {\n      placeholder: \"Select status...\",\n      value: this.state.selectedStatus,\n      onOptionSelect: this.handleStatusChange\n    }, React.createElement(react_components_1.Option, {\n      value: \"not-started\"\n    }, \"Not Started\"), React.createElement(react_components_1.Option, {\n      value: \"in-progress\"\n    }, \"In Progress\"), React.createElement(react_components_1.Option, {\n      value: \"blocked\"\n    }, \"Blocked\"), React.createElement(react_components_1.Option, {\n      value: \"completed\"\n    }, \"Completed\"), React.createElement(react_components_1.Option, {\n      value: \"on-hold\"\n    }, \"On Hold\"))), React.createElement(\"div\", {\n      className: styles.buttonContainer\n    }, React.createElement(react_components_1.Button, {\n      appearance: \"primary\",\n      onClick: this.handleStartTracking,\n      disabled: !this.state.selectedProject || !this.state.selectedTask\n    }, \"Start Tracking\"), React.createElement(react_components_1.Button, {\n      appearance: \"secondary\",\n      onClick: this.handleStopTracking\n    }, \"Stop Tracking\")), (this.state.selectedProject || this.state.selectedTask || this.state.selectedStatus) && React.createElement(react_components_1.Card, {\n      className: styles.card\n    }, React.createElement(react_components_1.CardHeader, {\n      header: React.createElement(react_components_1.Text, {\n        weight: \"semibold\"\n      }, \"Current Selection\")\n    }), React.createElement(react_components_1.CardPreview, null, React.createElement(\"div\", null, this.state.selectedProject && React.createElement(react_components_1.Text, null, \"Project: \", this.state.selectedProject), this.state.selectedTask && React.createElement(react_components_1.Text, null, \"Task: \", this.state.selectedTask), this.state.selectedStatus && React.createElement(react_components_1.Text, null, \"Status: \", this.state.selectedStatus)))));\n  };\n  return TimeTrackingPanel;\n}(React.Component);\nexports.TimeTrackingPanel = TimeTrackingPanel;\n\n//# sourceURL=webpack://pcf_tools_652ac3f36e1e4bca82eb3c1dc44e6fad/./TimeTracking/TimeTrackingPanel.tsx?\n}");

/***/ }),

/***/ "./TimeTracking/index.ts":
/*!*******************************!*\
  !*** ./TimeTracking/index.ts ***!
  \*******************************/
/***/ ((__unused_webpack_module, exports, __webpack_require__) => {

eval("{\n\nObject.defineProperty(exports, \"__esModule\", ({\n  value: true\n}));\nexports.TimeTracking = void 0;\nvar TimeTrackingPanel_1 = __webpack_require__(/*! ./TimeTrackingPanel */ \"./TimeTracking/TimeTrackingPanel.tsx\");\nvar React = __webpack_require__(/*! react */ \"react\");\nvar TimeTracking = /** @class */function () {\n  /**\n   * Empty constructor.\n   */\n  function TimeTracking() {\n    // Empty\n  }\n  /**\n   * Used to initialize the control instance. Controls can kick off remote server calls and other initialization actions here.\n   * Data-set values are not initialized here, use updateView.\n   * @param context The entire property bag available to control via Context Object; It contains values as set up by the customizer mapped to property names defined in the manifest, as well as utility functions.\n   * @param notifyOutputChanged A callback method to alert the framework that the control has new outputs ready to be retrieved asynchronously.\n   * @param state A piece of data that persists in one session for a single user. Can be set at any point in a controls life cycle by calling 'setControlState' in the Mode interface.\n   */\n  TimeTracking.prototype.init = function (context, notifyOutputChanged, state) {\n    this.notifyOutputChanged = notifyOutputChanged;\n  };\n  /**\n   * Called when any value in the property bag has changed. This includes field values, data-sets, global values such as container height and width, offline status, control metadata values such as label, visible, etc.\n   * @param context The entire property bag available to control via Context Object; It contains values as set up by the customizer mapped to names defined in the manifest, as well as utility functions\n   * @returns ReactElement root react element for the control\n   */\n  TimeTracking.prototype.updateView = function (context) {\n    var props = {\n      name: 'Power Apps'\n    };\n    return React.createElement(TimeTrackingPanel_1.TimeTrackingPanel, props);\n  };\n  /**\n   * It is called by the framework prior to a control receiving new data.\n   * @returns an object based on nomenclature defined in manifest, expecting object[s] for property marked as \"bound\" or \"output\"\n   */\n  TimeTracking.prototype.getOutputs = function () {\n    return {};\n  };\n  /**\n   * Called when the control is to be removed from the DOM tree. Controls should use this call for cleanup.\n   * i.e. cancelling any pending remote calls, removing listeners, etc.\n   */\n  TimeTracking.prototype.destroy = function () {\n    // Add code to cleanup control if necessary\n  };\n  return TimeTracking;\n}();\nexports.TimeTracking = TimeTracking;\n\n//# sourceURL=webpack://pcf_tools_652ac3f36e1e4bca82eb3c1dc44e6fad/./TimeTracking/index.ts?\n}");

/***/ }),

/***/ "@fluentui/react-components":
/*!************************************!*\
  !*** external "FluentUIReactv940" ***!
  \************************************/
/***/ ((module) => {

module.exports = FluentUIReactv940;

/***/ }),

/***/ "react":
/*!***************************!*\
  !*** external "Reactv16" ***!
  \***************************/
/***/ ((module) => {

module.exports = Reactv16;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId].call(module.exports, module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module can't be inlined because the eval devtool is used.
/******/ 	var __webpack_exports__ = __webpack_require__("./TimeTracking/index.ts");
/******/ 	pcf_tools_652ac3f36e1e4bca82eb3c1dc44e6fad = __webpack_exports__;
/******/ 	
/******/ })()
;
if (window.ComponentFramework && window.ComponentFramework.registerControl) {
	ComponentFramework.registerControl('CoreFinTimeTracking.TimeTracking', pcf_tools_652ac3f36e1e4bca82eb3c1dc44e6fad.TimeTracking);
} else {
	var CoreFinTimeTracking = CoreFinTimeTracking || {};
	CoreFinTimeTracking.TimeTracking = pcf_tools_652ac3f36e1e4bca82eb3c1dc44e6fad.TimeTracking;
	pcf_tools_652ac3f36e1e4bca82eb3c1dc44e6fad = undefined;
}