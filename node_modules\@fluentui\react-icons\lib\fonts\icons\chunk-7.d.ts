import type { FluentFontIcon } from "../../utils/fonts/createFluentFontIcon";
export declare const PhoneVerticalScrollFilled: FluentFontIcon;
export declare const PhoneVerticalScrollRegular: FluentFontIcon;
export declare const PhoneVibrateFilled: FluentFontIcon;
export declare const PhoneVibrateRegular: FluentFontIcon;
export declare const PhotoFilterFilled: FluentFontIcon;
export declare const PhotoFilterRegular: FluentFontIcon;
export declare const PiFilled: FluentFontIcon;
export declare const PiRegular: FluentFontIcon;
export declare const PictureInPictureFilled: FluentFontIcon;
export declare const PictureInPictureRegular: FluentFontIcon;
export declare const PictureInPictureEnterFilled: FluentFontIcon;
export declare const PictureInPictureEnterRegular: FluentFontIcon;
export declare const PictureInPictureExitFilled: FluentFontIcon;
export declare const PictureInPictureExitRegular: FluentFontIcon;
export declare const PillFilled: FluentFontIcon;
export declare const PillRegular: FluentFontIcon;
export declare const PinFilled: FluentFontIcon;
export declare const PinRegular: FluentFontIcon;
export declare const PinGlobeFilled: FluentFontIcon;
export declare const PinGlobeRegular: FluentFontIcon;
export declare const PinOffFilled: FluentFontIcon;
export declare const PinOffRegular: FluentFontIcon;
export declare const PipelineFilled: FluentFontIcon;
export declare const PipelineRegular: FluentFontIcon;
export declare const PipelineAddFilled: FluentFontIcon;
export declare const PipelineAddRegular: FluentFontIcon;
export declare const PipelineArrowCurveDownFilled: FluentFontIcon;
export declare const PipelineArrowCurveDownRegular: FluentFontIcon;
export declare const PipelinePlayFilled: FluentFontIcon;
export declare const PipelinePlayRegular: FluentFontIcon;
export declare const PivotFilled: FluentFontIcon;
export declare const PivotRegular: FluentFontIcon;
export declare const PlanetFilled: FluentFontIcon;
export declare const PlanetRegular: FluentFontIcon;
export declare const PlantCattailFilled: FluentFontIcon;
export declare const PlantCattailRegular: FluentFontIcon;
export declare const PlantGrassFilled: FluentFontIcon;
export declare const PlantGrassRegular: FluentFontIcon;
export declare const PlantRagweedFilled: FluentFontIcon;
export declare const PlantRagweedRegular: FluentFontIcon;
export declare const PlayFilled: FluentFontIcon;
export declare const PlayRegular: FluentFontIcon;
export declare const PlayCircleFilled: FluentFontIcon;
export declare const PlayCircleRegular: FluentFontIcon;
export declare const PlayCircleHintFilled: FluentFontIcon;
export declare const PlayCircleHintRegular: FluentFontIcon;
export declare const PlayCircleHintHalfFilled: FluentFontIcon;
export declare const PlayCircleHintHalfRegular: FluentFontIcon;
export declare const PlayCircleSparkleFilled: FluentFontIcon;
export declare const PlayCircleSparkleRegular: FluentFontIcon;
export declare const PlaySettingsFilled: FluentFontIcon;
export declare const PlaySettingsRegular: FluentFontIcon;
export declare const PlayingCardsFilled: FluentFontIcon;
export declare const PlayingCardsRegular: FluentFontIcon;
export declare const PlugConnectedFilled: FluentFontIcon;
export declare const PlugConnectedRegular: FluentFontIcon;
export declare const PlugConnectedAddFilled: FluentFontIcon;
export declare const PlugConnectedAddRegular: FluentFontIcon;
export declare const PlugConnectedCheckmarkFilled: FluentFontIcon;
export declare const PlugConnectedCheckmarkRegular: FluentFontIcon;
export declare const PlugConnectedSettingsFilled: FluentFontIcon;
export declare const PlugConnectedSettingsRegular: FluentFontIcon;
export declare const PlugDisconnectedFilled: FluentFontIcon;
export declare const PlugDisconnectedRegular: FluentFontIcon;
export declare const PointScanFilled: FluentFontIcon;
export declare const PointScanRegular: FluentFontIcon;
export declare const PollFilled: FluentFontIcon;
export declare const PollRegular: FluentFontIcon;
export declare const PollHorizontalFilled: FluentFontIcon;
export declare const PollHorizontalRegular: FluentFontIcon;
export declare const PollOffFilled: FluentFontIcon;
export declare const PollOffRegular: FluentFontIcon;
export declare const PortHdmiFilled: FluentFontIcon;
export declare const PortHdmiRegular: FluentFontIcon;
export declare const PortMicroUsbFilled: FluentFontIcon;
export declare const PortMicroUsbRegular: FluentFontIcon;
export declare const PortUsbAFilled: FluentFontIcon;
export declare const PortUsbARegular: FluentFontIcon;
export declare const PortUsbCFilled: FluentFontIcon;
export declare const PortUsbCRegular: FluentFontIcon;
export declare const PositionBackwardFilled: FluentFontIcon;
export declare const PositionBackwardRegular: FluentFontIcon;
export declare const PositionForwardFilled: FluentFontIcon;
export declare const PositionForwardRegular: FluentFontIcon;
export declare const PositionToBackFilled: FluentFontIcon;
export declare const PositionToBackRegular: FluentFontIcon;
export declare const PositionToFrontFilled: FluentFontIcon;
export declare const PositionToFrontRegular: FluentFontIcon;
export declare const PowerFilled: FluentFontIcon;
export declare const PowerRegular: FluentFontIcon;
export declare const PredictionsFilled: FluentFontIcon;
export declare const PredictionsRegular: FluentFontIcon;
export declare const PremiumFilled: FluentFontIcon;
export declare const PremiumRegular: FluentFontIcon;
export declare const PremiumPersonFilled: FluentFontIcon;
export declare const PremiumPersonRegular: FluentFontIcon;
export declare const PresenceAvailableFilled: FluentFontIcon;
export declare const PresenceAvailableRegular: FluentFontIcon;
export declare const PresenceAwayFilled: FluentFontIcon;
export declare const PresenceAwayRegular: FluentFontIcon;
export declare const PresenceBlockedRegular: FluentFontIcon;
export declare const PresenceBusyFilled: FluentFontIcon;
export declare const PresenceDndFilled: FluentFontIcon;
export declare const PresenceDndRegular: FluentFontIcon;
export declare const PresenceOfflineRegular: FluentFontIcon;
export declare const PresenceOofRegular: FluentFontIcon;
export declare const PresenceTentativeRegular: FluentFontIcon;
export declare const PresenceUnknownRegular: FluentFontIcon;
export declare const PresenterFilled: FluentFontIcon;
export declare const PresenterRegular: FluentFontIcon;
export declare const PresenterOffFilled: FluentFontIcon;
export declare const PresenterOffRegular: FluentFontIcon;
export declare const PreviewLinkFilled: FluentFontIcon;
export declare const PreviewLinkRegular: FluentFontIcon;
export declare const PreviousFilled: FluentFontIcon;
export declare const PreviousRegular: FluentFontIcon;
export declare const PreviousFrameFilled: FluentFontIcon;
export declare const PreviousFrameRegular: FluentFontIcon;
export declare const PrintFilled: FluentFontIcon;
export declare const PrintRegular: FluentFontIcon;
export declare const PrintAddFilled: FluentFontIcon;
export declare const PrintAddRegular: FluentFontIcon;
export declare const ProductionFilled: FluentFontIcon;
export declare const ProductionRegular: FluentFontIcon;
export declare const ProductionCheckmarkFilled: FluentFontIcon;
export declare const ProductionCheckmarkRegular: FluentFontIcon;
export declare const ProhibitedFilled: FluentFontIcon;
export declare const ProhibitedRegular: FluentFontIcon;
export declare const ProhibitedMultipleFilled: FluentFontIcon;
export declare const ProhibitedMultipleRegular: FluentFontIcon;
export declare const ProhibitedNoteFilled: FluentFontIcon;
export declare const ProhibitedNoteRegular: FluentFontIcon;
export declare const ProjectionScreenFilled: FluentFontIcon;
export declare const ProjectionScreenRegular: FluentFontIcon;
export declare const ProjectionScreenDismissFilled: FluentFontIcon;
export declare const ProjectionScreenDismissRegular: FluentFontIcon;
export declare const ProjectionScreenTextFilled: FluentFontIcon;
export declare const ProjectionScreenTextRegular: FluentFontIcon;
export declare const ProjectionScreenTextSparkleFilled: FluentFontIcon;
export declare const ProjectionScreenTextSparkleRegular: FluentFontIcon;
export declare const PromptFilled: FluentFontIcon;
export declare const PromptRegular: FluentFontIcon;
export declare const ProtocolHandlerFilled: FluentFontIcon;
export declare const ProtocolHandlerRegular: FluentFontIcon;
export declare const PulseFilled: FluentFontIcon;
export declare const PulseRegular: FluentFontIcon;
export declare const PulseSquareFilled: FluentFontIcon;
export declare const PulseSquareRegular: FluentFontIcon;
export declare const PuzzleCubeFilled: FluentFontIcon;
export declare const PuzzleCubeRegular: FluentFontIcon;
export declare const PuzzleCubePieceFilled: FluentFontIcon;
export declare const PuzzleCubePieceRegular: FluentFontIcon;
export declare const PuzzlePieceFilled: FluentFontIcon;
export declare const PuzzlePieceRegular: FluentFontIcon;
export declare const PuzzlePieceShieldFilled: FluentFontIcon;
export declare const PuzzlePieceShieldRegular: FluentFontIcon;
export declare const QrCodeFilled: FluentFontIcon;
export declare const QrCodeRegular: FluentFontIcon;
export declare const QuestionFilled: FluentFontIcon;
export declare const QuestionRegular: FluentFontIcon;
export declare const QuestionCircleFilled: FluentFontIcon;
export declare const QuestionCircleRegular: FluentFontIcon;
export declare const QuizNewFilled: FluentFontIcon;
export declare const QuizNewRegular: FluentFontIcon;
export declare const RadarFilled: FluentFontIcon;
export declare const RadarRegular: FluentFontIcon;
export declare const RadarCheckmarkFilled: FluentFontIcon;
export declare const RadarCheckmarkRegular: FluentFontIcon;
export declare const RadarRectangleMultipleFilled: FluentFontIcon;
export declare const RadarRectangleMultipleRegular: FluentFontIcon;
export declare const RadioButtonFilled: FluentFontIcon;
export declare const RadioButtonRegular: FluentFontIcon;
export declare const RamFilled: FluentFontIcon;
export declare const RamRegular: FluentFontIcon;
export declare const RatingMatureFilled: FluentFontIcon;
export declare const RatingMatureRegular: FluentFontIcon;
export declare const RatioOneToOneFilled: FluentFontIcon;
export declare const RatioOneToOneRegular: FluentFontIcon;
export declare const ReOrderFilled: FluentFontIcon;
export declare const ReOrderRegular: FluentFontIcon;
export declare const ReOrderDotsHorizontalFilled: FluentFontIcon;
export declare const ReOrderDotsHorizontalRegular: FluentFontIcon;
export declare const ReOrderDotsVerticalFilled: FluentFontIcon;
export declare const ReOrderDotsVerticalRegular: FluentFontIcon;
export declare const ReOrderVerticalFilled: FluentFontIcon;
export declare const ReOrderVerticalRegular: FluentFontIcon;
export declare const ReadAloudFilled: FluentFontIcon;
export declare const ReadAloudRegular: FluentFontIcon;
export declare const ReadingListFilled: FluentFontIcon;
export declare const ReadingListRegular: FluentFontIcon;
export declare const ReadingListAddFilled: FluentFontIcon;
export declare const ReadingListAddRegular: FluentFontIcon;
export declare const ReadingModeMobileFilled: FluentFontIcon;
export declare const ReadingModeMobileRegular: FluentFontIcon;
export declare const RealEstateFilled: FluentFontIcon;
export declare const RealEstateRegular: FluentFontIcon;
export declare const ReceiptFilled: FluentFontIcon;
export declare const ReceiptRegular: FluentFontIcon;
export declare const ReceiptAddFilled: FluentFontIcon;
export declare const ReceiptAddRegular: FluentFontIcon;
export declare const ReceiptBagFilled: FluentFontIcon;
export declare const ReceiptBagRegular: FluentFontIcon;
export declare const ReceiptCubeFilled: FluentFontIcon;
export declare const ReceiptCubeRegular: FluentFontIcon;
export declare const ReceiptMoneyFilled: FluentFontIcon;
export declare const ReceiptMoneyRegular: FluentFontIcon;
export declare const ReceiptPlayFilled: FluentFontIcon;
export declare const ReceiptPlayRegular: FluentFontIcon;
export declare const ReceiptSearchFilled: FluentFontIcon;
export declare const ReceiptSearchRegular: FluentFontIcon;
export declare const ReceiptSparklesFilled: FluentFontIcon;
export declare const ReceiptSparklesRegular: FluentFontIcon;
export declare const RecordFilled: FluentFontIcon;
export declare const RecordRegular: FluentFontIcon;
export declare const RecordStopFilled: FluentFontIcon;
export declare const RecordStopRegular: FluentFontIcon;
export declare const RectangleLandscapeFilled: FluentFontIcon;
export declare const RectangleLandscapeRegular: FluentFontIcon;
export declare const RectangleLandscapeHintCopyFilled: FluentFontIcon;
export declare const RectangleLandscapeHintCopyRegular: FluentFontIcon;
export declare const RectangleLandscapeSparkleFilled: FluentFontIcon;
export declare const RectangleLandscapeSparkleRegular: FluentFontIcon;
export declare const RectangleLandscapeSyncFilled: FluentFontIcon;
export declare const RectangleLandscapeSyncRegular: FluentFontIcon;
export declare const RectangleLandscapeSyncOffFilled: FluentFontIcon;
export declare const RectangleLandscapeSyncOffRegular: FluentFontIcon;
export declare const RectanglePortraitFilled: FluentFontIcon;
export declare const RectanglePortraitRegular: FluentFontIcon;
export declare const RectanglePortraitLocationTargetFilled: FluentFontIcon;
export declare const RectanglePortraitLocationTargetRegular: FluentFontIcon;
export declare const RecycleFilled: FluentFontIcon;
export declare const RecycleRegular: FluentFontIcon;
export declare const RemixAddFilled: FluentFontIcon;
export declare const RemixAddRegular: FluentFontIcon;
export declare const RemoteFilled: FluentFontIcon;
export declare const RemoteRegular: FluentFontIcon;
export declare const RenameFilled: FluentFontIcon;
export declare const RenameRegular: FluentFontIcon;
export declare const RenameAFilled: FluentFontIcon;
export declare const RenameARegular: FluentFontIcon;
export declare const ReorderFilled: FluentFontIcon;
export declare const ReorderRegular: FluentFontIcon;
export declare const ReplayFilled: FluentFontIcon;
export declare const ReplayRegular: FluentFontIcon;
export declare const ResizeFilled: FluentFontIcon;
export declare const ResizeRegular: FluentFontIcon;
export declare const ResizeImageFilled: FluentFontIcon;
export declare const ResizeImageRegular: FluentFontIcon;
export declare const ResizeLargeFilled: FluentFontIcon;
export declare const ResizeLargeRegular: FluentFontIcon;
export declare const ResizeSmallFilled: FluentFontIcon;
export declare const ResizeSmallRegular: FluentFontIcon;
export declare const ResizeTableFilled: FluentFontIcon;
export declare const ResizeTableRegular: FluentFontIcon;
export declare const ResizeVideoFilled: FluentFontIcon;
export declare const ResizeVideoRegular: FluentFontIcon;
export declare const RewardFilled: FluentFontIcon;
export declare const RewardRegular: FluentFontIcon;
export declare const RewindFilled: FluentFontIcon;
export declare const RewindRegular: FluentFontIcon;
export declare const RhombusFilled: FluentFontIcon;
export declare const RhombusRegular: FluentFontIcon;
export declare const RibbonFilled: FluentFontIcon;
export declare const RibbonRegular: FluentFontIcon;
export declare const RibbonAddFilled: FluentFontIcon;
export declare const RibbonAddRegular: FluentFontIcon;
export declare const RibbonOffFilled: FluentFontIcon;
export declare const RibbonOffRegular: FluentFontIcon;
export declare const RibbonStarFilled: FluentFontIcon;
export declare const RibbonStarRegular: FluentFontIcon;
export declare const RoadFilled: FluentFontIcon;
export declare const RoadRegular: FluentFontIcon;
export declare const RoadConeFilled: FluentFontIcon;
export declare const RoadConeRegular: FluentFontIcon;
export declare const RocketFilled: FluentFontIcon;
export declare const RocketRegular: FluentFontIcon;
export declare const RotateLeftFilled: FluentFontIcon;
export declare const RotateLeftRegular: FluentFontIcon;
export declare const RotateRightFilled: FluentFontIcon;
export declare const RotateRightRegular: FluentFontIcon;
export declare const RouterFilled: FluentFontIcon;
export declare const RouterRegular: FluentFontIcon;
export declare const RowChildFilled: FluentFontIcon;
export declare const RowChildRegular: FluentFontIcon;
export declare const RowTripleFilled: FluentFontIcon;
export declare const RowTripleRegular: FluentFontIcon;
export declare const RssFilled: FluentFontIcon;
export declare const RssRegular: FluentFontIcon;
export declare const RulerFilled: FluentFontIcon;
export declare const RulerRegular: FluentFontIcon;
export declare const RunFilled: FluentFontIcon;
export declare const RunRegular: FluentFontIcon;
export declare const SanitizeFilled: FluentFontIcon;
export declare const SanitizeRegular: FluentFontIcon;
export declare const SaveFilled: FluentFontIcon;
export declare const SaveRegular: FluentFontIcon;
export declare const SaveArrowRightFilled: FluentFontIcon;
export declare const SaveArrowRightRegular: FluentFontIcon;
export declare const SaveCopyFilled: FluentFontIcon;
export declare const SaveCopyRegular: FluentFontIcon;
export declare const SaveEditFilled: FluentFontIcon;
export declare const SaveEditRegular: FluentFontIcon;
export declare const SaveImageFilled: FluentFontIcon;
export declare const SaveImageRegular: FluentFontIcon;
export declare const SaveMultipleFilled: FluentFontIcon;
export declare const SaveMultipleRegular: FluentFontIcon;
export declare const SaveSearchFilled: FluentFontIcon;
export declare const SaveSearchRegular: FluentFontIcon;
export declare const SaveSyncFilled: FluentFontIcon;
export declare const SaveSyncRegular: FluentFontIcon;
export declare const SavingsFilled: FluentFontIcon;
export declare const SavingsRegular: FluentFontIcon;
export declare const ScaleFillFilled: FluentFontIcon;
export declare const ScaleFillRegular: FluentFontIcon;
export declare const ScaleFitFilled: FluentFontIcon;
export declare const ScaleFitRegular: FluentFontIcon;
export declare const ScalesFilled: FluentFontIcon;
export declare const ScalesRegular: FluentFontIcon;
export declare const ScanFilled: FluentFontIcon;
export declare const ScanRegular: FluentFontIcon;
export declare const ScanCameraFilled: FluentFontIcon;
export declare const ScanCameraRegular: FluentFontIcon;
export declare const ScanDashFilled: FluentFontIcon;
export declare const ScanDashRegular: FluentFontIcon;
export declare const ScanObjectFilled: FluentFontIcon;
export declare const ScanObjectRegular: FluentFontIcon;
export declare const ScanPersonFilled: FluentFontIcon;
export declare const ScanPersonRegular: FluentFontIcon;
export declare const ScanTableFilled: FluentFontIcon;
export declare const ScanTableRegular: FluentFontIcon;
export declare const ScanTextFilled: FluentFontIcon;
export declare const ScanTextRegular: FluentFontIcon;
export declare const ScanThumbUpFilled: FluentFontIcon;
export declare const ScanThumbUpRegular: FluentFontIcon;
export declare const ScanThumbUpOffFilled: FluentFontIcon;
export declare const ScanThumbUpOffRegular: FluentFontIcon;
export declare const ScanTypeFilled: FluentFontIcon;
export declare const ScanTypeRegular: FluentFontIcon;
export declare const ScanTypeCheckmarkFilled: FluentFontIcon;
export declare const ScanTypeCheckmarkRegular: FluentFontIcon;
export declare const ScanTypeOffFilled: FluentFontIcon;
export declare const ScanTypeOffRegular: FluentFontIcon;
export declare const ScratchpadFilled: FluentFontIcon;
export declare const ScratchpadRegular: FluentFontIcon;
export declare const ScreenCutFilled: FluentFontIcon;
export declare const ScreenCutRegular: FluentFontIcon;
export declare const ScreenPersonFilled: FluentFontIcon;
export declare const ScreenPersonRegular: FluentFontIcon;
export declare const ScreenSearchFilled: FluentFontIcon;
export declare const ScreenSearchRegular: FluentFontIcon;
export declare const ScreenshotFilled: FluentFontIcon;
export declare const ScreenshotRegular: FluentFontIcon;
export declare const ScreenshotRecordFilled: FluentFontIcon;
export declare const ScreenshotRecordRegular: FluentFontIcon;
export declare const ScriptFilled: FluentFontIcon;
export declare const ScriptRegular: FluentFontIcon;
export declare const SearchFilled: FluentFontIcon;
export declare const SearchRegular: FluentFontIcon;
export declare const SearchInfoFilled: FluentFontIcon;
export declare const SearchInfoRegular: FluentFontIcon;
export declare const SearchSettingsFilled: FluentFontIcon;
export declare const SearchSettingsRegular: FluentFontIcon;
export declare const SearchShieldFilled: FluentFontIcon;
export declare const SearchShieldRegular: FluentFontIcon;
export declare const SearchSparkleFilled: FluentFontIcon;
export declare const SearchSparkleRegular: FluentFontIcon;
export declare const SearchSquareFilled: FluentFontIcon;
export declare const SearchSquareRegular: FluentFontIcon;
export declare const SearchVisualFilled: FluentFontIcon;
export declare const SearchVisualRegular: FluentFontIcon;
export declare const SeatFilled: FluentFontIcon;
export declare const SeatRegular: FluentFontIcon;
export declare const SeatAddFilled: FluentFontIcon;
export declare const SeatAddRegular: FluentFontIcon;
export declare const SelectAllOffFilled: FluentFontIcon;
export declare const SelectAllOffRegular: FluentFontIcon;
export declare const SelectAllOnFilled: FluentFontIcon;
export declare const SelectAllOnRegular: FluentFontIcon;
export declare const SelectObjectFilled: FluentFontIcon;
export declare const SelectObjectRegular: FluentFontIcon;
export declare const SelectObjectSkewFilled: FluentFontIcon;
export declare const SelectObjectSkewRegular: FluentFontIcon;
export declare const SelectObjectSkewDismissFilled: FluentFontIcon;
export declare const SelectObjectSkewDismissRegular: FluentFontIcon;
export declare const SelectObjectSkewEditFilled: FluentFontIcon;
export declare const SelectObjectSkewEditRegular: FluentFontIcon;
export declare const SendFilled: FluentFontIcon;
export declare const SendRegular: FluentFontIcon;
export declare const SendBeakerFilled: FluentFontIcon;
export declare const SendBeakerRegular: FluentFontIcon;
export declare const SendClockFilled: FluentFontIcon;
export declare const SendClockRegular: FluentFontIcon;
export declare const SendCopyFilled: FluentFontIcon;
export declare const SendCopyRegular: FluentFontIcon;
export declare const SendPersonFilled: FluentFontIcon;
export declare const SendPersonRegular: FluentFontIcon;
export declare const SerialPortFilled: FluentFontIcon;
export declare const SerialPortRegular: FluentFontIcon;
export declare const ServerFilled: FluentFontIcon;
export declare const ServerRegular: FluentFontIcon;
export declare const ServerLinkFilled: FluentFontIcon;
export declare const ServerLinkRegular: FluentFontIcon;
export declare const ServerMultipleFilled: FluentFontIcon;
export declare const ServerMultipleRegular: FluentFontIcon;
export declare const ServerPlayFilled: FluentFontIcon;
export declare const ServerPlayRegular: FluentFontIcon;
export declare const ServiceBellFilled: FluentFontIcon;
export declare const ServiceBellRegular: FluentFontIcon;
export declare const SettingsFilled: FluentFontIcon;
export declare const SettingsRegular: FluentFontIcon;
export declare const SettingsChatFilled: FluentFontIcon;
export declare const SettingsChatRegular: FluentFontIcon;
export declare const SettingsCogMultipleFilled: FluentFontIcon;
export declare const SettingsCogMultipleRegular: FluentFontIcon;
export declare const ShapeExcludeFilled: FluentFontIcon;
export declare const ShapeExcludeRegular: FluentFontIcon;
export declare const ShapeIntersectFilled: FluentFontIcon;
export declare const ShapeIntersectRegular: FluentFontIcon;
export declare const ShapeOrganicFilled: FluentFontIcon;
export declare const ShapeOrganicRegular: FluentFontIcon;
export declare const ShapeSubtractFilled: FluentFontIcon;
export declare const ShapeSubtractRegular: FluentFontIcon;
export declare const ShapeUnionFilled: FluentFontIcon;
export declare const ShapeUnionRegular: FluentFontIcon;
export declare const ShapesFilled: FluentFontIcon;
export declare const ShapesRegular: FluentFontIcon;
export declare const ShareFilled: FluentFontIcon;
export declare const ShareRegular: FluentFontIcon;
export declare const ShareAndroidFilled: FluentFontIcon;
export declare const ShareAndroidRegular: FluentFontIcon;
export declare const ShareCloseTrayFilled: FluentFontIcon;
export declare const ShareCloseTrayRegular: FluentFontIcon;
export declare const ShareIosFilled: FluentFontIcon;
export declare const ShareIosRegular: FluentFontIcon;
export declare const ShareMultipleFilled: FluentFontIcon;
export declare const ShareMultipleRegular: FluentFontIcon;
export declare const ShareScreenPersonFilled: FluentFontIcon;
export declare const ShareScreenPersonRegular: FluentFontIcon;
export declare const ShareScreenPersonOverlayFilled: FluentFontIcon;
export declare const ShareScreenPersonOverlayRegular: FluentFontIcon;
export declare const ShareScreenPersonOverlayInsideFilled: FluentFontIcon;
export declare const ShareScreenPersonOverlayInsideRegular: FluentFontIcon;
export declare const ShareScreenPersonPFilled: FluentFontIcon;
export declare const ShareScreenPersonPRegular: FluentFontIcon;
export declare const ShareScreenStartFilled: FluentFontIcon;
export declare const ShareScreenStartRegular: FluentFontIcon;
export declare const ShareScreenStopFilled: FluentFontIcon;
export declare const ShareScreenStopRegular: FluentFontIcon;
export declare const ShieldFilled: FluentFontIcon;
export declare const ShieldRegular: FluentFontIcon;
export declare const ShieldAddFilled: FluentFontIcon;
export declare const ShieldAddRegular: FluentFontIcon;
export declare const ShieldArrowRightFilled: FluentFontIcon;
export declare const ShieldArrowRightRegular: FluentFontIcon;
export declare const ShieldBadgeFilled: FluentFontIcon;
export declare const ShieldBadgeRegular: FluentFontIcon;
export declare const ShieldCheckmarkFilled: FluentFontIcon;
export declare const ShieldCheckmarkRegular: FluentFontIcon;
export declare const ShieldDismissFilled: FluentFontIcon;
export declare const ShieldDismissRegular: FluentFontIcon;
export declare const ShieldDismissShieldFilled: FluentFontIcon;
export declare const ShieldDismissShieldRegular: FluentFontIcon;
export declare const ShieldErrorFilled: FluentFontIcon;
export declare const ShieldErrorRegular: FluentFontIcon;
export declare const ShieldGlobeFilled: FluentFontIcon;
export declare const ShieldGlobeRegular: FluentFontIcon;
export declare const ShieldKeyholeFilled: FluentFontIcon;
export declare const ShieldKeyholeRegular: FluentFontIcon;
export declare const ShieldLockFilled: FluentFontIcon;
export declare const ShieldLockRegular: FluentFontIcon;
export declare const ShieldPersonFilled: FluentFontIcon;
export declare const ShieldPersonRegular: FluentFontIcon;
export declare const ShieldPersonAddFilled: FluentFontIcon;
export declare const ShieldPersonAddRegular: FluentFontIcon;
export declare const ShieldProhibitedFilled: FluentFontIcon;
export declare const ShieldProhibitedRegular: FluentFontIcon;
export declare const ShieldQuestionFilled: FluentFontIcon;
export declare const ShieldQuestionRegular: FluentFontIcon;
export declare const ShieldSettingsFilled: FluentFontIcon;
export declare const ShieldSettingsRegular: FluentFontIcon;
export declare const ShieldTaskFilled: FluentFontIcon;
export declare const ShieldTaskRegular: FluentFontIcon;
export declare const ShiftsFilled: FluentFontIcon;
export declare const ShiftsRegular: FluentFontIcon;
export declare const Shifts30MinutesFilled: FluentFontIcon;
export declare const Shifts30MinutesRegular: FluentFontIcon;
export declare const ShiftsActivityFilled: FluentFontIcon;
export declare const ShiftsActivityRegular: FluentFontIcon;
export declare const ShiftsAddFilled: FluentFontIcon;
export declare const ShiftsAddRegular: FluentFontIcon;
export declare const ShiftsAvailabilityFilled: FluentFontIcon;
export declare const ShiftsAvailabilityRegular: FluentFontIcon;
export declare const ShiftsCheckmarkFilled: FluentFontIcon;
export declare const ShiftsCheckmarkRegular: FluentFontIcon;
export declare const ShiftsDayFilled: FluentFontIcon;
export declare const ShiftsDayRegular: FluentFontIcon;
export declare const ShiftsOpenFilled: FluentFontIcon;
export declare const ShiftsOpenRegular: FluentFontIcon;
export declare const ShiftsProhibitedFilled: FluentFontIcon;
export declare const ShiftsProhibitedRegular: FluentFontIcon;
