import * as React from 'react';
import {
  Dropdown,
  Option,
  Field,
  tokens,
  <PERSON><PERSON>,
  Card,
  CardHeader,
  CardPreview,
  Text
} from '@fluentui/react-components';

export interface ITimeTrackingPanel {
  name?: string;
}

interface ITimeTrackingState {
  selectedProject: string;
  selectedTask: string;
  selectedStatus: string;
}

// Define styles as regular objects
const styles = {
  container: {
    display: 'flex',
    flexDirection: 'column' as const,
    gap: tokens.spacingVerticalM,
    padding: tokens.spacingVerticalL,
    maxWidth: '400px',
  },
  dropdownField: {
    display: 'flex',
    flexDirection: 'column' as const,
    gap: tokens.spacingVerticalXS,
  },
  buttonContainer: {
    display: 'flex',
    gap: tokens.spacingHorizontalM,
    marginTop: tokens.spacingVerticalM,
  },
  card: {
    marginTop: tokens.spacingVerticalM,
    padding: tokens.spacingVerticalM,
  }
};

export class TimeTrackingPanel extends React.Component<ITimeTrackingPanel, ITimeTrackingState> {

  constructor(props: ITimeTrackingPanel) {
    super(props);
    this.state = {
      selectedProject: '',
      selectedTask: '',
      selectedStatus: ''
    };
  }

  private handleProjectChange = (_event: any, data: any) => {
    this.setState({ selectedProject: data.optionValue || '' });
  };

  private handleTaskChange = (_event: any, data: any) => {
    this.setState({ selectedTask: data.optionValue || '' });
  };

  private handleStatusChange = (_event: any, data: any) => {
    this.setState({ selectedStatus: data.optionValue || '' });
  };

  private handleStartTracking = () => {
    console.log('Starting time tracking:', {
      project: this.state.selectedProject,
      task: this.state.selectedTask,
      status: this.state.selectedStatus
    });
    // Add your time tracking logic here
  };

  private handleStopTracking = () => {
    console.log('Stopping time tracking');
    // Add your stop tracking logic here
  };

  public render(): React.ReactNode {
    return (
      <div style={styles.container}>
        <Text size={500} weight="semibold">
          Time Tracking - Hello {this.props.name}!
        </Text>

        <Field label="Select Project" style={styles.dropdownField}>
          <Dropdown
            placeholder="Choose a project..."
            value={this.state.selectedProject}
            onOptionSelect={this.handleProjectChange}
          >
            <Option value="project1">CoreFin Development</Option>
            <Option value="project2">Time Tracking Module</Option>
            <Option value="project3">User Interface Updates</Option>
            <Option value="project4">Bug Fixes</Option>
            <Option value="project5">Documentation</Option>
          </Dropdown>
        </Field>

        <Field label="Select Task Type" style={styles.dropdownField}>
          <Dropdown
            placeholder="Choose a task type..."
            value={this.state.selectedTask}
            onOptionSelect={this.handleTaskChange}
          >
            <Option value="development">Development</Option>
            <Option value="testing">Testing</Option>
            <Option value="debugging">Debugging</Option>
            <Option value="documentation">Documentation</Option>
            <Option value="meeting">Meeting</Option>
            <Option value="research">Research</Option>
            <Option value="review">Code Review</Option>
          </Dropdown>
        </Field>

        <Field label="Status" style={styles.dropdownField}>
          <Dropdown
            placeholder="Select status..."
            value={this.state.selectedStatus}
            onOptionSelect={this.handleStatusChange}
          >
            <Option value="not-started">Not Started</Option>
            <Option value="in-progress">In Progress</Option>
            <Option value="blocked">Blocked</Option>
            <Option value="completed">Completed</Option>
            <Option value="on-hold">On Hold</Option>
          </Dropdown>
        </Field>

        <div style={styles.buttonContainer}>
          <Button
            appearance="primary"
            onClick={this.handleStartTracking}
            disabled={!this.state.selectedProject || !this.state.selectedTask}
          >
            Start Tracking
          </Button>
          <Button
            appearance="secondary"
            onClick={this.handleStopTracking}
          >
            Stop Tracking
          </Button>
        </div>

        {(this.state.selectedProject || this.state.selectedTask || this.state.selectedStatus) && (
          <Card style={styles.card}>
            <CardHeader
              header={<Text weight="semibold">Current Selection</Text>}
            />
            <CardPreview>
              <div>
                {this.state.selectedProject && <Text>Project: {this.state.selectedProject}</Text>}
                {this.state.selectedTask && <Text>Task: {this.state.selectedTask}</Text>}
                {this.state.selectedStatus && <Text>Status: {this.state.selectedStatus}</Text>}
              </div>
            </CardPreview>
          </Card>
        )}
      </div>
    );
  }
}
