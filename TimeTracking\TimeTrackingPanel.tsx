import * as React from 'react';
import {
  Dropdown,
  Option,
  Field,
  makeStyles,
  tokens,
  Button,
  Card,
  CardHeader,
  CardPreview,
  Text
} from '@fluentui/react-components';

export interface ITimeTrackingPanel {
  name?: string;
}

const useStyles = makeStyles({
  container: {
    display: 'flex',
    flexDirection: 'column',
    gap: tokens.spacingVerticalM,
    padding: tokens.spacingVerticalL,
    maxWidth: '400px',
    backgroundColor: tokens.colorNeutralBackground1,
    borderRadius: tokens.borderRadiusMedium,
    boxShadow: tokens.shadow4,
  },
  title: {
    marginBottom: tokens.spacingVerticalS,
  },
  dropdownField: {
    marginBottom: tokens.spacingVerticalS,
  },
  buttonContainer: {
    display: 'flex',
    gap: tokens.spacingHorizontalM,
    marginTop: tokens.spacingVerticalM,
  },
  card: {
    marginTop: tokens.spacingVerticalM,
  },
  selectionItem: {
    display: 'block',
    marginBottom: tokens.spacingVerticalXS,
  }
});

export const TimeTrackingPanel: React.FC<ITimeTrackingPanel> = (props) => {
  const styles = useStyles();

  const [selectedProject, setSelectedProject] = React.useState('');
  const [selectedTask, setSelectedTask] = React.useState('');
  const [selectedStatus, setSelectedStatus] = React.useState('');

  const handleProjectChange = React.useCallback((_event: any, data: any) => {
    setSelectedProject(data.optionValue || '');
  }, []);

  const handleTaskChange = React.useCallback((_event: any, data: any) => {
    setSelectedTask(data.optionValue || '');
  }, []);

  const handleStatusChange = React.useCallback((_event: any, data: any) => {
    setSelectedStatus(data.optionValue || '');
  }, []);

  const handleStartTracking = React.useCallback(() => {
    console.log('Starting time tracking:', {
      project: selectedProject,
      task: selectedTask,
      status: selectedStatus
    });
    // Add your time tracking logic here
  }, [selectedProject, selectedTask, selectedStatus]);

  const handleStopTracking = React.useCallback(() => {
    console.log('Stopping time tracking');
    // Add your stop tracking logic here
  }, []);

    <div className={styles.container}>
      <Text size={500} weight="semibold" className={styles.title}>
        Time Tracking - Hello {props.name}!
      </Text>

      <Field label="Select Project" className={styles.dropdownField}>
        <Dropdown
          placeholder="Choose a project..."
          value={selectedProject}
          onOptionSelect={handleProjectChange}
        >
          <Option value="project1">CoreFin Development</Option>
          <Option value="project2">Time Tracking Module</Option>
          <Option value="project3">User Interface Updates</Option>
          <Option value="project4">Bug Fixes</Option>
          <Option value="project5">Documentation</Option>
        </Dropdown>
      </Field>

      <Field label="Select Task Type" className={styles.dropdownField}>
        <Dropdown
          placeholder="Choose a task type..."
          value={selectedTask}
          onOptionSelect={handleTaskChange}
        >
          <Option value="development">Development</Option>
          <Option value="testing">Testing</Option>
          <Option value="debugging">Debugging</Option>
          <Option value="documentation">Documentation</Option>
          <Option value="meeting">Meeting</Option>
          <Option value="research">Research</Option>
          <Option value="review">Code Review</Option>
        </Dropdown>
      </Field>

      <Field label="Status" className={styles.dropdownField}>
        <Dropdown
          placeholder="Select status..."
          value={selectedStatus}
          onOptionSelect={handleStatusChange}
        >
          <Option value="not-started">Not Started</Option>
          <Option value="in-progress">In Progress</Option>
          <Option value="blocked">Blocked</Option>
          <Option value="completed">Completed</Option>
          <Option value="on-hold">On Hold</Option>
        </Dropdown>
      </Field>

      <div className={styles.buttonContainer}>
        <Button
          appearance="primary"
          onClick={handleStartTracking}
          disabled={!selectedProject || !selectedTask}
        >
          Start Tracking
        </Button>
        <Button
          appearance="secondary"
          onClick={handleStopTracking}
        >
          Stop Tracking
        </Button>
      </div>

      {(selectedProject || selectedTask || selectedStatus) && (
        <Card className={styles.card}>
          <CardHeader
            header={<Text weight="semibold">Current Selection</Text>}
          />
          <CardPreview>
            <div>
              {selectedProject && <Text className={styles.selectionItem}>Project: {selectedProject}</Text>}
              {selectedTask && <Text className={styles.selectionItem}>Task: {selectedTask}</Text>}
              {selectedStatus && <Text className={styles.selectionItem}>Status: {selectedStatus}</Text>}
            </div>
          </CardPreview>
        </Card>
      )}
    </div>
  );
};
