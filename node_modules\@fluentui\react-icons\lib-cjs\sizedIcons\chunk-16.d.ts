import type { FluentIcon } from "../utils/createFluentIcon";
export declare const TabDesktopArrowClockwise16Regular: FluentIcon;
export declare const TabDesktopArrowClockwise20Filled: FluentIcon;
export declare const TabDesktopArrowClockwise20Regular: FluentIcon;
export declare const TabDesktopArrowClockwise24Filled: FluentIcon;
export declare const TabDesktopArrowClockwise24Regular: FluentIcon;
export declare const TabDesktopArrowLeft20Filled: FluentIcon;
export declare const TabDesktopArrowLeft20Regular: FluentIcon;
export declare const TabDesktopBottom20Filled: FluentIcon;
export declare const TabDesktopBottom20Regular: FluentIcon;
export declare const TabDesktopBottom24Filled: FluentIcon;
export declare const TabDesktopBottom24Regular: FluentIcon;
export declare const TabDesktopClock20Filled: FluentIcon;
export declare const TabDesktopClock20Regular: FluentIcon;
export declare const TabDesktopCopy20Filled: FluentIcon;
export declare const TabDesktopCopy20Regular: FluentIcon;
export declare const TabDesktopImage16Filled: FluentIcon;
export declare const TabDesktopImage16Regular: FluentIcon;
export declare const TabDesktopImage20Filled: FluentIcon;
export declare const TabDesktopImage20Regular: FluentIcon;
export declare const TabDesktopImage24Filled: FluentIcon;
export declare const TabDesktopImage24Regular: FluentIcon;
export declare const TabDesktopLink16Filled: FluentIcon;
export declare const TabDesktopLink16Regular: FluentIcon;
export declare const TabDesktopLink20Filled: FluentIcon;
export declare const TabDesktopLink20Regular: FluentIcon;
export declare const TabDesktopLink24Filled: FluentIcon;
export declare const TabDesktopLink24Regular: FluentIcon;
export declare const TabDesktopLink28Filled: FluentIcon;
export declare const TabDesktopLink28Regular: FluentIcon;
export declare const TabDesktopMultiple16Filled: FluentIcon;
export declare const TabDesktopMultiple16Regular: FluentIcon;
export declare const TabDesktopMultiple20Filled: FluentIcon;
export declare const TabDesktopMultiple20Regular: FluentIcon;
export declare const TabDesktopMultiple24Filled: FluentIcon;
export declare const TabDesktopMultiple24Regular: FluentIcon;
export declare const TabDesktopMultipleAdd16Filled: FluentIcon;
export declare const TabDesktopMultipleAdd16Regular: FluentIcon;
export declare const TabDesktopMultipleAdd20Filled: FluentIcon;
export declare const TabDesktopMultipleAdd20Regular: FluentIcon;
export declare const TabDesktopMultipleBottom20Filled: FluentIcon;
export declare const TabDesktopMultipleBottom20Regular: FluentIcon;
export declare const TabDesktopMultipleBottom24Filled: FluentIcon;
export declare const TabDesktopMultipleBottom24Regular: FluentIcon;
export declare const TabDesktopMultipleSparkle16Filled: FluentIcon;
export declare const TabDesktopMultipleSparkle16Regular: FluentIcon;
export declare const TabDesktopMultipleSparkle20Filled: FluentIcon;
export declare const TabDesktopMultipleSparkle20Regular: FluentIcon;
export declare const TabDesktopMultipleSparkle24Filled: FluentIcon;
export declare const TabDesktopMultipleSparkle24Regular: FluentIcon;
export declare const TabDesktopNewPage20Filled: FluentIcon;
export declare const TabDesktopNewPage20Regular: FluentIcon;
export declare const TabDesktopSearch16Filled: FluentIcon;
export declare const TabDesktopSearch16Regular: FluentIcon;
export declare const TabDesktopSearch20Filled: FluentIcon;
export declare const TabDesktopSearch20Regular: FluentIcon;
export declare const TabDesktopSearch24Filled: FluentIcon;
export declare const TabDesktopSearch24Regular: FluentIcon;
export declare const TabGroup16Filled: FluentIcon;
export declare const TabGroup16Regular: FluentIcon;
export declare const TabGroup20Filled: FluentIcon;
export declare const TabGroup20Regular: FluentIcon;
export declare const TabGroup24Filled: FluentIcon;
export declare const TabGroup24Regular: FluentIcon;
export declare const TabInPrivate16Filled: FluentIcon;
export declare const TabInPrivate16Regular: FluentIcon;
export declare const TabInPrivate20Filled: FluentIcon;
export declare const TabInPrivate20Regular: FluentIcon;
export declare const TabInPrivate24Filled: FluentIcon;
export declare const TabInPrivate24Regular: FluentIcon;
export declare const TabInPrivate28Filled: FluentIcon;
export declare const TabInPrivate28Regular: FluentIcon;
export declare const TabInprivateAccount20Filled: FluentIcon;
export declare const TabInprivateAccount20Regular: FluentIcon;
export declare const TabInprivateAccount24Filled: FluentIcon;
export declare const TabInprivateAccount24Regular: FluentIcon;
export declare const TabProhibited20Filled: FluentIcon;
export declare const TabProhibited20Regular: FluentIcon;
export declare const TabProhibited24Filled: FluentIcon;
export declare const TabProhibited24Regular: FluentIcon;
export declare const TabShieldDismiss20Filled: FluentIcon;
export declare const TabShieldDismiss20Regular: FluentIcon;
export declare const TabShieldDismiss24Filled: FluentIcon;
export declare const TabShieldDismiss24Regular: FluentIcon;
export declare const Table16Color: FluentIcon;
export declare const Table16Filled: FluentIcon;
export declare const Table16Regular: FluentIcon;
export declare const Table20Color: FluentIcon;
export declare const Table20Filled: FluentIcon;
export declare const Table20Regular: FluentIcon;
export declare const Table24Color: FluentIcon;
export declare const Table24Filled: FluentIcon;
export declare const Table24Regular: FluentIcon;
export declare const Table28Color: FluentIcon;
export declare const Table28Filled: FluentIcon;
export declare const Table28Regular: FluentIcon;
export declare const Table32Color: FluentIcon;
export declare const Table32Filled: FluentIcon;
export declare const Table32Light: FluentIcon;
export declare const Table32Regular: FluentIcon;
export declare const Table48Color: FluentIcon;
export declare const Table48Filled: FluentIcon;
export declare const Table48Regular: FluentIcon;
export declare const TableAdd16Filled: FluentIcon;
export declare const TableAdd16Regular: FluentIcon;
export declare const TableAdd20Filled: FluentIcon;
export declare const TableAdd20Regular: FluentIcon;
export declare const TableAdd24Filled: FluentIcon;
export declare const TableAdd24Regular: FluentIcon;
export declare const TableAdd28Filled: FluentIcon;
export declare const TableAdd28Regular: FluentIcon;
export declare const TableAltText20Filled: FluentIcon;
export declare const TableAltText20Regular: FluentIcon;
export declare const TableAltText24Filled: FluentIcon;
export declare const TableAltText24Regular: FluentIcon;
export declare const TableAltText32Filled: FluentIcon;
export declare const TableAltText32Light: FluentIcon;
export declare const TableAltText32Regular: FluentIcon;
export declare const TableArrowRepeatAll20Filled: FluentIcon;
export declare const TableArrowRepeatAll20Regular: FluentIcon;
export declare const TableArrowRepeatAll24Filled: FluentIcon;
export declare const TableArrowRepeatAll24Regular: FluentIcon;
export declare const TableArrowRepeatAll28Filled: FluentIcon;
export declare const TableArrowRepeatAll28Regular: FluentIcon;
export declare const TableArrowUp20Filled: FluentIcon;
export declare const TableArrowUp20Regular: FluentIcon;
export declare const TableArrowUp24Filled: FluentIcon;
export declare const TableArrowUp24Regular: FluentIcon;
export declare const TableBottomRow16Filled: FluentIcon;
export declare const TableBottomRow16Regular: FluentIcon;
export declare const TableBottomRow20Filled: FluentIcon;
export declare const TableBottomRow20Regular: FluentIcon;
export declare const TableBottomRow24Filled: FluentIcon;
export declare const TableBottomRow24Regular: FluentIcon;
export declare const TableBottomRow28Filled: FluentIcon;
export declare const TableBottomRow28Regular: FluentIcon;
export declare const TableBottomRow32Filled: FluentIcon;
export declare const TableBottomRow32Regular: FluentIcon;
export declare const TableBottomRow48Filled: FluentIcon;
export declare const TableBottomRow48Regular: FluentIcon;
export declare const TableCalculator16Filled: FluentIcon;
export declare const TableCalculator16Regular: FluentIcon;
export declare const TableCalculator20Filled: FluentIcon;
export declare const TableCalculator20Regular: FluentIcon;
export declare const TableCellAdd16Filled: FluentIcon;
export declare const TableCellAdd16Regular: FluentIcon;
export declare const TableCellAdd20Filled: FluentIcon;
export declare const TableCellAdd20Regular: FluentIcon;
export declare const TableCellAdd24Filled: FluentIcon;
export declare const TableCellAdd24Regular: FluentIcon;
export declare const TableCellCenter16Filled: FluentIcon;
export declare const TableCellCenter16Regular: FluentIcon;
export declare const TableCellCenter20Filled: FluentIcon;
export declare const TableCellCenter20Regular: FluentIcon;
export declare const TableCellCenter24Filled: FluentIcon;
export declare const TableCellCenter24Regular: FluentIcon;
export declare const TableCellCenter28Filled: FluentIcon;
export declare const TableCellCenter28Regular: FluentIcon;
export declare const TableCellCenterArrowRepeatAll20Filled: FluentIcon;
export declare const TableCellCenterArrowRepeatAll20Regular: FluentIcon;
export declare const TableCellCenterArrowRepeatAll24Filled: FluentIcon;
export declare const TableCellCenterArrowRepeatAll24Regular: FluentIcon;
export declare const TableCellCenterArrowRepeatAll28Filled: FluentIcon;
export declare const TableCellCenterArrowRepeatAll28Regular: FluentIcon;
export declare const TableCellCenterEdit16Filled: FluentIcon;
export declare const TableCellCenterEdit16Regular: FluentIcon;
export declare const TableCellCenterEdit20Filled: FluentIcon;
export declare const TableCellCenterEdit20Regular: FluentIcon;
export declare const TableCellCenterEdit24Filled: FluentIcon;
export declare const TableCellCenterEdit24Regular: FluentIcon;
export declare const TableCellCenterEdit28Filled: FluentIcon;
export declare const TableCellCenterEdit28Regular: FluentIcon;
export declare const TableCellCenterLink20Filled: FluentIcon;
export declare const TableCellCenterLink20Regular: FluentIcon;
export declare const TableCellCenterLink24Filled: FluentIcon;
export declare const TableCellCenterLink24Regular: FluentIcon;
export declare const TableCellCenterLink28Filled: FluentIcon;
export declare const TableCellCenterLink28Regular: FluentIcon;
export declare const TableCellCenterSearch20Filled: FluentIcon;
export declare const TableCellCenterSearch20Regular: FluentIcon;
export declare const TableCellCenterSearch24Filled: FluentIcon;
export declare const TableCellCenterSearch24Regular: FluentIcon;
export declare const TableCellCenterSearch28Filled: FluentIcon;
export declare const TableCellCenterSearch28Regular: FluentIcon;
export declare const TableCellEdit16Filled: FluentIcon;
export declare const TableCellEdit16Regular: FluentIcon;
export declare const TableCellEdit20Filled: FluentIcon;
export declare const TableCellEdit20Regular: FluentIcon;
export declare const TableCellEdit24Filled: FluentIcon;
export declare const TableCellEdit24Regular: FluentIcon;
export declare const TableCellEdit28Filled: FluentIcon;
export declare const TableCellEdit28Regular: FluentIcon;
export declare const TableCellsMerge16Filled: FluentIcon;
export declare const TableCellsMerge16Regular: FluentIcon;
export declare const TableCellsMerge20Filled: FluentIcon;
export declare const TableCellsMerge20Regular: FluentIcon;
export declare const TableCellsMerge24Filled: FluentIcon;
export declare const TableCellsMerge24Regular: FluentIcon;
export declare const TableCellsMerge28Filled: FluentIcon;
export declare const TableCellsMerge28Regular: FluentIcon;
export declare const TableCellsMerge32Light: FluentIcon;
export declare const TableCellsSplit16Filled: FluentIcon;
export declare const TableCellsSplit16Regular: FluentIcon;
export declare const TableCellsSplit20Filled: FluentIcon;
export declare const TableCellsSplit20Regular: FluentIcon;
export declare const TableCellsSplit24Filled: FluentIcon;
export declare const TableCellsSplit24Regular: FluentIcon;
export declare const TableCellsSplit28Filled: FluentIcon;
export declare const TableCellsSplit28Regular: FluentIcon;
export declare const TableCellsSplit32Light: FluentIcon;
export declare const TableChecker20Filled: FluentIcon;
export declare const TableChecker20Regular: FluentIcon;
export declare const TableColumnTopBottom16Filled: FluentIcon;
export declare const TableColumnTopBottom16Regular: FluentIcon;
export declare const TableColumnTopBottom20Filled: FluentIcon;
export declare const TableColumnTopBottom20Regular: FluentIcon;
export declare const TableColumnTopBottom24Filled: FluentIcon;
export declare const TableColumnTopBottom24Regular: FluentIcon;
export declare const TableColumnTopBottom28Filled: FluentIcon;
export declare const TableColumnTopBottom28Regular: FluentIcon;
export declare const TableColumnTopBottomArrowRepeatAll20Filled: FluentIcon;
export declare const TableColumnTopBottomArrowRepeatAll20Regular: FluentIcon;
export declare const TableColumnTopBottomArrowRepeatAll24Filled: FluentIcon;
export declare const TableColumnTopBottomArrowRepeatAll24Regular: FluentIcon;
export declare const TableColumnTopBottomArrowRepeatAll28Filled: FluentIcon;
export declare const TableColumnTopBottomArrowRepeatAll28Regular: FluentIcon;
export declare const TableColumnTopBottomEdit16Filled: FluentIcon;
export declare const TableColumnTopBottomEdit16Regular: FluentIcon;
export declare const TableColumnTopBottomEdit20Filled: FluentIcon;
export declare const TableColumnTopBottomEdit20Regular: FluentIcon;
export declare const TableColumnTopBottomEdit24Filled: FluentIcon;
export declare const TableColumnTopBottomEdit24Regular: FluentIcon;
export declare const TableColumnTopBottomEdit28Filled: FluentIcon;
export declare const TableColumnTopBottomEdit28Regular: FluentIcon;
export declare const TableColumnTopBottomLink20Filled: FluentIcon;
export declare const TableColumnTopBottomLink20Regular: FluentIcon;
export declare const TableColumnTopBottomLink24Filled: FluentIcon;
export declare const TableColumnTopBottomLink24Regular: FluentIcon;
export declare const TableColumnTopBottomLink28Filled: FluentIcon;
export declare const TableColumnTopBottomLink28Regular: FluentIcon;
export declare const TableColumnTopBottomSearch20Filled: FluentIcon;
export declare const TableColumnTopBottomSearch20Regular: FluentIcon;
export declare const TableColumnTopBottomSearch24Filled: FluentIcon;
export declare const TableColumnTopBottomSearch24Regular: FluentIcon;
export declare const TableColumnTopBottomSearch28Filled: FluentIcon;
export declare const TableColumnTopBottomSearch28Regular: FluentIcon;
export declare const TableCopy20Filled: FluentIcon;
export declare const TableCopy20Regular: FluentIcon;
export declare const TableCursor16Filled: FluentIcon;
export declare const TableCursor16Regular: FluentIcon;
export declare const TableCursor20Filled: FluentIcon;
export declare const TableCursor20Regular: FluentIcon;
export declare const TableCursor24Filled: FluentIcon;
export declare const TableCursor24Regular: FluentIcon;
export declare const TableDefault32Filled: FluentIcon;
export declare const TableDefault32Regular: FluentIcon;
export declare const TableDeleteColumn16Filled: FluentIcon;
export declare const TableDeleteColumn16Regular: FluentIcon;
export declare const TableDeleteColumn20Filled: FluentIcon;
export declare const TableDeleteColumn20Regular: FluentIcon;
export declare const TableDeleteColumn24Filled: FluentIcon;
export declare const TableDeleteColumn24Regular: FluentIcon;
export declare const TableDeleteColumn28Filled: FluentIcon;
export declare const TableDeleteColumn28Regular: FluentIcon;
export declare const TableDeleteRow16Filled: FluentIcon;
export declare const TableDeleteRow16Regular: FluentIcon;
export declare const TableDeleteRow20Filled: FluentIcon;
export declare const TableDeleteRow20Regular: FluentIcon;
export declare const TableDeleteRow24Filled: FluentIcon;
export declare const TableDeleteRow24Regular: FluentIcon;
export declare const TableDeleteRow28Filled: FluentIcon;
export declare const TableDeleteRow28Regular: FluentIcon;
export declare const TableDismiss16Filled: FluentIcon;
export declare const TableDismiss16Regular: FluentIcon;
export declare const TableDismiss20Filled: FluentIcon;
export declare const TableDismiss20Regular: FluentIcon;
export declare const TableDismiss24Filled: FluentIcon;
export declare const TableDismiss24Regular: FluentIcon;
export declare const TableDismiss28Filled: FluentIcon;
export declare const TableDismiss28Regular: FluentIcon;
export declare const TableDismiss32Light: FluentIcon;
export declare const TableEdit16Filled: FluentIcon;
export declare const TableEdit16Regular: FluentIcon;
export declare const TableEdit20Filled: FluentIcon;
export declare const TableEdit20Regular: FluentIcon;
export declare const TableEdit24Filled: FluentIcon;
export declare const TableEdit24Regular: FluentIcon;
export declare const TableEdit28Filled: FluentIcon;
export declare const TableEdit28Regular: FluentIcon;
export declare const TableFreezeColumn16Filled: FluentIcon;
export declare const TableFreezeColumn16Regular: FluentIcon;
export declare const TableFreezeColumn20Filled: FluentIcon;
export declare const TableFreezeColumn20Regular: FluentIcon;
export declare const TableFreezeColumn24Filled: FluentIcon;
export declare const TableFreezeColumn24Regular: FluentIcon;
export declare const TableFreezeColumn28Filled: FluentIcon;
export declare const TableFreezeColumn28Regular: FluentIcon;
export declare const TableFreezeColumnAndRow16Filled: FluentIcon;
export declare const TableFreezeColumnAndRow16Regular: FluentIcon;
export declare const TableFreezeColumnAndRow20Filled: FluentIcon;
export declare const TableFreezeColumnAndRow20Regular: FluentIcon;
export declare const TableFreezeColumnAndRow24Filled: FluentIcon;
export declare const TableFreezeColumnAndRow24Regular: FluentIcon;
export declare const TableFreezeColumnAndRow28Filled: FluentIcon;
export declare const TableFreezeColumnAndRow28Regular: FluentIcon;
export declare const TableFreezeColumnAndRowDismiss20Filled: FluentIcon;
export declare const TableFreezeColumnAndRowDismiss20Regular: FluentIcon;
export declare const TableFreezeColumnAndRowDismiss24Filled: FluentIcon;
export declare const TableFreezeColumnAndRowDismiss24Regular: FluentIcon;
export declare const TableFreezeColumnDismiss20Filled: FluentIcon;
export declare const TableFreezeColumnDismiss20Regular: FluentIcon;
export declare const TableFreezeColumnDismiss24Filled: FluentIcon;
export declare const TableFreezeColumnDismiss24Regular: FluentIcon;
export declare const TableFreezeRow16Filled: FluentIcon;
export declare const TableFreezeRow16Regular: FluentIcon;
export declare const TableFreezeRow20Filled: FluentIcon;
export declare const TableFreezeRow20Regular: FluentIcon;
export declare const TableFreezeRow24Filled: FluentIcon;
export declare const TableFreezeRow24Regular: FluentIcon;
export declare const TableFreezeRow28Filled: FluentIcon;
export declare const TableFreezeRow28Regular: FluentIcon;
export declare const TableFreezeRowDismiss20Filled: FluentIcon;
export declare const TableFreezeRowDismiss20Regular: FluentIcon;
export declare const TableFreezeRowDismiss24Filled: FluentIcon;
export declare const TableFreezeRowDismiss24Regular: FluentIcon;
export declare const TableImage20Filled: FluentIcon;
export declare const TableImage20Regular: FluentIcon;
export declare const TableInsertColumn16Filled: FluentIcon;
export declare const TableInsertColumn16Regular: FluentIcon;
export declare const TableInsertColumn20Filled: FluentIcon;
export declare const TableInsertColumn20Regular: FluentIcon;
export declare const TableInsertColumn24Filled: FluentIcon;
export declare const TableInsertColumn24Regular: FluentIcon;
export declare const TableInsertColumn28Filled: FluentIcon;
export declare const TableInsertColumn28Regular: FluentIcon;
export declare const TableInsertRow16Filled: FluentIcon;
export declare const TableInsertRow16Regular: FluentIcon;
export declare const TableInsertRow20Filled: FluentIcon;
export declare const TableInsertRow20Regular: FluentIcon;
export declare const TableInsertRow24Filled: FluentIcon;
export declare const TableInsertRow24Regular: FluentIcon;
export declare const TableInsertRow28Filled: FluentIcon;
export declare const TableInsertRow28Regular: FluentIcon;
export declare const TableLightning16Filled: FluentIcon;
export declare const TableLightning16Regular: FluentIcon;
export declare const TableLightning20Filled: FluentIcon;
export declare const TableLightning20Regular: FluentIcon;
export declare const TableLightning24Filled: FluentIcon;
export declare const TableLightning24Regular: FluentIcon;
export declare const TableLightning28Filled: FluentIcon;
export declare const TableLightning28Regular: FluentIcon;
export declare const TableLink16Filled: FluentIcon;
export declare const TableLink16Regular: FluentIcon;
export declare const TableLink20Filled: FluentIcon;
export declare const TableLink20Regular: FluentIcon;
export declare const TableLink24Filled: FluentIcon;
export declare const TableLink24Regular: FluentIcon;
export declare const TableLink28Filled: FluentIcon;
export declare const TableLink28Regular: FluentIcon;
export declare const TableLock16Filled: FluentIcon;
export declare const TableLock16Regular: FluentIcon;
export declare const TableLock20Filled: FluentIcon;
export declare const TableLock20Regular: FluentIcon;
export declare const TableLock24Filled: FluentIcon;
export declare const TableLock24Regular: FluentIcon;
export declare const TableLock28Filled: FluentIcon;
export declare const TableLock28Regular: FluentIcon;
export declare const TableLock32Filled: FluentIcon;
export declare const TableLock32Regular: FluentIcon;
export declare const TableLock48Filled: FluentIcon;
export declare const TableLock48Regular: FluentIcon;
export declare const TableMoveAbove16Filled: FluentIcon;
export declare const TableMoveAbove16Regular: FluentIcon;
export declare const TableMoveAbove20Filled: FluentIcon;
export declare const TableMoveAbove20Regular: FluentIcon;
export declare const TableMoveAbove24Filled: FluentIcon;
export declare const TableMoveAbove24Regular: FluentIcon;
export declare const TableMoveAbove28Filled: FluentIcon;
export declare const TableMoveAbove28Regular: FluentIcon;
export declare const TableMoveAbove32Light: FluentIcon;
export declare const TableMoveBelow16Filled: FluentIcon;
export declare const TableMoveBelow16Regular: FluentIcon;
export declare const TableMoveBelow20Filled: FluentIcon;
export declare const TableMoveBelow20Regular: FluentIcon;
export declare const TableMoveBelow24Filled: FluentIcon;
export declare const TableMoveBelow24Regular: FluentIcon;
export declare const TableMoveBelow28Filled: FluentIcon;
export declare const TableMoveBelow28Regular: FluentIcon;
export declare const TableMoveBelow32Light: FluentIcon;
export declare const TableMoveLeft16Filled: FluentIcon;
export declare const TableMoveLeft16Regular: FluentIcon;
export declare const TableMoveLeft20Filled: FluentIcon;
export declare const TableMoveLeft20Regular: FluentIcon;
export declare const TableMoveLeft24Filled: FluentIcon;
export declare const TableMoveLeft24Regular: FluentIcon;
export declare const TableMoveLeft28Filled: FluentIcon;
export declare const TableMoveLeft28Regular: FluentIcon;
export declare const TableMoveLeft32Light: FluentIcon;
export declare const TableMoveRight16Filled: FluentIcon;
export declare const TableMoveRight16Regular: FluentIcon;
export declare const TableMoveRight20Filled: FluentIcon;
export declare const TableMoveRight20Regular: FluentIcon;
export declare const TableMoveRight24Filled: FluentIcon;
export declare const TableMoveRight24Regular: FluentIcon;
export declare const TableMoveRight28Filled: FluentIcon;
export declare const TableMoveRight28Regular: FluentIcon;
export declare const TableMoveRight32Light: FluentIcon;
export declare const TableMultiple20Filled: FluentIcon;
export declare const TableMultiple20Regular: FluentIcon;
export declare const TableOffset20Filled: FluentIcon;
export declare const TableOffset20Regular: FluentIcon;
export declare const TableOffset24Filled: FluentIcon;
export declare const TableOffset24Regular: FluentIcon;
export declare const TableOffsetAdd20Filled: FluentIcon;
export declare const TableOffsetAdd20Regular: FluentIcon;
export declare const TableOffsetAdd24Filled: FluentIcon;
export declare const TableOffsetAdd24Regular: FluentIcon;
export declare const TableOffsetLessThanOrEqualTo20Filled: FluentIcon;
export declare const TableOffsetLessThanOrEqualTo20Regular: FluentIcon;
export declare const TableOffsetLessThanOrEqualTo24Filled: FluentIcon;
export declare const TableOffsetLessThanOrEqualTo24Regular: FluentIcon;
export declare const TableOffsetSettings20Filled: FluentIcon;
export declare const TableOffsetSettings20Regular: FluentIcon;
export declare const TableOffsetSettings24Filled: FluentIcon;
export declare const TableOffsetSettings24Regular: FluentIcon;
export declare const TableResizeColumn16Filled: FluentIcon;
export declare const TableResizeColumn16Regular: FluentIcon;
export declare const TableResizeColumn20Filled: FluentIcon;
export declare const TableResizeColumn20Regular: FluentIcon;
export declare const TableResizeColumn24Filled: FluentIcon;
export declare const TableResizeColumn24Regular: FluentIcon;
export declare const TableResizeColumn28Filled: FluentIcon;
export declare const TableResizeColumn28Regular: FluentIcon;
export declare const TableResizeRow16Filled: FluentIcon;
export declare const TableResizeRow16Regular: FluentIcon;
export declare const TableResizeRow20Filled: FluentIcon;
export declare const TableResizeRow20Regular: FluentIcon;
export declare const TableResizeRow24Filled: FluentIcon;
export declare const TableResizeRow24Regular: FluentIcon;
export declare const TableResizeRow28Filled: FluentIcon;
export declare const TableResizeRow28Regular: FluentIcon;
export declare const TableSearch20Filled: FluentIcon;
export declare const TableSearch20Regular: FluentIcon;
export declare const TableSearch24Filled: FluentIcon;
export declare const TableSearch24Regular: FluentIcon;
export declare const TableSearch28Filled: FluentIcon;
export declare const TableSearch28Regular: FluentIcon;
export declare const TableSettings16Filled: FluentIcon;
export declare const TableSettings16Regular: FluentIcon;
export declare const TableSettings20Filled: FluentIcon;
export declare const TableSettings20Regular: FluentIcon;
export declare const TableSettings24Filled: FluentIcon;
export declare const TableSettings24Regular: FluentIcon;
export declare const TableSettings28Filled: FluentIcon;
export declare const TableSettings28Regular: FluentIcon;
export declare const TableSettings32Light: FluentIcon;
export declare const TableSimple16Filled: FluentIcon;
export declare const TableSimple16Regular: FluentIcon;
export declare const TableSimple20Filled: FluentIcon;
export declare const TableSimple20Regular: FluentIcon;
export declare const TableSimple24Filled: FluentIcon;
export declare const TableSimple24Regular: FluentIcon;
export declare const TableSimple28Filled: FluentIcon;
export declare const TableSimple28Regular: FluentIcon;
export declare const TableSimple32Filled: FluentIcon;
export declare const TableSimple32Light: FluentIcon;
export declare const TableSimple32Regular: FluentIcon;
export declare const TableSimple48Filled: FluentIcon;
export declare const TableSimple48Regular: FluentIcon;
export declare const TableSimpleCheckmark16Filled: FluentIcon;
export declare const TableSimpleCheckmark16Regular: FluentIcon;
export declare const TableSimpleCheckmark20Filled: FluentIcon;
export declare const TableSimpleCheckmark20Regular: FluentIcon;
export declare const TableSimpleCheckmark24Filled: FluentIcon;
export declare const TableSimpleCheckmark24Regular: FluentIcon;
export declare const TableSimpleCheckmark28Filled: FluentIcon;
export declare const TableSimpleCheckmark28Regular: FluentIcon;
export declare const TableSimpleCheckmark32Filled: FluentIcon;
export declare const TableSimpleCheckmark32Regular: FluentIcon;
export declare const TableSimpleCheckmark48Filled: FluentIcon;
export declare const TableSimpleCheckmark48Regular: FluentIcon;
export declare const TableSimpleExclude16Filled: FluentIcon;
export declare const TableSimpleExclude16Regular: FluentIcon;
export declare const TableSimpleExclude20Filled: FluentIcon;
export declare const TableSimpleExclude20Regular: FluentIcon;
export declare const TableSimpleExclude24Filled: FluentIcon;
export declare const TableSimpleExclude24Regular: FluentIcon;
export declare const TableSimpleExclude28Filled: FluentIcon;
export declare const TableSimpleExclude28Regular: FluentIcon;
export declare const TableSimpleExclude32Filled: FluentIcon;
export declare const TableSimpleExclude32Regular: FluentIcon;
export declare const TableSimpleExclude48Filled: FluentIcon;
export declare const TableSimpleExclude48Regular: FluentIcon;
export declare const TableSimpleInclude16Filled: FluentIcon;
export declare const TableSimpleInclude16Regular: FluentIcon;
export declare const TableSimpleInclude20Filled: FluentIcon;
export declare const TableSimpleInclude20Regular: FluentIcon;
export declare const TableSimpleInclude24Filled: FluentIcon;
export declare const TableSimpleInclude24Regular: FluentIcon;
export declare const TableSimpleInclude28Filled: FluentIcon;
export declare const TableSimpleInclude28Regular: FluentIcon;
export declare const TableSimpleInclude32Filled: FluentIcon;
export declare const TableSimpleInclude32Regular: FluentIcon;
export declare const TableSimpleInclude48Filled: FluentIcon;
export declare const TableSimpleInclude48Regular: FluentIcon;
export declare const TableSimpleMultiple20Filled: FluentIcon;
export declare const TableSimpleMultiple20Regular: FluentIcon;
export declare const TableSimpleMultiple24Filled: FluentIcon;
export declare const TableSimpleMultiple24Regular: FluentIcon;
export declare const TableSparkle20Filled: FluentIcon;
export declare const TableSparkle20Regular: FluentIcon;
export declare const TableSparkle24Filled: FluentIcon;
export declare const TableSparkle24Regular: FluentIcon;
export declare const TableSplit20Filled: FluentIcon;
export declare const TableSplit20Regular: FluentIcon;
export declare const TableStackAbove16Filled: FluentIcon;
export declare const TableStackAbove16Regular: FluentIcon;
export declare const TableStackAbove20Filled: FluentIcon;
export declare const TableStackAbove20Regular: FluentIcon;
export declare const TableStackAbove24Filled: FluentIcon;
export declare const TableStackAbove24Regular: FluentIcon;
export declare const TableStackAbove28Filled: FluentIcon;
export declare const TableStackAbove28Regular: FluentIcon;
export declare const TableStackBelow16Filled: FluentIcon;
export declare const TableStackBelow16Regular: FluentIcon;
export declare const TableStackBelow20Filled: FluentIcon;
export declare const TableStackBelow20Regular: FluentIcon;
export declare const TableStackBelow24Filled: FluentIcon;
export declare const TableStackBelow24Regular: FluentIcon;
export declare const TableStackBelow28Filled: FluentIcon;
export declare const TableStackBelow28Regular: FluentIcon;
export declare const TableStackLeft16Filled: FluentIcon;
export declare const TableStackLeft16Regular: FluentIcon;
export declare const TableStackLeft20Filled: FluentIcon;
export declare const TableStackLeft20Regular: FluentIcon;
export declare const TableStackLeft24Filled: FluentIcon;
export declare const TableStackLeft24Regular: FluentIcon;
export declare const TableStackLeft28Filled: FluentIcon;
export declare const TableStackLeft28Regular: FluentIcon;
export declare const TableStackRight16Filled: FluentIcon;
export declare const TableStackRight16Regular: FluentIcon;
export declare const TableStackRight20Filled: FluentIcon;
export declare const TableStackRight20Regular: FluentIcon;
export declare const TableStackRight24Filled: FluentIcon;
export declare const TableStackRight24Regular: FluentIcon;
export declare const TableStackRight28Filled: FluentIcon;
export declare const TableStackRight28Regular: FluentIcon;
export declare const TableSwitch16Filled: FluentIcon;
export declare const TableSwitch16Regular: FluentIcon;
export declare const TableSwitch20Filled: FluentIcon;
export declare const TableSwitch20Regular: FluentIcon;
export declare const TableSwitch24Filled: FluentIcon;
export declare const TableSwitch24Regular: FluentIcon;
export declare const TableSwitch28Filled: FluentIcon;
export declare const TableSwitch28Regular: FluentIcon;
export declare const Tablet12Filled: FluentIcon;
export declare const Tablet12Regular: FluentIcon;
export declare const Tablet16Filled: FluentIcon;
export declare const Tablet16Regular: FluentIcon;
export declare const Tablet20Filled: FluentIcon;
export declare const Tablet20Regular: FluentIcon;
export declare const Tablet24Filled: FluentIcon;
export declare const Tablet24Regular: FluentIcon;
export declare const Tablet32Filled: FluentIcon;
export declare const Tablet32Regular: FluentIcon;
export declare const Tablet48Filled: FluentIcon;
export declare const Tablet48Regular: FluentIcon;
export declare const TabletLaptop20Filled: FluentIcon;
export declare const TabletLaptop20Regular: FluentIcon;
export declare const TabletLaptop24Filled: FluentIcon;
export declare const TabletLaptop24Regular: FluentIcon;
export declare const TabletSpeaker20Filled: FluentIcon;
export declare const TabletSpeaker20Regular: FluentIcon;
export declare const TabletSpeaker24Filled: FluentIcon;
export declare const TabletSpeaker24Regular: FluentIcon;
export declare const Tabs16Filled: FluentIcon;
export declare const Tabs16Regular: FluentIcon;
export declare const Tabs20Filled: FluentIcon;
export declare const Tabs20Regular: FluentIcon;
export declare const Tabs24Filled: FluentIcon;
export declare const Tabs24Regular: FluentIcon;
export declare const Tag16Filled: FluentIcon;
export declare const Tag16Regular: FluentIcon;
export declare const Tag20Filled: FluentIcon;
export declare const Tag20Regular: FluentIcon;
export declare const Tag24Filled: FluentIcon;
export declare const Tag24Regular: FluentIcon;
export declare const Tag28Filled: FluentIcon;
export declare const Tag28Regular: FluentIcon;
export declare const Tag32Filled: FluentIcon;
export declare const Tag32Light: FluentIcon;
export declare const Tag32Regular: FluentIcon;
export declare const Tag48Filled: FluentIcon;
export declare const Tag48Regular: FluentIcon;
export declare const TagAdd16Filled: FluentIcon;
export declare const TagAdd16Regular: FluentIcon;
export declare const TagAdd20Filled: FluentIcon;
export declare const TagAdd20Regular: FluentIcon;
export declare const TagAdd24Filled: FluentIcon;
export declare const TagAdd24Regular: FluentIcon;
export declare const TagAdd28Filled: FluentIcon;
export declare const TagAdd28Regular: FluentIcon;
export declare const TagAdd32Filled: FluentIcon;
export declare const TagAdd32Regular: FluentIcon;
export declare const TagAdd48Filled: FluentIcon;
export declare const TagAdd48Regular: FluentIcon;
export declare const TagCircle20Filled: FluentIcon;
export declare const TagCircle20Regular: FluentIcon;
export declare const TagDismiss16Filled: FluentIcon;
export declare const TagDismiss16Regular: FluentIcon;
export declare const TagDismiss20Filled: FluentIcon;
export declare const TagDismiss20Regular: FluentIcon;
export declare const TagDismiss24Filled: FluentIcon;
export declare const TagDismiss24Regular: FluentIcon;
export declare const TagEdit16Filled: FluentIcon;
export declare const TagEdit16Regular: FluentIcon;
export declare const TagEdit20Filled: FluentIcon;
export declare const TagEdit20Regular: FluentIcon;
export declare const TagEdit24Filled: FluentIcon;
export declare const TagEdit24Regular: FluentIcon;
export declare const TagEdit28Filled: FluentIcon;
export declare const TagEdit28Regular: FluentIcon;
export declare const TagEdit32Filled: FluentIcon;
export declare const TagEdit32Regular: FluentIcon;
export declare const TagEdit48Filled: FluentIcon;
export declare const TagEdit48Regular: FluentIcon;
export declare const TagError16Filled: FluentIcon;
export declare const TagError16Regular: FluentIcon;
export declare const TagError20Filled: FluentIcon;
export declare const TagError20Regular: FluentIcon;
export declare const TagError24Filled: FluentIcon;
export declare const TagError24Regular: FluentIcon;
export declare const TagLock16Filled: FluentIcon;
export declare const TagLock16Regular: FluentIcon;
export declare const TagLock20Filled: FluentIcon;
export declare const TagLock20Regular: FluentIcon;
export declare const TagLock24Filled: FluentIcon;
export declare const TagLock24Regular: FluentIcon;
export declare const TagLock32Filled: FluentIcon;
export declare const TagLock32Regular: FluentIcon;
export declare const TagLockAccent16Filled: FluentIcon;
export declare const TagLockAccent20Filled: FluentIcon;
export declare const TagLockAccent24Filled: FluentIcon;
export declare const TagLockAccent32Filled: FluentIcon;
export declare const TagMultiple16Filled: FluentIcon;
export declare const TagMultiple16Regular: FluentIcon;
export declare const TagMultiple20Filled: FluentIcon;
export declare const TagMultiple20Regular: FluentIcon;
export declare const TagMultiple24Filled: FluentIcon;
export declare const TagMultiple24Regular: FluentIcon;
export declare const TagOff16Filled: FluentIcon;
export declare const TagOff16Regular: FluentIcon;
export declare const TagOff20Filled: FluentIcon;
export declare const TagOff20Regular: FluentIcon;
export declare const TagOff24Filled: FluentIcon;
export declare const TagOff24Regular: FluentIcon;
export declare const TagPercent16Filled: FluentIcon;
export declare const TagPercent16Regular: FluentIcon;
export declare const TagPercent20Filled: FluentIcon;
export declare const TagPercent20Regular: FluentIcon;
export declare const TagPercent24Filled: FluentIcon;
export declare const TagPercent24Regular: FluentIcon;
export declare const TagPercent28Filled: FluentIcon;
export declare const TagPercent28Regular: FluentIcon;
export declare const TagPercent32Filled: FluentIcon;
export declare const TagPercent32Regular: FluentIcon;
export declare const TagPercent48Filled: FluentIcon;
export declare const TagPercent48Regular: FluentIcon;
export declare const TagQuestionMark16Filled: FluentIcon;
export declare const TagQuestionMark16Regular: FluentIcon;
export declare const TagQuestionMark20Filled: FluentIcon;
export declare const TagQuestionMark20Regular: FluentIcon;
export declare const TagQuestionMark24Filled: FluentIcon;
export declare const TagQuestionMark24Regular: FluentIcon;
export declare const TagQuestionMark32Filled: FluentIcon;
export declare const TagQuestionMark32Regular: FluentIcon;
export declare const TagReset20Filled: FluentIcon;
export declare const TagReset20Regular: FluentIcon;
export declare const TagReset24Filled: FluentIcon;
export declare const TagReset24Regular: FluentIcon;
export declare const TagSearch20Filled: FluentIcon;
export declare const TagSearch20Regular: FluentIcon;
export declare const TagSearch24Filled: FluentIcon;
export declare const TagSearch24Regular: FluentIcon;
export declare const TapDouble20Filled: FluentIcon;
export declare const TapDouble20Regular: FluentIcon;
export declare const TapDouble24Filled: FluentIcon;
export declare const TapDouble24Regular: FluentIcon;
export declare const TapDouble32Filled: FluentIcon;
export declare const TapDouble32Regular: FluentIcon;
export declare const TapDouble48Filled: FluentIcon;
export declare const TapDouble48Regular: FluentIcon;
export declare const TapSingle20Filled: FluentIcon;
export declare const TapSingle20Regular: FluentIcon;
export declare const TapSingle24Filled: FluentIcon;
export declare const TapSingle24Regular: FluentIcon;
export declare const TapSingle32Filled: FluentIcon;
export declare const TapSingle32Regular: FluentIcon;
export declare const TapSingle48Filled: FluentIcon;
export declare const TapSingle48Regular: FluentIcon;
export declare const Target16Filled: FluentIcon;
export declare const Target16Regular: FluentIcon;
export declare const Target20Filled: FluentIcon;
export declare const Target20Regular: FluentIcon;
export declare const Target24Filled: FluentIcon;
export declare const Target24Regular: FluentIcon;
export declare const Target32Filled: FluentIcon;
export declare const Target32Regular: FluentIcon;
export declare const TargetAdd20Filled: FluentIcon;
export declare const TargetAdd20Regular: FluentIcon;
export declare const TargetAdd24Filled: FluentIcon;
export declare const TargetAdd24Regular: FluentIcon;
export declare const TargetArrow16Filled: FluentIcon;
export declare const TargetArrow16Regular: FluentIcon;
export declare const TargetArrow20Filled: FluentIcon;
export declare const TargetArrow20Regular: FluentIcon;
export declare const TargetArrow24Filled: FluentIcon;
export declare const TargetArrow24Regular: FluentIcon;
export declare const TargetDismiss20Filled: FluentIcon;
export declare const TargetDismiss20Regular: FluentIcon;
export declare const TargetDismiss24Filled: FluentIcon;
export declare const TargetDismiss24Regular: FluentIcon;
export declare const TargetEdit16Filled: FluentIcon;
export declare const TargetEdit16Regular: FluentIcon;
export declare const TargetEdit20Filled: FluentIcon;
export declare const TargetEdit20Regular: FluentIcon;
export declare const TargetEdit24Filled: FluentIcon;
export declare const TargetEdit24Regular: FluentIcon;
export declare const TargetSparkle16Filled: FluentIcon;
export declare const TargetSparkle16Regular: FluentIcon;
export declare const TargetSparkle20Filled: FluentIcon;
export declare const TargetSparkle20Regular: FluentIcon;
export declare const TargetSparkle24Filled: FluentIcon;
export declare const TargetSparkle24Regular: FluentIcon;
export declare const TaskListAdd20Filled: FluentIcon;
export declare const TaskListAdd20Regular: FluentIcon;
export declare const TaskListAdd24Filled: FluentIcon;
export declare const TaskListAdd24Regular: FluentIcon;
export declare const TaskListLtr20Filled: FluentIcon;
export declare const TaskListLtr20Regular: FluentIcon;
export declare const TaskListLtr24Filled: FluentIcon;
export declare const TaskListLtr24Regular: FluentIcon;
export declare const TaskListRtl20Filled: FluentIcon;
export declare const TaskListRtl20Regular: FluentIcon;
export declare const TaskListRtl24Filled: FluentIcon;
export declare const TaskListRtl24Regular: FluentIcon;
export declare const TaskListSquareAdd20Filled: FluentIcon;
export declare const TaskListSquareAdd20Regular: FluentIcon;
export declare const TaskListSquareAdd24Filled: FluentIcon;
export declare const TaskListSquareAdd24Regular: FluentIcon;
export declare const TaskListSquareDatabase20Filled: FluentIcon;
export declare const TaskListSquareDatabase20Regular: FluentIcon;
export declare const TaskListSquareDatabase24Filled: FluentIcon;
export declare const TaskListSquareDatabase24Regular: FluentIcon;
export declare const TaskListSquareLtr16Filled: FluentIcon;
export declare const TaskListSquareLtr16Regular: FluentIcon;
export declare const TaskListSquareLtr20Filled: FluentIcon;
export declare const TaskListSquareLtr20Regular: FluentIcon;
export declare const TaskListSquareLtr24Filled: FluentIcon;
export declare const TaskListSquareLtr24Regular: FluentIcon;
export declare const TaskListSquareLtr48Filled: FluentIcon;
export declare const TaskListSquareLtr48Regular: FluentIcon;
export declare const TaskListSquarePerson20Filled: FluentIcon;
export declare const TaskListSquarePerson20Regular: FluentIcon;
export declare const TaskListSquarePerson24Filled: FluentIcon;
export declare const TaskListSquarePerson24Regular: FluentIcon;
export declare const TaskListSquarePerson48Filled: FluentIcon;
export declare const TaskListSquarePerson48Regular: FluentIcon;
export declare const TaskListSquareRtl16Filled: FluentIcon;
export declare const TaskListSquareRtl16Regular: FluentIcon;
export declare const TaskListSquareRtl20Filled: FluentIcon;
export declare const TaskListSquareRtl20Regular: FluentIcon;
export declare const TaskListSquareRtl24Filled: FluentIcon;
export declare const TaskListSquareRtl24Regular: FluentIcon;
export declare const TaskListSquareSettings20Filled: FluentIcon;
export declare const TaskListSquareSettings20Regular: FluentIcon;
export declare const TaskListSquareSparkle16Filled: FluentIcon;
export declare const TaskListSquareSparkle16Regular: FluentIcon;
export declare const TaskListSquareSparkle20Filled: FluentIcon;
export declare const TaskListSquareSparkle20Regular: FluentIcon;
export declare const TaskListSquareSparkle24Filled: FluentIcon;
export declare const TaskListSquareSparkle24Regular: FluentIcon;
export declare const TasksApp20Filled: FluentIcon;
export declare const TasksApp20Regular: FluentIcon;
export declare const TasksApp24Filled: FluentIcon;
export declare const TasksApp24Regular: FluentIcon;
export declare const TasksApp28Filled: FluentIcon;
export declare const TasksApp28Regular: FluentIcon;
export declare const Teaching16Filled: FluentIcon;
export declare const Teaching16Regular: FluentIcon;
export declare const Teaching20Filled: FluentIcon;
export declare const Teaching20Regular: FluentIcon;
export declare const Teaching24Filled: FluentIcon;
export declare const Teaching24Regular: FluentIcon;
export declare const Teaching28Filled: FluentIcon;
export declare const Teaching28Regular: FluentIcon;
export declare const Teaching32Filled: FluentIcon;
export declare const Teaching32Regular: FluentIcon;
export declare const Teaching48Filled: FluentIcon;
export declare const Teaching48Regular: FluentIcon;
export declare const TeardropBottomRight16Filled: FluentIcon;
export declare const TeardropBottomRight16Regular: FluentIcon;
export declare const TeardropBottomRight20Filled: FluentIcon;
export declare const TeardropBottomRight20Regular: FluentIcon;
export declare const TeardropBottomRight24Filled: FluentIcon;
export declare const TeardropBottomRight24Regular: FluentIcon;
export declare const TeardropBottomRight28Filled: FluentIcon;
export declare const TeardropBottomRight28Regular: FluentIcon;
export declare const TeardropBottomRight32Filled: FluentIcon;
export declare const TeardropBottomRight32Regular: FluentIcon;
export declare const TeardropBottomRight48Filled: FluentIcon;
export declare const TeardropBottomRight48Regular: FluentIcon;
export declare const Teddy20Filled: FluentIcon;
export declare const Teddy20Regular: FluentIcon;
export declare const Teddy24Filled: FluentIcon;
export declare const Teddy24Regular: FluentIcon;
export declare const Temperature16Filled: FluentIcon;
export declare const Temperature16Regular: FluentIcon;
export declare const Temperature20Filled: FluentIcon;
export declare const Temperature20Regular: FluentIcon;
export declare const Temperature24Filled: FluentIcon;
export declare const Temperature24Regular: FluentIcon;
export declare const Temperature32Filled: FluentIcon;
export declare const Temperature32Regular: FluentIcon;
export declare const Temperature48Filled: FluentIcon;
export declare const Temperature48Regular: FluentIcon;
export declare const TemperatureDegreeCelsius16Filled: FluentIcon;
export declare const TemperatureDegreeCelsius16Regular: FluentIcon;
export declare const TemperatureDegreeCelsius20Filled: FluentIcon;
export declare const TemperatureDegreeCelsius20Regular: FluentIcon;
export declare const TemperatureDegreeCelsius24Filled: FluentIcon;
export declare const TemperatureDegreeCelsius24Regular: FluentIcon;
export declare const TemperatureDegreeCelsius28Filled: FluentIcon;
export declare const TemperatureDegreeCelsius28Regular: FluentIcon;
export declare const TemperatureDegreeCelsius32Filled: FluentIcon;
export declare const TemperatureDegreeCelsius32Regular: FluentIcon;
export declare const TemperatureDegreeCelsius48Filled: FluentIcon;
export declare const TemperatureDegreeCelsius48Regular: FluentIcon;
export declare const TemperatureDegreeFahrenheit16Filled: FluentIcon;
export declare const TemperatureDegreeFahrenheit16Regular: FluentIcon;
export declare const TemperatureDegreeFahrenheit20Filled: FluentIcon;
export declare const TemperatureDegreeFahrenheit20Regular: FluentIcon;
export declare const TemperatureDegreeFahrenheit24Filled: FluentIcon;
export declare const TemperatureDegreeFahrenheit24Regular: FluentIcon;
export declare const TemperatureDegreeFahrenheit28Filled: FluentIcon;
export declare const TemperatureDegreeFahrenheit28Regular: FluentIcon;
export declare const TemperatureDegreeFahrenheit32Filled: FluentIcon;
export declare const TemperatureDegreeFahrenheit32Regular: FluentIcon;
export declare const TemperatureDegreeFahrenheit48Filled: FluentIcon;
export declare const TemperatureDegreeFahrenheit48Regular: FluentIcon;
export declare const Tent12Filled: FluentIcon;
export declare const Tent12Regular: FluentIcon;
export declare const Tent16Filled: FluentIcon;
export declare const Tent16Regular: FluentIcon;
export declare const Tent20Filled: FluentIcon;
export declare const Tent20Regular: FluentIcon;
export declare const Tent24Filled: FluentIcon;
export declare const Tent24Regular: FluentIcon;
export declare const Tent28Filled: FluentIcon;
export declare const Tent28Regular: FluentIcon;
export declare const Tent48Filled: FluentIcon;
export declare const Tent48Regular: FluentIcon;
export declare const TetrisApp16Filled: FluentIcon;
export declare const TetrisApp16Regular: FluentIcon;
export declare const TetrisApp20Filled: FluentIcon;
export declare const TetrisApp20Regular: FluentIcon;
export declare const TetrisApp24Filled: FluentIcon;
export declare const TetrisApp24Regular: FluentIcon;
export declare const TetrisApp28Filled: FluentIcon;
export declare const TetrisApp28Regular: FluentIcon;
export declare const TetrisApp32Filled: FluentIcon;
export declare const TetrisApp32Regular: FluentIcon;
export declare const TetrisApp48Filled: FluentIcon;
export declare const TetrisApp48Regular: FluentIcon;
export declare const Text12Filled: FluentIcon;
export declare const Text12Regular: FluentIcon;
export declare const Text16Filled: FluentIcon;
export declare const Text16Regular: FluentIcon;
export declare const Text32Filled: FluentIcon;
export declare const Text32Light: FluentIcon;
export declare const Text32Regular: FluentIcon;
export declare const TextAbcUnderlineDouble32Filled: FluentIcon;
export declare const TextAbcUnderlineDouble32Regular: FluentIcon;
export declare const TextAdd20Filled: FluentIcon;
export declare const TextAdd20Regular: FluentIcon;
export declare const TextAddSpaceAfter20Filled: FluentIcon;
export declare const TextAddSpaceAfter20Regular: FluentIcon;
export declare const TextAddSpaceAfter24Filled: FluentIcon;
export declare const TextAddSpaceAfter24Regular: FluentIcon;
export declare const TextAddSpaceBefore20Filled: FluentIcon;
export declare const TextAddSpaceBefore20Regular: FluentIcon;
export declare const TextAddSpaceBefore24Filled: FluentIcon;
export declare const TextAddSpaceBefore24Regular: FluentIcon;
export declare const TextAddT20Filled: FluentIcon;
export declare const TextAddT20Regular: FluentIcon;
export declare const TextAddT24Filled: FluentIcon;
export declare const TextAddT24Regular: FluentIcon;
export declare const TextAlignCenter16Filled: FluentIcon;
export declare const TextAlignCenter16Regular: FluentIcon;
export declare const TextAlignCenter20Filled: FluentIcon;
export declare const TextAlignCenter20Regular: FluentIcon;
export declare const TextAlignCenter24Filled: FluentIcon;
export declare const TextAlignCenter24Regular: FluentIcon;
export declare const TextAlignCenterRotate27016Filled: FluentIcon;
export declare const TextAlignCenterRotate27016Regular: FluentIcon;
export declare const TextAlignCenterRotate27020Filled: FluentIcon;
export declare const TextAlignCenterRotate27020Regular: FluentIcon;
export declare const TextAlignCenterRotate27024Filled: FluentIcon;
export declare const TextAlignCenterRotate27024Regular: FluentIcon;
export declare const TextAlignCenterRotate9016Filled: FluentIcon;
export declare const TextAlignCenterRotate9016Regular: FluentIcon;
export declare const TextAlignCenterRotate9020Filled: FluentIcon;
export declare const TextAlignCenterRotate9020Regular: FluentIcon;
export declare const TextAlignCenterRotate9024Filled: FluentIcon;
export declare const TextAlignCenterRotate9024Regular: FluentIcon;
export declare const TextAlignDistributed20Filled: FluentIcon;
export declare const TextAlignDistributed20Regular: FluentIcon;
export declare const TextAlignDistributed24Filled: FluentIcon;
export declare const TextAlignDistributed24Regular: FluentIcon;
export declare const TextAlignDistributedEvenly20Filled: FluentIcon;
export declare const TextAlignDistributedEvenly20Regular: FluentIcon;
export declare const TextAlignDistributedEvenly24Filled: FluentIcon;
export declare const TextAlignDistributedEvenly24Regular: FluentIcon;
export declare const TextAlignDistributedVertical20Filled: FluentIcon;
export declare const TextAlignDistributedVertical20Regular: FluentIcon;
export declare const TextAlignDistributedVertical24Filled: FluentIcon;
export declare const TextAlignDistributedVertical24Regular: FluentIcon;
export declare const TextAlignJustify20Filled: FluentIcon;
export declare const TextAlignJustify20Regular: FluentIcon;
export declare const TextAlignJustify24Filled: FluentIcon;
export declare const TextAlignJustify24Regular: FluentIcon;
export declare const TextAlignJustifyLow20Filled: FluentIcon;
export declare const TextAlignJustifyLow20Regular: FluentIcon;
export declare const TextAlignJustifyLow24Filled: FluentIcon;
export declare const TextAlignJustifyLow24Regular: FluentIcon;
export declare const TextAlignJustifyLow9020Filled: FluentIcon;
export declare const TextAlignJustifyLow9020Regular: FluentIcon;
export declare const TextAlignJustifyLow9024Filled: FluentIcon;
export declare const TextAlignJustifyLow9024Regular: FluentIcon;
export declare const TextAlignJustifyLowRotate27020Filled: FluentIcon;
export declare const TextAlignJustifyLowRotate27020Regular: FluentIcon;
export declare const TextAlignJustifyLowRotate27024Filled: FluentIcon;
export declare const TextAlignJustifyLowRotate27024Regular: FluentIcon;
export declare const TextAlignJustifyLowRotate9020Filled: FluentIcon;
export declare const TextAlignJustifyLowRotate9020Regular: FluentIcon;
export declare const TextAlignJustifyLowRotate9024Filled: FluentIcon;
export declare const TextAlignJustifyLowRotate9024Regular: FluentIcon;
export declare const TextAlignJustifyRotate27020Filled: FluentIcon;
export declare const TextAlignJustifyRotate27020Regular: FluentIcon;
export declare const TextAlignJustifyRotate27024Filled: FluentIcon;
export declare const TextAlignJustifyRotate27024Regular: FluentIcon;
export declare const TextAlignJustifyRotate9020Filled: FluentIcon;
export declare const TextAlignJustifyRotate9020Regular: FluentIcon;
export declare const TextAlignJustifyRotate9024Filled: FluentIcon;
export declare const TextAlignJustifyRotate9024Regular: FluentIcon;
export declare const TextAlignLeft16Filled: FluentIcon;
export declare const TextAlignLeft16Regular: FluentIcon;
export declare const TextAlignLeft20Filled: FluentIcon;
export declare const TextAlignLeft20Regular: FluentIcon;
export declare const TextAlignLeft24Filled: FluentIcon;
export declare const TextAlignLeft24Regular: FluentIcon;
export declare const TextAlignLeftRotate27016Filled: FluentIcon;
export declare const TextAlignLeftRotate27016Regular: FluentIcon;
export declare const TextAlignLeftRotate27020Filled: FluentIcon;
export declare const TextAlignLeftRotate27020Regular: FluentIcon;
export declare const TextAlignLeftRotate27024Filled: FluentIcon;
export declare const TextAlignLeftRotate27024Regular: FluentIcon;
export declare const TextAlignLeftRotate9016Filled: FluentIcon;
export declare const TextAlignLeftRotate9016Regular: FluentIcon;
export declare const TextAlignLeftRotate9020Filled: FluentIcon;
export declare const TextAlignLeftRotate9020Regular: FluentIcon;
export declare const TextAlignLeftRotate9024Filled: FluentIcon;
export declare const TextAlignLeftRotate9024Regular: FluentIcon;
export declare const TextAlignRight16Filled: FluentIcon;
export declare const TextAlignRight16Regular: FluentIcon;
export declare const TextAlignRight20Filled: FluentIcon;
export declare const TextAlignRight20Regular: FluentIcon;
export declare const TextAlignRight24Filled: FluentIcon;
export declare const TextAlignRight24Regular: FluentIcon;
export declare const TextAlignRightRotate27016Filled: FluentIcon;
export declare const TextAlignRightRotate27016Regular: FluentIcon;
export declare const TextAlignRightRotate27020Filled: FluentIcon;
export declare const TextAlignRightRotate27020Regular: FluentIcon;
export declare const TextAlignRightRotate27024Filled: FluentIcon;
export declare const TextAlignRightRotate27024Regular: FluentIcon;
export declare const TextAlignRightRotate9016Filled: FluentIcon;
export declare const TextAlignRightRotate9016Regular: FluentIcon;
export declare const TextAlignRightRotate9020Filled: FluentIcon;
export declare const TextAlignRightRotate9020Regular: FluentIcon;
export declare const TextAlignRightRotate9024Filled: FluentIcon;
export declare const TextAlignRightRotate9024Regular: FluentIcon;
export declare const TextArrowDownRightColumn16Filled: FluentIcon;
export declare const TextArrowDownRightColumn16Regular: FluentIcon;
export declare const TextArrowDownRightColumn20Filled: FluentIcon;
export declare const TextArrowDownRightColumn20Regular: FluentIcon;
export declare const TextArrowDownRightColumn24Filled: FluentIcon;
export declare const TextArrowDownRightColumn24Regular: FluentIcon;
export declare const TextArrowDownRightColumn28Filled: FluentIcon;
export declare const TextArrowDownRightColumn28Regular: FluentIcon;
export declare const TextArrowDownRightColumn32Filled: FluentIcon;
export declare const TextArrowDownRightColumn32Regular: FluentIcon;
