"use client";
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.Checkbox2Filled = exports.Checkbox1Regular = exports.Checkbox1Filled = exports.CheckRegular = exports.CheckFilled = exports.ChatWarningRegular = exports.ChatWarningFilled = exports.ChatVideoRegular = exports.ChatVideoFilled = exports.ChatSparkleRegular = exports.ChatSparkleFilled = exports.ChatSettingsRegular = exports.ChatSettingsFilled = exports.ChatOffRegular = exports.ChatOffFilled = exports.ChatMultipleMinusRegular = exports.ChatMultipleMinusFilled = exports.ChatMultipleHeartRegular = exports.ChatMultipleHeartFilled = exports.ChatMultipleCheckmarkRegular = exports.ChatMultipleCheckmarkFilled = exports.ChatMultipleRegular = exports.ChatMultipleFilled = exports.ChatMailRegular = exports.ChatMailFilled = exports.ChatLockRegular = exports.ChatLockFilled = exports.ChatHistoryRegular = exports.ChatHistoryFilled = exports.ChatHelpRegular = exports.ChatHelpFilled = exports.ChatEmptyRegular = exports.ChatEmptyFilled = exports.ChatDismissRegular = exports.ChatDismissFilled = exports.ChatCursorRegular = exports.ChatCursorFilled = exports.ChatBubblesQuestionRegular = exports.ChatBubblesQuestionFilled = exports.ChatArrowDoubleBackRegular = exports.ChatArrowDoubleBackFilled = exports.ChatArrowBackDownRegular = exports.ChatArrowBackDownFilled = exports.ChatArrowBackRegular = exports.ChatArrowBackFilled = exports.ChatAddRegular = exports.ChatAddFilled = exports.ChatRegular = exports.ChatFilled = exports.ChartPersonRegular = void 0;
exports.ChevronDoubleRightFilled = exports.ChevronDoubleLeftRegular = exports.ChevronDoubleLeftFilled = exports.ChevronDoubleDownRegular = exports.ChevronDoubleDownFilled = exports.ChevronCircleUpRegular = exports.ChevronCircleUpFilled = exports.ChevronCircleRightRegular = exports.ChevronCircleRightFilled = exports.ChevronCircleLeftRegular = exports.ChevronCircleLeftFilled = exports.ChevronCircleDownRegular = exports.ChevronCircleDownFilled = exports.ChessRegular = exports.ChessFilled = exports.CheckmarkUnderlineCircleRegular = exports.CheckmarkUnderlineCircleFilled = exports.CheckmarkStarburstRegular = exports.CheckmarkStarburstFilled = exports.CheckmarkSquareRegular = exports.CheckmarkSquareFilled = exports.CheckmarkNoteRegular = exports.CheckmarkNoteFilled = exports.CheckmarkLockRegular = exports.CheckmarkLockFilled = exports.CheckmarkCircleWarningRegular = exports.CheckmarkCircleWarningFilled = exports.CheckmarkCircleSquareRegular = exports.CheckmarkCircleSquareFilled = exports.CheckmarkCircleHintRegular = exports.CheckmarkCircleHintFilled = exports.CheckmarkCircleRegular = exports.CheckmarkCircleFilled = exports.CheckmarkRegular = exports.CheckmarkFilled = exports.CheckboxWarningRegular = exports.CheckboxWarningFilled = exports.CheckboxUncheckedRegular = exports.CheckboxUncheckedFilled = exports.CheckboxPersonRegular = exports.CheckboxPersonFilled = exports.CheckboxIndeterminateRegular = exports.CheckboxIndeterminateFilled = exports.CheckboxCheckedSyncRegular = exports.CheckboxCheckedSyncFilled = exports.CheckboxCheckedRegular = exports.CheckboxCheckedFilled = exports.CheckboxArrowRightRegular = exports.CheckboxArrowRightFilled = exports.Checkbox2Regular = void 0;
exports.CircleSparkleFilled = exports.CircleSmallRegular = exports.CircleSmallFilled = exports.CircleShadowRegular = exports.CircleShadowFilled = exports.CircleOffRegular = exports.CircleOffFilled = exports.CircleMultipleSubtractCheckmarkRegular = exports.CircleMultipleSubtractCheckmarkFilled = exports.CircleMultipleHintCheckmarkRegular = exports.CircleMultipleHintCheckmarkFilled = exports.CircleMultipleConcentricRegular = exports.CircleMultipleConcentricFilled = exports.CircleLineRegular = exports.CircleLineFilled = exports.CircleImageRegular = exports.CircleImageFilled = exports.CircleHintHalfVerticalRegular = exports.CircleHintHalfVerticalFilled = exports.CircleHintDismissRegular = exports.CircleHintDismissFilled = exports.CircleHintCursorRegular = exports.CircleHintCursorFilled = exports.CircleHintRegular = exports.CircleHintFilled = exports.CircleHighlightRegular = exports.CircleHighlightFilled = exports.CircleHalfFillRegular = exports.CircleHalfFillFilled = exports.CircleEraserRegular = exports.CircleEraserFilled = exports.CircleEditRegular = exports.CircleEditFilled = exports.CircleRegular = exports.CircleFilled = exports.ChevronUpDownRegular = exports.ChevronUpDownFilled = exports.ChevronUpRegular = exports.ChevronUpFilled = exports.ChevronRightRegular = exports.ChevronRightFilled = exports.ChevronLeftRegular = exports.ChevronLeftFilled = exports.ChevronDownUpRegular = exports.ChevronDownUpFilled = exports.ChevronDownRegular = exports.ChevronDownFilled = exports.ChevronDoubleUpRegular = exports.ChevronDoubleUpFilled = exports.ChevronDoubleRightRegular = void 0;
exports.ClipboardMoreFilled = exports.ClipboardMonthRegular = exports.ClipboardMonthFilled = exports.ClipboardMathFormulaRegular = exports.ClipboardMathFormulaFilled = exports.ClipboardLinkRegular = exports.ClipboardLinkFilled = exports.ClipboardLetterRegular = exports.ClipboardLetterFilled = exports.ClipboardImageRegular = exports.ClipboardImageFilled = exports.ClipboardHeartRegular = exports.ClipboardHeartFilled = exports.ClipboardErrorRegular = exports.ClipboardErrorFilled = exports.ClipboardEditRegular = exports.ClipboardEditFilled = exports.ClipboardDayRegular = exports.ClipboardDayFilled = exports.ClipboardDataBarRegular = exports.ClipboardDataBarFilled = exports.ClipboardCodeRegular = exports.ClipboardCodeFilled = exports.ClipboardClockRegular = exports.ClipboardClockFilled = exports.ClipboardCheckmarkRegular = exports.ClipboardCheckmarkFilled = exports.ClipboardBulletListRtlRegular = exports.ClipboardBulletListRtlFilled = exports.ClipboardBulletListLtrRegular = exports.ClipboardBulletListLtrFilled = exports.ClipboardBulletListRegular = exports.ClipboardBulletListFilled = exports.ClipboardBrushRegular = exports.ClipboardBrushFilled = exports.ClipboardArrowRightRegular = exports.ClipboardArrowRightFilled = exports.Clipboard3DayRegular = exports.Clipboard3DayFilled = exports.ClipboardRegular = exports.ClipboardFilled = exports.ClearFormattingRegular = exports.ClearFormattingFilled = exports.ClassificationRegular = exports.ClassificationFilled = exports.ClassRegular = exports.ClassFilled = exports.CityRegular = exports.CityFilled = exports.CircleSparkleRegular = void 0;
exports.ClothesHangerFilled = exports.ClosedCaptionOffRegular = exports.ClosedCaptionOffFilled = exports.ClosedCaptionRegular = exports.ClosedCaptionFilled = exports.ClockToolboxRegular = exports.ClockToolboxFilled = exports.ClockSparkleRegular = exports.ClockSparkleFilled = exports.ClockPauseRegular = exports.ClockPauseFilled = exports.ClockLockRegular = exports.ClockLockFilled = exports.ClockDismissRegular = exports.ClockDismissFilled = exports.ClockBillRegular = exports.ClockBillFilled = exports.ClockArrowDownloadRegular = exports.ClockArrowDownloadFilled = exports.ClockAlarmRegular = exports.ClockAlarmFilled = exports.ClockRegular = exports.ClockFilled = exports.ClipboardTextRtlRegular = exports.ClipboardTextRtlFilled = exports.ClipboardTextLtrRegular = exports.ClipboardTextLtrFilled = exports.ClipboardTextEditRegular = exports.ClipboardTextEditFilled = exports.ClipboardTaskListRtlRegular = exports.ClipboardTaskListRtlFilled = exports.ClipboardTaskListLtrRegular = exports.ClipboardTaskListLtrFilled = exports.ClipboardTaskAddRegular = exports.ClipboardTaskAddFilled = exports.ClipboardTaskRegular = exports.ClipboardTaskFilled = exports.ClipboardSettingsRegular = exports.ClipboardSettingsFilled = exports.ClipboardSearchRegular = exports.ClipboardSearchFilled = exports.ClipboardPulseRegular = exports.ClipboardPulseFilled = exports.ClipboardPasteRegular = exports.ClipboardPasteFilled = exports.ClipboardNumber123Regular = exports.ClipboardNumber123Filled = exports.ClipboardNoteRegular = exports.ClipboardNoteFilled = exports.ClipboardMoreRegular = void 0;
exports.CodeBlockEditFilled = exports.CodeBlockRegular = exports.CodeBlockFilled = exports.CodeRegular = exports.CodeFilled = exports.CloverRegular = exports.CloverFilled = exports.CloudWordsRegular = exports.CloudWordsFilled = exports.CloudSyncRegular = exports.CloudSyncFilled = exports.CloudSwapRegular = exports.CloudSwapFilled = exports.CloudOffRegular = exports.CloudOffFilled = exports.CloudLinkRegular = exports.CloudLinkFilled = exports.CloudFlowRegular = exports.CloudFlowFilled = exports.CloudErrorRegular = exports.CloudErrorFilled = exports.CloudEditRegular = exports.CloudEditFilled = exports.CloudDismissRegular = exports.CloudDismissFilled = exports.CloudDesktopRegular = exports.CloudDesktopFilled = exports.CloudDatabaseRegular = exports.CloudDatabaseFilled = exports.CloudCubeRegular = exports.CloudCubeFilled = exports.CloudCheckmarkRegular = exports.CloudCheckmarkFilled = exports.CloudBidirectionalRegular = exports.CloudBidirectionalFilled = exports.CloudBeakerRegular = exports.CloudBeakerFilled = exports.CloudArrowUpRegular = exports.CloudArrowUpFilled = exports.CloudArrowRightRegular = exports.CloudArrowRightFilled = exports.CloudArrowDownRegular = exports.CloudArrowDownFilled = exports.CloudArchiveRegular = exports.CloudArchiveFilled = exports.CloudAddRegular = exports.CloudAddFilled = exports.CloudRegular = exports.CloudFilled = exports.ClothesHangerRegular = void 0;
exports.CommentArrowLeftRegular = exports.CommentArrowLeftFilled = exports.CommentAddRegular = exports.CommentAddFilled = exports.CommentRegular = exports.CommentFilled = exports.CommaRegular = exports.CommaFilled = exports.ColumnTripleEditRegular = exports.ColumnTripleEditFilled = exports.ColumnTripleRegular = exports.ColumnTripleFilled = exports.ColumnSingleCompareRegular = exports.ColumnSingleCompareFilled = exports.ColumnEditRegular = exports.ColumnEditFilled = exports.ColumnDoubleCompareRegular = exports.ColumnDoubleCompareFilled = exports.ColumnArrowRightRegular = exports.ColumnArrowRightFilled = exports.ColumnRegular = exports.ColumnFilled = exports.ColorLineAccentRegular = exports.ColorLineRegular = exports.ColorLineFilled = exports.ColorFillAccentRegular = exports.ColorFillRegular = exports.ColorFillFilled = exports.ColorBackgroundAccentRegular = exports.ColorBackgroundRegular = exports.ColorBackgroundFilled = exports.ColorRegular = exports.ColorFilled = exports.CollectionsEmptyRegular = exports.CollectionsEmptyFilled = exports.CollectionsAddRegular = exports.CollectionsAddFilled = exports.CollectionsRegular = exports.CollectionsFilled = exports.CoinStackRegular = exports.CoinStackFilled = exports.CoinMultipleRegular = exports.CoinMultipleFilled = exports.CodeTextEditRegular = exports.CodeTextEditFilled = exports.CodeTextRegular = exports.CodeTextFilled = exports.CodeCircleRegular = exports.CodeCircleFilled = exports.CodeBlockEditRegular = void 0;
exports.ConnectedRegular = exports.ConnectedFilled = exports.ConferenceRoomRegular = exports.ConferenceRoomFilled = exports.ComposeRegular = exports.ComposeFilled = exports.CompassTrueNorthRegular = exports.CompassTrueNorthFilled = exports.CompassNorthwestRegular = exports.CompassNorthwestFilled = exports.CommunicationShieldRegular = exports.CommunicationShieldFilled = exports.CommunicationPersonRegular = exports.CommunicationPersonFilled = exports.CommunicationRegular = exports.CommunicationFilled = exports.CommentTextRegular = exports.CommentTextFilled = exports.CommentQuoteRegular = exports.CommentQuoteFilled = exports.CommentOffRegular = exports.CommentOffFilled = exports.CommentNoteRegular = exports.CommentNoteFilled = exports.CommentMultipleMentionRegular = exports.CommentMultipleMentionFilled = exports.CommentMultipleLinkRegular = exports.CommentMultipleLinkFilled = exports.CommentMultipleCheckmarkRegular = exports.CommentMultipleCheckmarkFilled = exports.CommentMultipleRegular = exports.CommentMultipleFilled = exports.CommentMentionRegular = exports.CommentMentionFilled = exports.CommentLinkRegular = exports.CommentLinkFilled = exports.CommentLightningRegular = exports.CommentLightningFilled = exports.CommentErrorRegular = exports.CommentErrorFilled = exports.CommentEditRegular = exports.CommentEditFilled = exports.CommentDismissRegular = exports.CommentDismissFilled = exports.CommentCheckmarkRegular = exports.CommentCheckmarkFilled = exports.CommentBadgeRegular = exports.CommentBadgeFilled = exports.CommentArrowRightRegular = exports.CommentArrowRightFilled = void 0;
exports.CropInterimRegular = exports.CropInterimFilled = exports.CropArrowRotateRegular = exports.CropArrowRotateFilled = exports.CropRegular = exports.CropFilled = exports.CreditCardToolboxRegular = exports.CreditCardToolboxFilled = exports.CreditCardPersonRegular = exports.CreditCardPersonFilled = exports.CreditCardClockRegular = exports.CreditCardClockFilled = exports.CouchRegular = exports.CouchFilled = exports.CopySelectRegular = exports.CopySelectFilled = exports.CopyArrowRightRegular = exports.CopyArrowRightFilled = exports.CopyAddRegular = exports.CopyAddFilled = exports.CopyRegular = exports.CopyFilled = exports.CookiesRegular = exports.CookiesFilled = exports.ConvertRangeRegular = exports.ConvertRangeFilled = exports.ControlButtonRegular = exports.ControlButtonFilled = exports.ContractUpRightRegular = exports.ContractUpRightFilled = exports.ContractDownLeftRegular = exports.ContractDownLeftFilled = exports.ContentViewGalleryLightningRegular = exports.ContentViewGalleryLightningFilled = exports.ContentViewGalleryRegular = exports.ContentViewGalleryFilled = exports.ContentViewRegular = exports.ContentViewFilled = exports.ContentSettingsRegular = exports.ContentSettingsFilled = exports.ContactCardRibbonRegular = exports.ContactCardRibbonFilled = exports.ContactCardLinkRegular = exports.ContactCardLinkFilled = exports.ContactCardGroupRegular = exports.ContactCardGroupFilled = exports.ContactCardRegular = exports.ContactCardFilled = exports.ConnectorRegular = exports.ConnectorFilled = void 0;
exports.DataBarVerticalRegular = exports.DataBarVerticalFilled = exports.DataBarHorizontalRegular = exports.DataBarHorizontalFilled = exports.DataAreaRegular = exports.DataAreaFilled = exports.DarkThemeRegular = exports.DarkThemeFilled = exports.CutRegular = exports.CutFilled = exports.CursorProhibitedRegular = exports.CursorProhibitedFilled = exports.CursorHoverOffRegular = exports.CursorHoverOffFilled = exports.CursorHoverRegular = exports.CursorHoverFilled = exports.CursorClickRegular = exports.CursorClickFilled = exports.CursorRegular = exports.CursorFilled = exports.CurrencyDollarRupeeRegular = exports.CurrencyDollarRupeeFilled = exports.CurrencyDollarEuroRegular = exports.CurrencyDollarEuroFilled = exports.CubeTreeRegular = exports.CubeTreeFilled = exports.CubeSyncRegular = exports.CubeSyncFilled = exports.CubeRotateRegular = exports.CubeRotateFilled = exports.CubeQuickRegular = exports.CubeQuickFilled = exports.CubeMultipleRegular = exports.CubeMultipleFilled = exports.CubeLinkRegular = exports.CubeLinkFilled = exports.CubeCheckmarkRegular = exports.CubeCheckmarkFilled = exports.CubeArrowCurveDownRegular = exports.CubeArrowCurveDownFilled = exports.CubeAddRegular = exports.CubeAddFilled = exports.CubeRegular = exports.CubeFilled = exports.CrownSubtractRegular = exports.CrownSubtractFilled = exports.CrownRegular = exports.CrownFilled = exports.CropInterimOffRegular = exports.CropInterimOffFilled = void 0;
const createFluentFontIcon_1 = require("../../utils/fonts/createFluentFontIcon");
exports.ChartPersonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChartPersonRegular", "", 2, undefined));
exports.ChatFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatFilled", "", 2, undefined));
exports.ChatRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatRegular", "", 2, undefined));
exports.ChatAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatAddFilled", "", 2, undefined));
exports.ChatAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatAddRegular", "", 2, undefined));
exports.ChatArrowBackFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatArrowBackFilled", "", 2, undefined));
exports.ChatArrowBackRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatArrowBackRegular", "", 2, undefined));
exports.ChatArrowBackDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatArrowBackDownFilled", "", 2, undefined));
exports.ChatArrowBackDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatArrowBackDownRegular", "", 2, undefined));
exports.ChatArrowDoubleBackFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatArrowDoubleBackFilled", "", 2, undefined));
exports.ChatArrowDoubleBackRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatArrowDoubleBackRegular", "", 2, undefined));
exports.ChatBubblesQuestionFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatBubblesQuestionFilled", "", 2, undefined));
exports.ChatBubblesQuestionRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatBubblesQuestionRegular", "", 2, undefined));
exports.ChatCursorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatCursorFilled", "", 2, undefined));
exports.ChatCursorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatCursorRegular", "", 2, undefined));
exports.ChatDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatDismissFilled", "", 2, undefined));
exports.ChatDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatDismissRegular", "", 2, undefined));
exports.ChatEmptyFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatEmptyFilled", "", 2, undefined));
exports.ChatEmptyRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatEmptyRegular", "", 2, undefined));
exports.ChatHelpFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatHelpFilled", "", 2, undefined));
exports.ChatHelpRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatHelpRegular", "", 2, undefined));
exports.ChatHistoryFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatHistoryFilled", "", 2, undefined));
exports.ChatHistoryRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatHistoryRegular", "", 2, undefined));
exports.ChatLockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatLockFilled", "", 2, undefined));
exports.ChatLockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatLockRegular", "", 2, undefined));
exports.ChatMailFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatMailFilled", "", 2, undefined));
exports.ChatMailRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatMailRegular", "", 2, undefined));
exports.ChatMultipleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatMultipleFilled", "", 2, undefined));
exports.ChatMultipleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatMultipleRegular", "", 2, undefined));
exports.ChatMultipleCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatMultipleCheckmarkFilled", "", 2, undefined));
exports.ChatMultipleCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatMultipleCheckmarkRegular", "", 2, undefined));
exports.ChatMultipleHeartFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatMultipleHeartFilled", "", 2, undefined));
exports.ChatMultipleHeartRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatMultipleHeartRegular", "", 2, undefined));
exports.ChatMultipleMinusFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatMultipleMinusFilled", "", 2, undefined));
exports.ChatMultipleMinusRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatMultipleMinusRegular", "", 2, undefined));
exports.ChatOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatOffFilled", "", 2, undefined));
exports.ChatOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatOffRegular", "", 2, undefined));
exports.ChatSettingsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatSettingsFilled", "", 2, undefined));
exports.ChatSettingsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatSettingsRegular", "", 2, undefined));
exports.ChatSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatSparkleFilled", "", 2, undefined));
exports.ChatSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatSparkleRegular", "", 2, undefined));
exports.ChatVideoFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatVideoFilled", "", 2, undefined));
exports.ChatVideoRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatVideoRegular", "", 2, undefined));
exports.ChatWarningFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatWarningFilled", "", 2, undefined));
exports.ChatWarningRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChatWarningRegular", "", 2, undefined));
exports.CheckFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckFilled", "", 2, undefined));
exports.CheckRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckRegular", "", 2, undefined));
exports.Checkbox1Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Checkbox1Filled", "", 2, undefined));
exports.Checkbox1Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Checkbox1Regular", "", 2, undefined));
exports.Checkbox2Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Checkbox2Filled", "", 2, undefined));
exports.Checkbox2Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Checkbox2Regular", "", 2, undefined));
exports.CheckboxArrowRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckboxArrowRightFilled", "", 2, undefined));
exports.CheckboxArrowRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckboxArrowRightRegular", "", 2, undefined));
exports.CheckboxCheckedFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckboxCheckedFilled", "", 2, undefined));
exports.CheckboxCheckedRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckboxCheckedRegular", "", 2, undefined));
exports.CheckboxCheckedSyncFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckboxCheckedSyncFilled", "", 2, undefined));
exports.CheckboxCheckedSyncRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckboxCheckedSyncRegular", "", 2, undefined));
exports.CheckboxIndeterminateFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckboxIndeterminateFilled", "", 2, undefined));
exports.CheckboxIndeterminateRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckboxIndeterminateRegular", "", 2, undefined));
exports.CheckboxPersonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckboxPersonFilled", "", 2, undefined));
exports.CheckboxPersonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckboxPersonRegular", "", 2, undefined));
exports.CheckboxUncheckedFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckboxUncheckedFilled", "", 2, undefined));
exports.CheckboxUncheckedRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckboxUncheckedRegular", "", 2, undefined));
exports.CheckboxWarningFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckboxWarningFilled", "", 2, undefined));
exports.CheckboxWarningRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckboxWarningRegular", "", 2, undefined));
exports.CheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckmarkFilled", "", 2, undefined));
exports.CheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckmarkRegular", "", 2, undefined));
exports.CheckmarkCircleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckmarkCircleFilled", "", 2, undefined));
exports.CheckmarkCircleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckmarkCircleRegular", "", 2, undefined));
exports.CheckmarkCircleHintFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckmarkCircleHintFilled", "", 2, undefined));
exports.CheckmarkCircleHintRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckmarkCircleHintRegular", "", 2, undefined));
exports.CheckmarkCircleSquareFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckmarkCircleSquareFilled", "", 2, undefined));
exports.CheckmarkCircleSquareRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckmarkCircleSquareRegular", "", 2, undefined));
exports.CheckmarkCircleWarningFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckmarkCircleWarningFilled", "", 2, undefined));
exports.CheckmarkCircleWarningRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckmarkCircleWarningRegular", "", 2, undefined));
exports.CheckmarkLockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckmarkLockFilled", "", 2, undefined));
exports.CheckmarkLockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckmarkLockRegular", "", 2, undefined));
exports.CheckmarkNoteFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckmarkNoteFilled", "", 2, undefined));
exports.CheckmarkNoteRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckmarkNoteRegular", "", 2, undefined));
exports.CheckmarkSquareFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckmarkSquareFilled", "", 2, undefined));
exports.CheckmarkSquareRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckmarkSquareRegular", "", 2, undefined));
exports.CheckmarkStarburstFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckmarkStarburstFilled", "", 2, undefined));
exports.CheckmarkStarburstRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckmarkStarburstRegular", "", 2, undefined));
exports.CheckmarkUnderlineCircleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckmarkUnderlineCircleFilled", "", 2, undefined));
exports.CheckmarkUnderlineCircleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CheckmarkUnderlineCircleRegular", "", 2, undefined));
exports.ChessFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChessFilled", "", 2, undefined));
exports.ChessRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChessRegular", "", 2, undefined));
exports.ChevronCircleDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronCircleDownFilled", "", 2, undefined));
exports.ChevronCircleDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronCircleDownRegular", "", 2, undefined));
exports.ChevronCircleLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronCircleLeftFilled", "", 2, undefined));
exports.ChevronCircleLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronCircleLeftRegular", "", 2, undefined));
exports.ChevronCircleRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronCircleRightFilled", "", 2, undefined));
exports.ChevronCircleRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronCircleRightRegular", "", 2, undefined));
exports.ChevronCircleUpFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronCircleUpFilled", "", 2, undefined));
exports.ChevronCircleUpRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronCircleUpRegular", "", 2, undefined));
exports.ChevronDoubleDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronDoubleDownFilled", "", 2, undefined));
exports.ChevronDoubleDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronDoubleDownRegular", "", 2, undefined));
exports.ChevronDoubleLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronDoubleLeftFilled", "", 2, undefined));
exports.ChevronDoubleLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronDoubleLeftRegular", "", 2, undefined));
exports.ChevronDoubleRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronDoubleRightFilled", "", 2, undefined));
exports.ChevronDoubleRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronDoubleRightRegular", "", 2, undefined));
exports.ChevronDoubleUpFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronDoubleUpFilled", "", 2, undefined));
exports.ChevronDoubleUpRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronDoubleUpRegular", "", 2, undefined));
exports.ChevronDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronDownFilled", "", 2, undefined));
exports.ChevronDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronDownRegular", "", 2, undefined));
exports.ChevronDownUpFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronDownUpFilled", "", 2, undefined));
exports.ChevronDownUpRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronDownUpRegular", "", 2, undefined));
exports.ChevronLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronLeftFilled", "", 2, undefined));
exports.ChevronLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronLeftRegular", "", 2, undefined));
exports.ChevronRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronRightFilled", "", 2, undefined));
exports.ChevronRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronRightRegular", "", 2, undefined));
exports.ChevronUpFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronUpFilled", "", 2, undefined));
exports.ChevronUpRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronUpRegular", "", 2, undefined));
exports.ChevronUpDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronUpDownFilled", "", 2, undefined));
exports.ChevronUpDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ChevronUpDownRegular", "", 2, undefined));
exports.CircleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleFilled", "", 2, undefined));
exports.CircleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleRegular", "", 2, undefined));
exports.CircleEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleEditFilled", "", 2, undefined));
exports.CircleEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleEditRegular", "", 2, undefined));
exports.CircleEraserFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleEraserFilled", "", 2, undefined));
exports.CircleEraserRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleEraserRegular", "", 2, undefined));
exports.CircleHalfFillFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleHalfFillFilled", "", 2, undefined));
exports.CircleHalfFillRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleHalfFillRegular", "", 2, undefined));
exports.CircleHighlightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleHighlightFilled", "", 2, undefined));
exports.CircleHighlightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleHighlightRegular", "", 2, undefined));
exports.CircleHintFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleHintFilled", "", 2, undefined));
exports.CircleHintRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleHintRegular", "", 2, undefined));
exports.CircleHintCursorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleHintCursorFilled", "", 2, undefined));
exports.CircleHintCursorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleHintCursorRegular", "", 2, undefined));
exports.CircleHintDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleHintDismissFilled", "", 2, undefined));
exports.CircleHintDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleHintDismissRegular", "", 2, undefined));
exports.CircleHintHalfVerticalFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleHintHalfVerticalFilled", "", 2, undefined));
exports.CircleHintHalfVerticalRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleHintHalfVerticalRegular", "", 2, undefined));
exports.CircleImageFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleImageFilled", "", 2, undefined));
exports.CircleImageRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleImageRegular", "", 2, undefined));
exports.CircleLineFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleLineFilled", "", 2, undefined));
exports.CircleLineRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleLineRegular", "", 2, undefined));
exports.CircleMultipleConcentricFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleMultipleConcentricFilled", "", 2, undefined));
exports.CircleMultipleConcentricRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleMultipleConcentricRegular", "", 2, undefined));
exports.CircleMultipleHintCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleMultipleHintCheckmarkFilled", "", 2, undefined));
exports.CircleMultipleHintCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleMultipleHintCheckmarkRegular", "", 2, undefined));
exports.CircleMultipleSubtractCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleMultipleSubtractCheckmarkFilled", "", 2, undefined));
exports.CircleMultipleSubtractCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleMultipleSubtractCheckmarkRegular", "", 2, undefined));
exports.CircleOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleOffFilled", "", 2, undefined));
exports.CircleOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleOffRegular", "", 2, undefined));
exports.CircleShadowFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleShadowFilled", "", 2, undefined));
exports.CircleShadowRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleShadowRegular", "", 2, undefined));
exports.CircleSmallFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleSmallFilled", "", 2, undefined));
exports.CircleSmallRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleSmallRegular", "", 2, undefined));
exports.CircleSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleSparkleFilled", "", 2, undefined));
exports.CircleSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CircleSparkleRegular", "", 2, undefined));
exports.CityFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CityFilled", "", 2, undefined));
exports.CityRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CityRegular", "", 2, undefined));
exports.ClassFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClassFilled", "", 2, undefined));
exports.ClassRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClassRegular", "", 2, undefined));
exports.ClassificationFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClassificationFilled", "", 2, undefined));
exports.ClassificationRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClassificationRegular", "", 2, undefined));
exports.ClearFormattingFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClearFormattingFilled", "", 2, undefined));
exports.ClearFormattingRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClearFormattingRegular", "", 2, undefined));
exports.ClipboardFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardFilled", "", 2, undefined));
exports.ClipboardRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardRegular", "", 2, undefined));
exports.Clipboard3DayFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Clipboard3DayFilled", "", 2, undefined));
exports.Clipboard3DayRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Clipboard3DayRegular", "", 2, undefined));
exports.ClipboardArrowRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardArrowRightFilled", "", 2, undefined));
exports.ClipboardArrowRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardArrowRightRegular", "", 2, undefined));
exports.ClipboardBrushFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardBrushFilled", "", 2, undefined));
exports.ClipboardBrushRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardBrushRegular", "", 2, undefined));
exports.ClipboardBulletListFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardBulletListFilled", "", 2, undefined, { flipInRtl: true }));
exports.ClipboardBulletListRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardBulletListRegular", "", 2, undefined, { flipInRtl: true }));
exports.ClipboardBulletListLtrFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardBulletListLtrFilled", "", 2, undefined));
exports.ClipboardBulletListLtrRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardBulletListLtrRegular", "", 2, undefined));
exports.ClipboardBulletListRtlFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardBulletListRtlFilled", "", 2, undefined));
exports.ClipboardBulletListRtlRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardBulletListRtlRegular", "", 2, undefined));
exports.ClipboardCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardCheckmarkFilled", "", 2, undefined));
exports.ClipboardCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardCheckmarkRegular", "", 2, undefined));
exports.ClipboardClockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardClockFilled", "", 2, undefined));
exports.ClipboardClockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardClockRegular", "", 2, undefined));
exports.ClipboardCodeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardCodeFilled", "", 2, undefined));
exports.ClipboardCodeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardCodeRegular", "", 2, undefined));
exports.ClipboardDataBarFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardDataBarFilled", "", 2, undefined));
exports.ClipboardDataBarRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardDataBarRegular", "", 2, undefined));
exports.ClipboardDayFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardDayFilled", "", 2, undefined));
exports.ClipboardDayRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardDayRegular", "", 2, undefined));
exports.ClipboardEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardEditFilled", "", 2, undefined));
exports.ClipboardEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardEditRegular", "", 2, undefined));
exports.ClipboardErrorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardErrorFilled", "", 2, undefined));
exports.ClipboardErrorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardErrorRegular", "", 2, undefined));
exports.ClipboardHeartFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardHeartFilled", "", 2, undefined));
exports.ClipboardHeartRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardHeartRegular", "", 2, undefined));
exports.ClipboardImageFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardImageFilled", "", 2, undefined));
exports.ClipboardImageRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardImageRegular", "", 2, undefined));
exports.ClipboardLetterFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardLetterFilled", "", 2, undefined));
exports.ClipboardLetterRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardLetterRegular", "", 2, undefined));
exports.ClipboardLinkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardLinkFilled", "", 2, undefined));
exports.ClipboardLinkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardLinkRegular", "", 2, undefined));
exports.ClipboardMathFormulaFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardMathFormulaFilled", "", 2, undefined));
exports.ClipboardMathFormulaRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardMathFormulaRegular", "", 2, undefined));
exports.ClipboardMonthFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardMonthFilled", "", 2, undefined));
exports.ClipboardMonthRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardMonthRegular", "", 2, undefined));
exports.ClipboardMoreFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardMoreFilled", "", 2, undefined));
exports.ClipboardMoreRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardMoreRegular", "", 2, undefined));
exports.ClipboardNoteFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardNoteFilled", "", 2, undefined));
exports.ClipboardNoteRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardNoteRegular", "", 2, undefined));
exports.ClipboardNumber123Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardNumber123Filled", "", 2, undefined));
exports.ClipboardNumber123Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardNumber123Regular", "", 2, undefined));
exports.ClipboardPasteFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardPasteFilled", "", 2, undefined));
exports.ClipboardPasteRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardPasteRegular", "", 2, undefined));
exports.ClipboardPulseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardPulseFilled", "", 2, undefined));
exports.ClipboardPulseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardPulseRegular", "", 2, undefined));
exports.ClipboardSearchFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardSearchFilled", "", 2, undefined));
exports.ClipboardSearchRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardSearchRegular", "", 2, undefined));
exports.ClipboardSettingsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardSettingsFilled", "", 2, undefined));
exports.ClipboardSettingsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardSettingsRegular", "", 2, undefined));
exports.ClipboardTaskFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardTaskFilled", "", 2, undefined));
exports.ClipboardTaskRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardTaskRegular", "", 2, undefined));
exports.ClipboardTaskAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardTaskAddFilled", "", 2, undefined));
exports.ClipboardTaskAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardTaskAddRegular", "", 2, undefined));
exports.ClipboardTaskListLtrFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardTaskListLtrFilled", "", 2, undefined));
exports.ClipboardTaskListLtrRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardTaskListLtrRegular", "", 2, undefined));
exports.ClipboardTaskListRtlFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardTaskListRtlFilled", "", 2, undefined));
exports.ClipboardTaskListRtlRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardTaskListRtlRegular", "", 2, undefined));
exports.ClipboardTextEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardTextEditFilled", "", 2, undefined));
exports.ClipboardTextEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardTextEditRegular", "", 2, undefined));
exports.ClipboardTextLtrFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardTextLtrFilled", "", 2, undefined));
exports.ClipboardTextLtrRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardTextLtrRegular", "", 2, undefined));
exports.ClipboardTextRtlFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardTextRtlFilled", "", 2, undefined));
exports.ClipboardTextRtlRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClipboardTextRtlRegular", "", 2, undefined));
exports.ClockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClockFilled", "", 2, undefined));
exports.ClockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClockRegular", "", 2, undefined));
exports.ClockAlarmFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClockAlarmFilled", "", 2, undefined));
exports.ClockAlarmRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClockAlarmRegular", "", 2, undefined));
exports.ClockArrowDownloadFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClockArrowDownloadFilled", "", 2, undefined));
exports.ClockArrowDownloadRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClockArrowDownloadRegular", "", 2, undefined));
exports.ClockBillFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClockBillFilled", "", 2, undefined));
exports.ClockBillRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClockBillRegular", "", 2, undefined));
exports.ClockDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClockDismissFilled", "", 2, undefined));
exports.ClockDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClockDismissRegular", "", 2, undefined));
exports.ClockLockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClockLockFilled", "", 2, undefined));
exports.ClockLockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClockLockRegular", "", 2, undefined));
exports.ClockPauseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClockPauseFilled", "", 2, undefined));
exports.ClockPauseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClockPauseRegular", "", 2, undefined));
exports.ClockSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClockSparkleFilled", "", 2, undefined));
exports.ClockSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClockSparkleRegular", "", 2, undefined));
exports.ClockToolboxFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClockToolboxFilled", "", 2, undefined));
exports.ClockToolboxRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClockToolboxRegular", "", 2, undefined));
exports.ClosedCaptionFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClosedCaptionFilled", "", 2, undefined));
exports.ClosedCaptionRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClosedCaptionRegular", "", 2, undefined));
exports.ClosedCaptionOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClosedCaptionOffFilled", "", 2, undefined));
exports.ClosedCaptionOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClosedCaptionOffRegular", "", 2, undefined));
exports.ClothesHangerFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClothesHangerFilled", "", 2, undefined));
exports.ClothesHangerRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ClothesHangerRegular", "", 2, undefined));
exports.CloudFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudFilled", "", 2, undefined));
exports.CloudRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudRegular", "", 2, undefined));
exports.CloudAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudAddFilled", "", 2, undefined));
exports.CloudAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudAddRegular", "", 2, undefined));
exports.CloudArchiveFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudArchiveFilled", "", 2, undefined));
exports.CloudArchiveRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudArchiveRegular", "", 2, undefined));
exports.CloudArrowDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudArrowDownFilled", "", 2, undefined));
exports.CloudArrowDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudArrowDownRegular", "", 2, undefined));
exports.CloudArrowRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudArrowRightFilled", "", 2, undefined));
exports.CloudArrowRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudArrowRightRegular", "", 2, undefined));
exports.CloudArrowUpFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudArrowUpFilled", "", 2, undefined));
exports.CloudArrowUpRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudArrowUpRegular", "", 2, undefined));
exports.CloudBeakerFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudBeakerFilled", "", 2, undefined));
exports.CloudBeakerRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudBeakerRegular", "", 2, undefined));
exports.CloudBidirectionalFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudBidirectionalFilled", "", 2, undefined));
exports.CloudBidirectionalRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudBidirectionalRegular", "", 2, undefined));
exports.CloudCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudCheckmarkFilled", "", 2, undefined));
exports.CloudCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudCheckmarkRegular", "", 2, undefined));
exports.CloudCubeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudCubeFilled", "", 2, undefined));
exports.CloudCubeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudCubeRegular", "", 2, undefined));
exports.CloudDatabaseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudDatabaseFilled", "", 2, undefined));
exports.CloudDatabaseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudDatabaseRegular", "", 2, undefined));
exports.CloudDesktopFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudDesktopFilled", "", 2, undefined));
exports.CloudDesktopRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudDesktopRegular", "", 2, undefined));
exports.CloudDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudDismissFilled", "", 2, undefined));
exports.CloudDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudDismissRegular", "", 2, undefined));
exports.CloudEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudEditFilled", "", 2, undefined));
exports.CloudEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudEditRegular", "", 2, undefined));
exports.CloudErrorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudErrorFilled", "", 2, undefined));
exports.CloudErrorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudErrorRegular", "", 2, undefined));
exports.CloudFlowFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudFlowFilled", "", 2, undefined));
exports.CloudFlowRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudFlowRegular", "", 2, undefined));
exports.CloudLinkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudLinkFilled", "", 2, undefined));
exports.CloudLinkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudLinkRegular", "", 2, undefined));
exports.CloudOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudOffFilled", "", 2, undefined));
exports.CloudOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudOffRegular", "", 2, undefined));
exports.CloudSwapFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudSwapFilled", "", 2, undefined));
exports.CloudSwapRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudSwapRegular", "", 2, undefined));
exports.CloudSyncFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudSyncFilled", "", 2, undefined));
exports.CloudSyncRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudSyncRegular", "", 2, undefined));
exports.CloudWordsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudWordsFilled", "", 2, undefined));
exports.CloudWordsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloudWordsRegular", "", 2, undefined));
exports.CloverFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloverFilled", "", 2, undefined));
exports.CloverRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CloverRegular", "", 2, undefined));
exports.CodeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CodeFilled", "", 2, undefined));
exports.CodeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CodeRegular", "", 2, undefined));
exports.CodeBlockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CodeBlockFilled", "", 2, undefined));
exports.CodeBlockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CodeBlockRegular", "", 2, undefined));
exports.CodeBlockEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CodeBlockEditFilled", "", 2, undefined));
exports.CodeBlockEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CodeBlockEditRegular", "", 2, undefined));
exports.CodeCircleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CodeCircleFilled", "", 2, undefined));
exports.CodeCircleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CodeCircleRegular", "", 2, undefined));
exports.CodeTextFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CodeTextFilled", "", 2, undefined));
exports.CodeTextRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CodeTextRegular", "", 2, undefined));
exports.CodeTextEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CodeTextEditFilled", "", 2, undefined));
exports.CodeTextEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CodeTextEditRegular", "", 2, undefined));
exports.CoinMultipleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CoinMultipleFilled", "", 2, undefined));
exports.CoinMultipleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CoinMultipleRegular", "", 2, undefined));
exports.CoinStackFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CoinStackFilled", "", 2, undefined));
exports.CoinStackRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CoinStackRegular", "", 2, undefined));
exports.CollectionsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CollectionsFilled", "", 2, undefined));
exports.CollectionsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CollectionsRegular", "", 2, undefined));
exports.CollectionsAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CollectionsAddFilled", "", 2, undefined));
exports.CollectionsAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CollectionsAddRegular", "", 2, undefined));
exports.CollectionsEmptyFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CollectionsEmptyFilled", "", 2, undefined));
exports.CollectionsEmptyRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CollectionsEmptyRegular", "", 2, undefined));
exports.ColorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColorFilled", "", 2, undefined));
exports.ColorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColorRegular", "", 2, undefined));
exports.ColorBackgroundFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColorBackgroundFilled", "", 2, undefined));
exports.ColorBackgroundRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColorBackgroundRegular", "", 2, undefined));
exports.ColorBackgroundAccentRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColorBackgroundAccentRegular", "", 2, undefined));
exports.ColorFillFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColorFillFilled", "", 2, undefined));
exports.ColorFillRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColorFillRegular", "", 2, undefined));
exports.ColorFillAccentRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColorFillAccentRegular", "", 2, undefined));
exports.ColorLineFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColorLineFilled", "", 2, undefined));
exports.ColorLineRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColorLineRegular", "", 2, undefined));
exports.ColorLineAccentRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColorLineAccentRegular", "", 2, undefined));
exports.ColumnFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColumnFilled", "", 2, undefined));
exports.ColumnRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColumnRegular", "", 2, undefined));
exports.ColumnArrowRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColumnArrowRightFilled", "", 2, undefined));
exports.ColumnArrowRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColumnArrowRightRegular", "", 2, undefined));
exports.ColumnDoubleCompareFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColumnDoubleCompareFilled", "", 2, undefined));
exports.ColumnDoubleCompareRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColumnDoubleCompareRegular", "", 2, undefined));
exports.ColumnEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColumnEditFilled", "", 2, undefined));
exports.ColumnEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColumnEditRegular", "", 2, undefined));
exports.ColumnSingleCompareFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColumnSingleCompareFilled", "", 2, undefined));
exports.ColumnSingleCompareRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColumnSingleCompareRegular", "", 2, undefined));
exports.ColumnTripleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColumnTripleFilled", "", 2, undefined));
exports.ColumnTripleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColumnTripleRegular", "", 2, undefined));
exports.ColumnTripleEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColumnTripleEditFilled", "", 2, undefined));
exports.ColumnTripleEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ColumnTripleEditRegular", "", 2, undefined));
exports.CommaFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommaFilled", "", 2, undefined));
exports.CommaRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommaRegular", "", 2, undefined));
exports.CommentFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentFilled", "", 2, undefined, { flipInRtl: true }));
exports.CommentRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentRegular", "", 2, undefined, { flipInRtl: true }));
exports.CommentAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentAddFilled", "", 2, undefined, { flipInRtl: true }));
exports.CommentAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentAddRegular", "", 2, undefined, { flipInRtl: true }));
exports.CommentArrowLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentArrowLeftFilled", "", 2, undefined, { flipInRtl: true }));
exports.CommentArrowLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentArrowLeftRegular", "", 2, undefined, { flipInRtl: true }));
exports.CommentArrowRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentArrowRightFilled", "", 2, undefined, { flipInRtl: true }));
exports.CommentArrowRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentArrowRightRegular", "", 2, undefined, { flipInRtl: true }));
exports.CommentBadgeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentBadgeFilled", "", 2, undefined));
exports.CommentBadgeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentBadgeRegular", "", 2, undefined));
exports.CommentCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentCheckmarkFilled", "", 2, undefined, { flipInRtl: true }));
exports.CommentCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentCheckmarkRegular", "", 2, undefined, { flipInRtl: true }));
exports.CommentDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentDismissFilled", "", 2, undefined, { flipInRtl: true }));
exports.CommentDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentDismissRegular", "", 2, undefined, { flipInRtl: true }));
exports.CommentEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentEditFilled", "", 2, undefined, { flipInRtl: true }));
exports.CommentEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentEditRegular", "", 2, undefined, { flipInRtl: true }));
exports.CommentErrorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentErrorFilled", "", 2, undefined, { flipInRtl: true }));
exports.CommentErrorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentErrorRegular", "", 2, undefined, { flipInRtl: true }));
exports.CommentLightningFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentLightningFilled", "", 2, undefined, { flipInRtl: true }));
exports.CommentLightningRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentLightningRegular", "", 2, undefined, { flipInRtl: true }));
exports.CommentLinkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentLinkFilled", "", 2, undefined, { flipInRtl: true }));
exports.CommentLinkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentLinkRegular", "", 2, undefined, { flipInRtl: true }));
exports.CommentMentionFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentMentionFilled", "", 2, undefined, { flipInRtl: true }));
exports.CommentMentionRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentMentionRegular", "", 2, undefined, { flipInRtl: true }));
exports.CommentMultipleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentMultipleFilled", "", 2, undefined, { flipInRtl: true }));
exports.CommentMultipleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentMultipleRegular", "", 2, undefined, { flipInRtl: true }));
exports.CommentMultipleCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentMultipleCheckmarkFilled", "", 2, undefined, { flipInRtl: true }));
exports.CommentMultipleCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentMultipleCheckmarkRegular", "", 2, undefined, { flipInRtl: true }));
exports.CommentMultipleLinkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentMultipleLinkFilled", "", 2, undefined, { flipInRtl: true }));
exports.CommentMultipleLinkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentMultipleLinkRegular", "", 2, undefined, { flipInRtl: true }));
exports.CommentMultipleMentionFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentMultipleMentionFilled", "", 2, undefined, { flipInRtl: true }));
exports.CommentMultipleMentionRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentMultipleMentionRegular", "", 2, undefined, { flipInRtl: true }));
exports.CommentNoteFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentNoteFilled", "", 2, undefined, { flipInRtl: true }));
exports.CommentNoteRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentNoteRegular", "", 2, undefined, { flipInRtl: true }));
exports.CommentOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentOffFilled", "", 2, undefined, { flipInRtl: true }));
exports.CommentOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentOffRegular", "", 2, undefined, { flipInRtl: true }));
exports.CommentQuoteFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentQuoteFilled", "", 2, undefined));
exports.CommentQuoteRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentQuoteRegular", "", 2, undefined));
exports.CommentTextFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentTextFilled", "", 2, undefined, { flipInRtl: true }));
exports.CommentTextRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommentTextRegular", "", 2, undefined, { flipInRtl: true }));
exports.CommunicationFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommunicationFilled", "", 2, undefined));
exports.CommunicationRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommunicationRegular", "", 2, undefined));
exports.CommunicationPersonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommunicationPersonFilled", "", 2, undefined));
exports.CommunicationPersonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommunicationPersonRegular", "", 2, undefined));
exports.CommunicationShieldFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommunicationShieldFilled", "", 2, undefined));
exports.CommunicationShieldRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CommunicationShieldRegular", "", 2, undefined));
exports.CompassNorthwestFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CompassNorthwestFilled", "", 2, undefined));
exports.CompassNorthwestRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CompassNorthwestRegular", "", 2, undefined));
exports.CompassTrueNorthFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CompassTrueNorthFilled", "", 2, undefined));
exports.CompassTrueNorthRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CompassTrueNorthRegular", "", 2, undefined));
exports.ComposeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ComposeFilled", "", 2, undefined));
exports.ComposeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ComposeRegular", "", 2, undefined));
exports.ConferenceRoomFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ConferenceRoomFilled", "", 2, undefined));
exports.ConferenceRoomRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ConferenceRoomRegular", "", 2, undefined));
exports.ConnectedFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ConnectedFilled", "", 2, undefined));
exports.ConnectedRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ConnectedRegular", "", 2, undefined));
exports.ConnectorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ConnectorFilled", "", 2, undefined));
exports.ConnectorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ConnectorRegular", "", 2, undefined));
exports.ContactCardFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ContactCardFilled", "", 2, undefined));
exports.ContactCardRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ContactCardRegular", "", 2, undefined));
exports.ContactCardGroupFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ContactCardGroupFilled", "", 2, undefined));
exports.ContactCardGroupRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ContactCardGroupRegular", "", 2, undefined));
exports.ContactCardLinkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ContactCardLinkFilled", "", 2, undefined));
exports.ContactCardLinkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ContactCardLinkRegular", "", 2, undefined));
exports.ContactCardRibbonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ContactCardRibbonFilled", "", 2, undefined));
exports.ContactCardRibbonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ContactCardRibbonRegular", "", 2, undefined));
exports.ContentSettingsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ContentSettingsFilled", "", 2, undefined));
exports.ContentSettingsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ContentSettingsRegular", "", 2, undefined));
exports.ContentViewFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ContentViewFilled", "", 2, undefined));
exports.ContentViewRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ContentViewRegular", "", 2, undefined));
exports.ContentViewGalleryFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ContentViewGalleryFilled", "", 2, undefined));
exports.ContentViewGalleryRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ContentViewGalleryRegular", "", 2, undefined));
exports.ContentViewGalleryLightningFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ContentViewGalleryLightningFilled", "", 2, undefined));
exports.ContentViewGalleryLightningRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ContentViewGalleryLightningRegular", "", 2, undefined));
exports.ContractDownLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ContractDownLeftFilled", "", 2, undefined));
exports.ContractDownLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ContractDownLeftRegular", "", 2, undefined));
exports.ContractUpRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ContractUpRightFilled", "", 2, undefined));
exports.ContractUpRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ContractUpRightRegular", "", 2, undefined));
exports.ControlButtonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ControlButtonFilled", "", 2, undefined));
exports.ControlButtonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ControlButtonRegular", "", 2, undefined));
exports.ConvertRangeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ConvertRangeFilled", "", 2, undefined));
exports.ConvertRangeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ConvertRangeRegular", "", 2, undefined));
exports.CookiesFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CookiesFilled", "", 2, undefined));
exports.CookiesRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CookiesRegular", "", 2, undefined));
exports.CopyFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CopyFilled", "", 2, undefined));
exports.CopyRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CopyRegular", "", 2, undefined));
exports.CopyAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CopyAddFilled", "", 2, undefined));
exports.CopyAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CopyAddRegular", "", 2, undefined));
exports.CopyArrowRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CopyArrowRightFilled", "", 2, undefined));
exports.CopyArrowRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CopyArrowRightRegular", "", 2, undefined));
exports.CopySelectFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CopySelectFilled", "", 2, undefined));
exports.CopySelectRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CopySelectRegular", "", 2, undefined));
exports.CouchFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CouchFilled", "", 2, undefined));
exports.CouchRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CouchRegular", "", 2, undefined));
exports.CreditCardClockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CreditCardClockFilled", "", 2, undefined));
exports.CreditCardClockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CreditCardClockRegular", "", 2, undefined));
exports.CreditCardPersonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CreditCardPersonFilled", "", 2, undefined));
exports.CreditCardPersonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CreditCardPersonRegular", "", 2, undefined));
exports.CreditCardToolboxFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CreditCardToolboxFilled", "", 2, undefined));
exports.CreditCardToolboxRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CreditCardToolboxRegular", "", 2, undefined));
exports.CropFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CropFilled", "", 2, undefined));
exports.CropRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CropRegular", "", 2, undefined));
exports.CropArrowRotateFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CropArrowRotateFilled", "", 2, undefined));
exports.CropArrowRotateRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CropArrowRotateRegular", "", 2, undefined));
exports.CropInterimFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CropInterimFilled", "", 2, undefined));
exports.CropInterimRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CropInterimRegular", "", 2, undefined));
exports.CropInterimOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CropInterimOffFilled", "", 2, undefined));
exports.CropInterimOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CropInterimOffRegular", "", 2, undefined));
exports.CrownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CrownFilled", "", 2, undefined));
exports.CrownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CrownRegular", "", 2, undefined));
exports.CrownSubtractFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CrownSubtractFilled", "", 2, undefined));
exports.CrownSubtractRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CrownSubtractRegular", "", 2, undefined));
exports.CubeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CubeFilled", "", 2, undefined));
exports.CubeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CubeRegular", "", 2, undefined));
exports.CubeAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CubeAddFilled", "", 2, undefined));
exports.CubeAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CubeAddRegular", "", 2, undefined));
exports.CubeArrowCurveDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CubeArrowCurveDownFilled", "", 2, undefined));
exports.CubeArrowCurveDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CubeArrowCurveDownRegular", "", 2, undefined));
exports.CubeCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CubeCheckmarkFilled", "", 2, undefined));
exports.CubeCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CubeCheckmarkRegular", "", 2, undefined));
exports.CubeLinkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CubeLinkFilled", "", 2, undefined));
exports.CubeLinkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CubeLinkRegular", "", 2, undefined));
exports.CubeMultipleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CubeMultipleFilled", "", 2, undefined));
exports.CubeMultipleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CubeMultipleRegular", "", 2, undefined));
exports.CubeQuickFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CubeQuickFilled", "", 2, undefined));
exports.CubeQuickRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CubeQuickRegular", "", 2, undefined));
exports.CubeRotateFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CubeRotateFilled", "", 2, undefined));
exports.CubeRotateRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CubeRotateRegular", "", 2, undefined));
exports.CubeSyncFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CubeSyncFilled", "", 2, undefined));
exports.CubeSyncRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CubeSyncRegular", "", 2, undefined));
exports.CubeTreeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CubeTreeFilled", "", 2, undefined));
exports.CubeTreeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CubeTreeRegular", "", 2, undefined));
exports.CurrencyDollarEuroFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CurrencyDollarEuroFilled", "", 2, undefined));
exports.CurrencyDollarEuroRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CurrencyDollarEuroRegular", "", 2, undefined));
exports.CurrencyDollarRupeeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CurrencyDollarRupeeFilled", "", 2, undefined));
exports.CurrencyDollarRupeeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CurrencyDollarRupeeRegular", "", 2, undefined));
exports.CursorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CursorFilled", "", 2, undefined));
exports.CursorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CursorRegular", "", 2, undefined));
exports.CursorClickFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CursorClickFilled", "", 2, undefined));
exports.CursorClickRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CursorClickRegular", "", 2, undefined));
exports.CursorHoverFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CursorHoverFilled", "", 2, undefined));
exports.CursorHoverRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CursorHoverRegular", "", 2, undefined));
exports.CursorHoverOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CursorHoverOffFilled", "", 2, undefined));
exports.CursorHoverOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CursorHoverOffRegular", "", 2, undefined));
exports.CursorProhibitedFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CursorProhibitedFilled", "", 2, undefined));
exports.CursorProhibitedRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CursorProhibitedRegular", "", 2, undefined));
exports.CutFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CutFilled", "", 2, undefined));
exports.CutRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("CutRegular", "", 2, undefined));
exports.DarkThemeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DarkThemeFilled", "", 2, undefined));
exports.DarkThemeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DarkThemeRegular", "", 2, undefined));
exports.DataAreaFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataAreaFilled", "", 2, undefined));
exports.DataAreaRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataAreaRegular", "", 2, undefined));
exports.DataBarHorizontalFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataBarHorizontalFilled", "", 2, undefined));
exports.DataBarHorizontalRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataBarHorizontalRegular", "", 2, undefined));
exports.DataBarVerticalFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataBarVerticalFilled", "", 2, undefined));
exports.DataBarVerticalRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("DataBarVerticalRegular", "", 2, undefined));
