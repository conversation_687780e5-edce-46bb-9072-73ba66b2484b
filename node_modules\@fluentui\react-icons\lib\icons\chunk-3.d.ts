import type { FluentIcon } from "../utils/createFluentIcon";
export declare const MailInboxAllFilled: FluentIcon;
export declare const MailInboxAllRegular: FluentIcon;
export declare const MailInboxArrowDownFilled: FluentIcon;
export declare const MailInboxArrowDownRegular: FluentIcon;
export declare const MailInboxArrowRightFilled: FluentIcon;
export declare const MailInboxArrowRightRegular: FluentIcon;
export declare const MailInboxArrowUpFilled: FluentIcon;
export declare const MailInboxArrowUpRegular: FluentIcon;
export declare const MailInboxCheckmarkFilled: FluentIcon;
export declare const MailInboxCheckmarkRegular: FluentIcon;
export declare const MailInboxDismissFilled: FluentIcon;
export declare const MailInboxDismissRegular: FluentIcon;
export declare const MailInboxPersonFilled: FluentIcon;
export declare const MailInboxPersonRegular: FluentIcon;
export declare const MailLinkFilled: FluentIcon;
export declare const MailLinkRegular: FluentIcon;
export declare const MailListFilled: FluentIcon;
export declare const MailListRegular: FluentIcon;
export declare const MailMultipleColor: FluentIcon;
export declare const MailMultipleFilled: FluentIcon;
export declare const MailMultipleRegular: FluentIcon;
export declare const MailOffFilled: FluentIcon;
export declare const MailOffRegular: FluentIcon;
export declare const MailOpenPersonFilled: FluentIcon;
export declare const MailOpenPersonRegular: FluentIcon;
export declare const MailPauseFilled: FluentIcon;
export declare const MailPauseRegular: FluentIcon;
export declare const MailProhibitedFilled: FluentIcon;
export declare const MailProhibitedRegular: FluentIcon;
export declare const MailReadFilled: FluentIcon;
export declare const MailReadRegular: FluentIcon;
export declare const MailReadBriefcaseFilled: FluentIcon;
export declare const MailReadBriefcaseRegular: FluentIcon;
export declare const MailReadMultipleFilled: FluentIcon;
export declare const MailReadMultipleRegular: FluentIcon;
export declare const MailRewindFilled: FluentIcon;
export declare const MailRewindRegular: FluentIcon;
export declare const MailSettingsFilled: FluentIcon;
export declare const MailSettingsRegular: FluentIcon;
export declare const MailShieldFilled: FluentIcon;
export declare const MailShieldRegular: FluentIcon;
export declare const MailTemplateFilled: FluentIcon;
export declare const MailTemplateRegular: FluentIcon;
export declare const MailUnreadFilled: FluentIcon;
export declare const MailUnreadRegular: FluentIcon;
export declare const MailWarningFilled: FluentIcon;
export declare const MailWarningRegular: FluentIcon;
export declare const MailboxFilled: FluentIcon;
export declare const MailboxRegular: FluentIcon;
export declare const MapFilled: FluentIcon;
export declare const MapRegular: FluentIcon;
export declare const MapDriveFilled: FluentIcon;
export declare const MapDriveRegular: FluentIcon;
export declare const MarkdownFilled: FluentIcon;
export declare const MarkdownRegular: FluentIcon;
export declare const MatchAppLayoutFilled: FluentIcon;
export declare const MatchAppLayoutRegular: FluentIcon;
export declare const MathFormatLinearFilled: FluentIcon;
export declare const MathFormatLinearRegular: FluentIcon;
export declare const MathFormatProfessionalFilled: FluentIcon;
export declare const MathFormatProfessionalRegular: FluentIcon;
export declare const MathFormulaFilled: FluentIcon;
export declare const MathFormulaRegular: FluentIcon;
export declare const MathFormulaSparkleFilled: FluentIcon;
export declare const MathFormulaSparkleRegular: FluentIcon;
export declare const MathSymbolsFilled: FluentIcon;
export declare const MathSymbolsRegular: FluentIcon;
export declare const MaximizeFilled: FluentIcon;
export declare const MaximizeRegular: FluentIcon;
export declare const MeetNowFilled: FluentIcon;
export declare const MeetNowRegular: FluentIcon;
export declare const MegaphoneFilled: FluentIcon;
export declare const MegaphoneRegular: FluentIcon;
export declare const MegaphoneCircleFilled: FluentIcon;
export declare const MegaphoneCircleRegular: FluentIcon;
export declare const MegaphoneLoudColor: FluentIcon;
export declare const MegaphoneLoudFilled: FluentIcon;
export declare const MegaphoneLoudRegular: FluentIcon;
export declare const MegaphoneOffFilled: FluentIcon;
export declare const MegaphoneOffRegular: FluentIcon;
export declare const MentionFilled: FluentIcon;
export declare const MentionRegular: FluentIcon;
export declare const MentionArrowDownFilled: FluentIcon;
export declare const MentionArrowDownRegular: FluentIcon;
export declare const MentionBracketsFilled: FluentIcon;
export declare const MentionBracketsRegular: FluentIcon;
export declare const MergeFilled: FluentIcon;
export declare const MergeRegular: FluentIcon;
export declare const MicColor: FluentIcon;
export declare const MicFilled: FluentIcon;
export declare const MicRegular: FluentIcon;
export declare const MicLinkFilled: FluentIcon;
export declare const MicLinkRegular: FluentIcon;
export declare const MicOffFilled: FluentIcon;
export declare const MicOffRegular: FluentIcon;
export declare const MicProhibitedFilled: FluentIcon;
export declare const MicProhibitedRegular: FluentIcon;
export declare const MicPulseFilled: FluentIcon;
export declare const MicPulseRegular: FluentIcon;
export declare const MicPulseOffFilled: FluentIcon;
export declare const MicPulseOffRegular: FluentIcon;
export declare const MicRecordFilled: FluentIcon;
export declare const MicRecordRegular: FluentIcon;
export declare const MicSettingsFilled: FluentIcon;
export declare const MicSettingsRegular: FluentIcon;
export declare const MicSparkleFilled: FluentIcon;
export declare const MicSparkleRegular: FluentIcon;
export declare const MicSyncFilled: FluentIcon;
export declare const MicSyncRegular: FluentIcon;
export declare const MicroscopeFilled: FluentIcon;
export declare const MicroscopeRegular: FluentIcon;
export declare const MidiFilled: FluentIcon;
export declare const MidiRegular: FluentIcon;
export declare const MobileOptimizedFilled: FluentIcon;
export declare const MobileOptimizedRegular: FluentIcon;
export declare const MoldFilled: FluentIcon;
export declare const MoldRegular: FluentIcon;
export declare const MoleculeColor: FluentIcon;
export declare const MoleculeFilled: FluentIcon;
export declare const MoleculeRegular: FluentIcon;
export declare const MoneyFilled: FluentIcon;
export declare const MoneyRegular: FluentIcon;
export declare const MoneyCalculatorFilled: FluentIcon;
export declare const MoneyCalculatorRegular: FluentIcon;
export declare const MoneyDismissFilled: FluentIcon;
export declare const MoneyDismissRegular: FluentIcon;
export declare const MoneyHandFilled: FluentIcon;
export declare const MoneyHandRegular: FluentIcon;
export declare const MoneyOffFilled: FluentIcon;
export declare const MoneyOffRegular: FluentIcon;
export declare const MoneySettingsFilled: FluentIcon;
export declare const MoneySettingsRegular: FluentIcon;
export declare const MoreCircleFilled: FluentIcon;
export declare const MoreCircleRegular: FluentIcon;
export declare const MoreHorizontalFilled: FluentIcon;
export declare const MoreHorizontalRegular: FluentIcon;
export declare const MoreVerticalFilled: FluentIcon;
export declare const MoreVerticalRegular: FluentIcon;
export declare const MountainLocationBottomFilled: FluentIcon;
export declare const MountainLocationBottomRegular: FluentIcon;
export declare const MountainLocationTopFilled: FluentIcon;
export declare const MountainLocationTopRegular: FluentIcon;
export declare const MountainTrailFilled: FluentIcon;
export declare const MountainTrailRegular: FluentIcon;
export declare const MoviesAndTvFilled: FluentIcon;
export declare const MoviesAndTvRegular: FluentIcon;
export declare const Multiplier12XFilled: FluentIcon;
export declare const Multiplier12XRegular: FluentIcon;
export declare const Multiplier15XFilled: FluentIcon;
export declare const Multiplier15XRegular: FluentIcon;
export declare const Multiplier18XFilled: FluentIcon;
export declare const Multiplier18XRegular: FluentIcon;
export declare const Multiplier1XFilled: FluentIcon;
export declare const Multiplier1XRegular: FluentIcon;
export declare const Multiplier2XFilled: FluentIcon;
export declare const Multiplier2XRegular: FluentIcon;
export declare const Multiplier5XFilled: FluentIcon;
export declare const Multiplier5XRegular: FluentIcon;
export declare const MultiselectLtrFilled: FluentIcon;
export declare const MultiselectLtrRegular: FluentIcon;
export declare const MultiselectRtlFilled: FluentIcon;
export declare const MultiselectRtlRegular: FluentIcon;
export declare const MusicNote1Filled: FluentIcon;
export declare const MusicNote1Regular: FluentIcon;
export declare const MusicNote2Filled: FluentIcon;
export declare const MusicNote2Regular: FluentIcon;
export declare const MusicNote2PlayFilled: FluentIcon;
export declare const MusicNote2PlayRegular: FluentIcon;
export declare const MusicNoteOff1Filled: FluentIcon;
export declare const MusicNoteOff1Regular: FluentIcon;
export declare const MusicNoteOff2Filled: FluentIcon;
export declare const MusicNoteOff2Regular: FluentIcon;
export declare const MyLocationFilled: FluentIcon;
export declare const MyLocationRegular: FluentIcon;
export declare const NavigationFilled: FluentIcon;
export declare const NavigationRegular: FluentIcon;
export declare const NavigationBriefcaseFilled: FluentIcon;
export declare const NavigationBriefcaseRegular: FluentIcon;
export declare const NavigationLocationTargetFilled: FluentIcon;
export declare const NavigationLocationTargetRegular: FluentIcon;
export declare const NavigationPersonFilled: FluentIcon;
export declare const NavigationPersonRegular: FluentIcon;
export declare const NavigationPlayFilled: FluentIcon;
export declare const NavigationPlayRegular: FluentIcon;
export declare const NavigationUnreadFilled: FluentIcon;
export declare const NavigationUnreadRegular: FluentIcon;
export declare const NetworkCheckFilled: FluentIcon;
export declare const NetworkCheckRegular: FluentIcon;
export declare const NewFilled: FluentIcon;
export declare const NewRegular: FluentIcon;
export declare const NewsColor: FluentIcon;
export declare const NewsFilled: FluentIcon;
export declare const NewsRegular: FluentIcon;
export declare const NextFilled: FluentIcon;
export declare const NextRegular: FluentIcon;
export declare const NextFrameFilled: FluentIcon;
export declare const NextFrameRegular: FluentIcon;
export declare const NoteFilled: FluentIcon;
export declare const NoteRegular: FluentIcon;
export declare const NoteAddFilled: FluentIcon;
export declare const NoteAddRegular: FluentIcon;
export declare const NoteEditFilled: FluentIcon;
export declare const NoteEditRegular: FluentIcon;
export declare const NotePinFilled: FluentIcon;
export declare const NotePinRegular: FluentIcon;
export declare const NotebookColor: FluentIcon;
export declare const NotebookFilled: FluentIcon;
export declare const NotebookRegular: FluentIcon;
export declare const NotebookAddFilled: FluentIcon;
export declare const NotebookAddRegular: FluentIcon;
export declare const NotebookArrowCurveDownFilled: FluentIcon;
export declare const NotebookArrowCurveDownRegular: FluentIcon;
export declare const NotebookErrorFilled: FluentIcon;
export declare const NotebookErrorRegular: FluentIcon;
export declare const NotebookEyeFilled: FluentIcon;
export declare const NotebookEyeRegular: FluentIcon;
export declare const NotebookLightningFilled: FluentIcon;
export declare const NotebookLightningRegular: FluentIcon;
export declare const NotebookQuestionMarkColor: FluentIcon;
export declare const NotebookQuestionMarkFilled: FluentIcon;
export declare const NotebookQuestionMarkRegular: FluentIcon;
export declare const NotebookSectionFilled: FluentIcon;
export declare const NotebookSectionRegular: FluentIcon;
export declare const NotebookSectionArrowRightFilled: FluentIcon;
export declare const NotebookSectionArrowRightRegular: FluentIcon;
export declare const NotebookSubsectionFilled: FluentIcon;
export declare const NotebookSubsectionRegular: FluentIcon;
export declare const NotebookSyncFilled: FluentIcon;
export declare const NotebookSyncRegular: FluentIcon;
export declare const NotepadFilled: FluentIcon;
export declare const NotepadRegular: FluentIcon;
export declare const NotepadEditFilled: FluentIcon;
export declare const NotepadEditRegular: FluentIcon;
export declare const NotepadPersonFilled: FluentIcon;
export declare const NotepadPersonRegular: FluentIcon;
export declare const NotepadPersonOffFilled: FluentIcon;
export declare const NotepadPersonOffRegular: FluentIcon;
export declare const NotepadSparkleFilled: FluentIcon;
export declare const NotepadSparkleRegular: FluentIcon;
export declare const NumberCircle0Filled: FluentIcon;
export declare const NumberCircle0Regular: FluentIcon;
export declare const NumberCircle1Filled: FluentIcon;
export declare const NumberCircle1Regular: FluentIcon;
export declare const NumberCircle2Filled: FluentIcon;
export declare const NumberCircle2Regular: FluentIcon;
export declare const NumberCircle3Filled: FluentIcon;
export declare const NumberCircle3Regular: FluentIcon;
export declare const NumberCircle4Filled: FluentIcon;
export declare const NumberCircle4Regular: FluentIcon;
export declare const NumberCircle5Filled: FluentIcon;
export declare const NumberCircle5Regular: FluentIcon;
export declare const NumberCircle6Filled: FluentIcon;
export declare const NumberCircle6Regular: FluentIcon;
export declare const NumberCircle7Filled: FluentIcon;
export declare const NumberCircle7Regular: FluentIcon;
export declare const NumberCircle8Filled: FluentIcon;
export declare const NumberCircle8Regular: FluentIcon;
export declare const NumberCircle9Filled: FluentIcon;
export declare const NumberCircle9Regular: FluentIcon;
export declare const NumberRowFilled: FluentIcon;
export declare const NumberRowRegular: FluentIcon;
export declare const NumberSymbolFilled: FluentIcon;
export declare const NumberSymbolRegular: FluentIcon;
export declare const NumberSymbolDismissFilled: FluentIcon;
export declare const NumberSymbolDismissRegular: FluentIcon;
export declare const NumberSymbolSquareColor: FluentIcon;
export declare const NumberSymbolSquareFilled: FluentIcon;
export declare const NumberSymbolSquareRegular: FluentIcon;
export declare const OpenFilled: FluentIcon;
export declare const OpenRegular: FluentIcon;
export declare const OpenFolderFilled: FluentIcon;
export declare const OpenFolderRegular: FluentIcon;
export declare const OpenOffFilled: FluentIcon;
export declare const OpenOffRegular: FluentIcon;
export declare const OptionsColor: FluentIcon;
export declare const OptionsFilled: FluentIcon;
export declare const OptionsRegular: FluentIcon;
export declare const OrgColor: FluentIcon;
export declare const OrganizationFilled: FluentIcon;
export declare const OrganizationRegular: FluentIcon;
export declare const OrganizationHorizontalFilled: FluentIcon;
export declare const OrganizationHorizontalRegular: FluentIcon;
export declare const OrientationFilled: FluentIcon;
export declare const OrientationRegular: FluentIcon;
export declare const OvalFilled: FluentIcon;
export declare const OvalRegular: FluentIcon;
export declare const OvenFilled: FluentIcon;
export declare const OvenRegular: FluentIcon;
export declare const PaddingDownFilled: FluentIcon;
export declare const PaddingDownRegular: FluentIcon;
export declare const PaddingLeftFilled: FluentIcon;
export declare const PaddingLeftRegular: FluentIcon;
export declare const PaddingRightFilled: FluentIcon;
export declare const PaddingRightRegular: FluentIcon;
export declare const PaddingTopFilled: FluentIcon;
export declare const PaddingTopRegular: FluentIcon;
export declare const PageFitFilled: FluentIcon;
export declare const PageFitRegular: FluentIcon;
export declare const PaintBrushColor: FluentIcon;
export declare const PaintBrushFilled: FluentIcon;
export declare const PaintBrushRegular: FluentIcon;
export declare const PaintBrushArrowDownFilled: FluentIcon;
export declare const PaintBrushArrowDownRegular: FluentIcon;
export declare const PaintBrushArrowUpFilled: FluentIcon;
export declare const PaintBrushArrowUpRegular: FluentIcon;
export declare const PaintBrushSparkleFilled: FluentIcon;
export declare const PaintBrushSparkleRegular: FluentIcon;
export declare const PaintBrushSubtractFilled: FluentIcon;
export declare const PaintBrushSubtractRegular: FluentIcon;
export declare const PaintBucketFilled: FluentIcon;
export declare const PaintBucketRegular: FluentIcon;
export declare const PaintBucketBrushFilled: FluentIcon;
export declare const PaintBucketBrushRegular: FluentIcon;
export declare const PairFilled: FluentIcon;
export declare const PairRegular: FluentIcon;
export declare const PanelBottomFilled: FluentIcon;
export declare const PanelBottomRegular: FluentIcon;
export declare const PanelBottomContractFilled: FluentIcon;
export declare const PanelBottomContractRegular: FluentIcon;
export declare const PanelBottomExpandFilled: FluentIcon;
export declare const PanelBottomExpandRegular: FluentIcon;
export declare const PanelLeftFilled: FluentIcon;
export declare const PanelLeftRegular: FluentIcon;
export declare const PanelLeftAddFilled: FluentIcon;
export declare const PanelLeftAddRegular: FluentIcon;
export declare const PanelLeftContractFilled: FluentIcon;
export declare const PanelLeftContractRegular: FluentIcon;
export declare const PanelLeftExpandFilled: FluentIcon;
export declare const PanelLeftExpandRegular: FluentIcon;
export declare const PanelLeftFocusRightFilled: FluentIcon;
export declare const PanelLeftHeaderFilled: FluentIcon;
export declare const PanelLeftHeaderRegular: FluentIcon;
export declare const PanelLeftHeaderAddFilled: FluentIcon;
export declare const PanelLeftHeaderAddRegular: FluentIcon;
export declare const PanelLeftHeaderKeyFilled: FluentIcon;
export declare const PanelLeftHeaderKeyRegular: FluentIcon;
export declare const PanelLeftKeyFilled: FluentIcon;
export declare const PanelLeftKeyRegular: FluentIcon;
export declare const PanelLeftTextFilled: FluentIcon;
export declare const PanelLeftTextRegular: FluentIcon;
export declare const PanelLeftTextAddFilled: FluentIcon;
export declare const PanelLeftTextAddRegular: FluentIcon;
export declare const PanelLeftTextDismissFilled: FluentIcon;
export declare const PanelLeftTextDismissRegular: FluentIcon;
export declare const PanelRightFilled: FluentIcon;
export declare const PanelRightRegular: FluentIcon;
export declare const PanelRightAddFilled: FluentIcon;
export declare const PanelRightAddRegular: FluentIcon;
export declare const PanelRightContractFilled: FluentIcon;
export declare const PanelRightContractRegular: FluentIcon;
export declare const PanelRightCursorFilled: FluentIcon;
export declare const PanelRightCursorRegular: FluentIcon;
export declare const PanelRightExpandFilled: FluentIcon;
export declare const PanelRightExpandRegular: FluentIcon;
export declare const PanelRightGalleryFilled: FluentIcon;
export declare const PanelRightGalleryRegular: FluentIcon;
export declare const PanelSeparateWindowFilled: FluentIcon;
export declare const PanelSeparateWindowRegular: FluentIcon;
export declare const PanelTopContractFilled: FluentIcon;
export declare const PanelTopContractRegular: FluentIcon;
export declare const PanelTopExpandFilled: FluentIcon;
export declare const PanelTopExpandRegular: FluentIcon;
export declare const PanelTopGalleryFilled: FluentIcon;
export declare const PanelTopGalleryRegular: FluentIcon;
export declare const PasswordFilled: FluentIcon;
export declare const PasswordRegular: FluentIcon;
export declare const PasswordClockFilled: FluentIcon;
export declare const PasswordClockRegular: FluentIcon;
export declare const PatchFilled: FluentIcon;
export declare const PatchRegular: FluentIcon;
export declare const PatientColor: FluentIcon;
export declare const PatientFilled: FluentIcon;
export declare const PatientRegular: FluentIcon;
export declare const PauseFilled: FluentIcon;
export declare const PauseRegular: FluentIcon;
export declare const PauseCircleFilled: FluentIcon;
export declare const PauseCircleRegular: FluentIcon;
export declare const PauseOffFilled: FluentIcon;
export declare const PauseOffRegular: FluentIcon;
export declare const PauseSettingsFilled: FluentIcon;
export declare const PauseSettingsRegular: FluentIcon;
export declare const PawColor: FluentIcon;
export declare const PaymentFilled: FluentIcon;
export declare const PaymentRegular: FluentIcon;
export declare const PaymentWirelessFilled: FluentIcon;
export declare const PaymentWirelessRegular: FluentIcon;
export declare const PenFilled: FluentIcon;
export declare const PenRegular: FluentIcon;
export declare const PenDismissFilled: FluentIcon;
export declare const PenDismissRegular: FluentIcon;
export declare const PenOffFilled: FluentIcon;
export declare const PenOffRegular: FluentIcon;
export declare const PenProhibitedFilled: FluentIcon;
export declare const PenProhibitedRegular: FluentIcon;
export declare const PenSparkleFilled: FluentIcon;
export declare const PenSparkleRegular: FluentIcon;
export declare const PenSyncFilled: FluentIcon;
export declare const PenSyncRegular: FluentIcon;
export declare const PentagonFilled: FluentIcon;
export declare const PentagonRegular: FluentIcon;
export declare const PeopleColor: FluentIcon;
export declare const PeopleFilled: FluentIcon;
export declare const PeopleRegular: FluentIcon;
export declare const PeopleAddColor: FluentIcon;
export declare const PeopleAddFilled: FluentIcon;
export declare const PeopleAddRegular: FluentIcon;
export declare const PeopleAudienceFilled: FluentIcon;
export declare const PeopleAudienceRegular: FluentIcon;
export declare const PeopleCallFilled: FluentIcon;
export declare const PeopleCallRegular: FluentIcon;
export declare const PeopleChatColor: FluentIcon;
export declare const PeopleChatFilled: FluentIcon;
export declare const PeopleChatRegular: FluentIcon;
export declare const PeopleCheckmarkFilled: FluentIcon;
export declare const PeopleCheckmarkRegular: FluentIcon;
export declare const PeopleCommunicationFilled: FluentIcon;
export declare const PeopleCommunicationRegular: FluentIcon;
export declare const PeopleCommunityColor: FluentIcon;
export declare const PeopleCommunityFilled: FluentIcon;
export declare const PeopleCommunityRegular: FluentIcon;
export declare const PeopleCommunityAddFilled: FluentIcon;
export declare const PeopleCommunityAddRegular: FluentIcon;
export declare const PeopleEditFilled: FluentIcon;
export declare const PeopleEditRegular: FluentIcon;
export declare const PeopleErrorFilled: FluentIcon;
export declare const PeopleErrorRegular: FluentIcon;
export declare const PeopleEyeFilled: FluentIcon;
export declare const PeopleEyeRegular: FluentIcon;
export declare const PeopleHomeColor: FluentIcon;
export declare const PeopleInterwovenColor: FluentIcon;
export declare const PeopleInterwovenFilled: FluentIcon;
export declare const PeopleInterwovenRegular: FluentIcon;
export declare const PeopleLinkFilled: FluentIcon;
export declare const PeopleLinkRegular: FluentIcon;
export declare const PeopleListColor: FluentIcon;
export declare const PeopleListFilled: FluentIcon;
export declare const PeopleListRegular: FluentIcon;
export declare const PeopleLockFilled: FluentIcon;
export declare const PeopleLockRegular: FluentIcon;
export declare const PeopleMoneyFilled: FluentIcon;
export declare const PeopleMoneyRegular: FluentIcon;
export declare const PeopleProhibitedFilled: FluentIcon;
export declare const PeopleProhibitedRegular: FluentIcon;
export declare const PeopleQueueFilled: FluentIcon;
export declare const PeopleQueueRegular: FluentIcon;
export declare const PeopleSearchFilled: FluentIcon;
export declare const PeopleSearchRegular: FluentIcon;
export declare const PeopleSettingsFilled: FluentIcon;
export declare const PeopleSettingsRegular: FluentIcon;
export declare const PeopleStarFilled: FluentIcon;
export declare const PeopleStarRegular: FluentIcon;
export declare const PeopleSubtractFilled: FluentIcon;
export declare const PeopleSubtractRegular: FluentIcon;
export declare const PeopleSwapFilled: FluentIcon;
export declare const PeopleSwapRegular: FluentIcon;
export declare const PeopleSyncColor: FluentIcon;
export declare const PeopleSyncFilled: FluentIcon;
export declare const PeopleSyncRegular: FluentIcon;
export declare const PeopleTeamColor: FluentIcon;
export declare const PeopleTeamFilled: FluentIcon;
export declare const PeopleTeamRegular: FluentIcon;
export declare const PeopleTeamAddFilled: FluentIcon;
export declare const PeopleTeamAddRegular: FluentIcon;
export declare const PeopleTeamDeleteFilled: FluentIcon;
export declare const PeopleTeamDeleteRegular: FluentIcon;
export declare const PeopleTeamToolboxFilled: FluentIcon;
export declare const PeopleTeamToolboxRegular: FluentIcon;
export declare const PeopleToolboxFilled: FluentIcon;
export declare const PeopleToolboxRegular: FluentIcon;
export declare const PersonColor: FluentIcon;
export declare const PersonFilled: FluentIcon;
export declare const PersonRegular: FluentIcon;
export declare const Person5Filled: FluentIcon;
export declare const Person5Regular: FluentIcon;
export declare const Person6Filled: FluentIcon;
export declare const Person6Regular: FluentIcon;
export declare const PersonAccountsFilled: FluentIcon;
export declare const PersonAccountsRegular: FluentIcon;
export declare const PersonAddColor: FluentIcon;
export declare const PersonAddFilled: FluentIcon;
export declare const PersonAddRegular: FluentIcon;
export declare const PersonAlertFilled: FluentIcon;
export declare const PersonAlertRegular: FluentIcon;
export declare const PersonAlertOffFilled: FluentIcon;
export declare const PersonAlertOffRegular: FluentIcon;
export declare const PersonArrowBackFilled: FluentIcon;
export declare const PersonArrowBackRegular: FluentIcon;
export declare const PersonArrowLeftFilled: FluentIcon;
export declare const PersonArrowLeftRegular: FluentIcon;
export declare const PersonArrowRightFilled: FluentIcon;
export declare const PersonArrowRightRegular: FluentIcon;
export declare const PersonAvailableColor: FluentIcon;
export declare const PersonAvailableFilled: FluentIcon;
export declare const PersonAvailableRegular: FluentIcon;
export declare const PersonBoardFilled: FluentIcon;
export declare const PersonBoardRegular: FluentIcon;
export declare const PersonBoardAddFilled: FluentIcon;
export declare const PersonBoardAddRegular: FluentIcon;
export declare const PersonBriefcaseFilled: FluentIcon;
export declare const PersonBriefcaseRegular: FluentIcon;
export declare const PersonCallFilled: FluentIcon;
export declare const PersonCallRegular: FluentIcon;
export declare const PersonChatFilled: FluentIcon;
export declare const PersonChatRegular: FluentIcon;
export declare const PersonCircleFilled: FluentIcon;
export declare const PersonCircleRegular: FluentIcon;
export declare const PersonClockFilled: FluentIcon;
export declare const PersonClockRegular: FluentIcon;
export declare const PersonDeleteFilled: FluentIcon;
export declare const PersonDeleteRegular: FluentIcon;
export declare const PersonDesktopFilled: FluentIcon;
export declare const PersonDesktopRegular: FluentIcon;
export declare const PersonEditFilled: FluentIcon;
export declare const PersonEditRegular: FluentIcon;
export declare const PersonErrorFilled: FluentIcon;
export declare const PersonErrorRegular: FluentIcon;
export declare const PersonFeedbackColor: FluentIcon;
export declare const PersonFeedbackFilled: FluentIcon;
export declare const PersonFeedbackRegular: FluentIcon;
export declare const PersonGuestFilled: FluentIcon;
export declare const PersonGuestRegular: FluentIcon;
export declare const PersonHeadHintFilled: FluentIcon;
export declare const PersonHeadHintRegular: FluentIcon;
export declare const PersonHeartColor: FluentIcon;
export declare const PersonHeartFilled: FluentIcon;
export declare const PersonHeartRegular: FluentIcon;
export declare const PersonHomeFilled: FluentIcon;
export declare const PersonHomeRegular: FluentIcon;
export declare const PersonInfoFilled: FluentIcon;
export declare const PersonInfoRegular: FluentIcon;
export declare const PersonKeyColor: FluentIcon;
export declare const PersonKeyFilled: FluentIcon;
export declare const PersonKeyRegular: FluentIcon;
export declare const PersonLightbulbFilled: FluentIcon;
export declare const PersonLightbulbRegular: FluentIcon;
export declare const PersonLightningFilled: FluentIcon;
export declare const PersonLightningRegular: FluentIcon;
export declare const PersonLinkFilled: FluentIcon;
export declare const PersonLinkRegular: FluentIcon;
export declare const PersonLockFilled: FluentIcon;
export declare const PersonLockRegular: FluentIcon;
export declare const PersonMailFilled: FluentIcon;
export declare const PersonMailRegular: FluentIcon;
export declare const PersonMoneyFilled: FluentIcon;
export declare const PersonMoneyRegular: FluentIcon;
export declare const PersonNoteFilled: FluentIcon;
export declare const PersonNoteRegular: FluentIcon;
export declare const PersonPasskeyFilled: FluentIcon;
export declare const PersonPasskeyRegular: FluentIcon;
export declare const PersonPillFilled: FluentIcon;
export declare const PersonPillRegular: FluentIcon;
export declare const PersonProhibitedFilled: FluentIcon;
export declare const PersonProhibitedRegular: FluentIcon;
export declare const PersonQuestionMarkFilled: FluentIcon;
export declare const PersonQuestionMarkRegular: FluentIcon;
export declare const PersonRibbonFilled: FluentIcon;
export declare const PersonRibbonRegular: FluentIcon;
export declare const PersonRunningFilled: FluentIcon;
export declare const PersonRunningRegular: FluentIcon;
export declare const PersonSearchFilled: FluentIcon;
export declare const PersonSearchRegular: FluentIcon;
export declare const PersonSettingsFilled: FluentIcon;
export declare const PersonSettingsRegular: FluentIcon;
export declare const PersonShieldFilled: FluentIcon;
export declare const PersonShieldRegular: FluentIcon;
export declare const PersonSoundSpatialFilled: FluentIcon;
export declare const PersonSoundSpatialRegular: FluentIcon;
export declare const PersonSquareFilled: FluentIcon;
export declare const PersonSquareRegular: FluentIcon;
export declare const PersonSquareAddFilled: FluentIcon;
export declare const PersonSquareAddRegular: FluentIcon;
export declare const PersonSquareCheckmarkFilled: FluentIcon;
export declare const PersonSquareCheckmarkRegular: FluentIcon;
export declare const PersonStarFilled: FluentIcon;
export declare const PersonStarRegular: FluentIcon;
export declare const PersonStarburstColor: FluentIcon;
export declare const PersonStarburstFilled: FluentIcon;
export declare const PersonStarburstRegular: FluentIcon;
export declare const PersonSubtractFilled: FluentIcon;
export declare const PersonSubtractRegular: FluentIcon;
export declare const PersonSupportFilled: FluentIcon;
export declare const PersonSupportRegular: FluentIcon;
export declare const PersonSwapFilled: FluentIcon;
export declare const PersonSwapRegular: FluentIcon;
export declare const PersonSyncFilled: FluentIcon;
export declare const PersonSyncRegular: FluentIcon;
export declare const PersonTagFilled: FluentIcon;
export declare const PersonTagRegular: FluentIcon;
export declare const PersonTentativeColor: FluentIcon;
export declare const PersonTentativeFilled: FluentIcon;
export declare const PersonTentativeRegular: FluentIcon;
export declare const PersonVoiceFilled: FluentIcon;
export declare const PersonVoiceRegular: FluentIcon;
export declare const PersonWalkingFilled: FluentIcon;
export declare const PersonWalkingRegular: FluentIcon;
export declare const PersonWarningColor: FluentIcon;
export declare const PersonWarningFilled: FluentIcon;
export declare const PersonWarningRegular: FluentIcon;
export declare const PersonWrenchFilled: FluentIcon;
export declare const PersonWrenchRegular: FluentIcon;
export declare const PhoneColor: FluentIcon;
export declare const PhoneFilled: FluentIcon;
export declare const PhoneRegular: FluentIcon;
export declare const PhoneAddFilled: FluentIcon;
export declare const PhoneAddRegular: FluentIcon;
export declare const PhoneArrowRightFilled: FluentIcon;
export declare const PhoneArrowRightRegular: FluentIcon;
export declare const PhoneChatFilled: FluentIcon;
export declare const PhoneChatRegular: FluentIcon;
export declare const PhoneCheckmarkFilled: FluentIcon;
export declare const PhoneCheckmarkRegular: FluentIcon;
export declare const PhoneDesktopFilled: FluentIcon;
export declare const PhoneDesktopRegular: FluentIcon;
export declare const PhoneDesktopAddFilled: FluentIcon;
export declare const PhoneDesktopAddRegular: FluentIcon;
export declare const PhoneDismissFilled: FluentIcon;
export declare const PhoneDismissRegular: FluentIcon;
export declare const PhoneEditFilled: FluentIcon;
export declare const PhoneEditRegular: FluentIcon;
export declare const PhoneEraserFilled: FluentIcon;
export declare const PhoneEraserRegular: FluentIcon;
export declare const PhoneFooterArrowDownFilled: FluentIcon;
export declare const PhoneFooterArrowDownRegular: FluentIcon;
export declare const PhoneHeaderArrowUpFilled: FluentIcon;
export declare const PhoneHeaderArrowUpRegular: FluentIcon;
export declare const PhoneKeyFilled: FluentIcon;
export declare const PhoneKeyRegular: FluentIcon;
export declare const PhoneLaptopColor: FluentIcon;
export declare const PhoneLaptopFilled: FluentIcon;
export declare const PhoneLaptopRegular: FluentIcon;
export declare const PhoneLinkSetupFilled: FluentIcon;
export declare const PhoneLinkSetupRegular: FluentIcon;
export declare const PhoneLockFilled: FluentIcon;
export declare const PhoneLockRegular: FluentIcon;
export declare const PhonePageHeaderFilled: FluentIcon;
export declare const PhonePageHeaderRegular: FluentIcon;
export declare const PhonePaginationFilled: FluentIcon;
export declare const PhonePaginationRegular: FluentIcon;
export declare const PhoneScreenTimeFilled: FluentIcon;
export declare const PhoneScreenTimeRegular: FluentIcon;
export declare const PhoneShakeFilled: FluentIcon;
export declare const PhoneShakeRegular: FluentIcon;
export declare const PhoneSpanInFilled: FluentIcon;
export declare const PhoneSpanInRegular: FluentIcon;
export declare const PhoneSpanOutFilled: FluentIcon;
export declare const PhoneSpanOutRegular: FluentIcon;
export declare const PhoneSpeakerFilled: FluentIcon;
export declare const PhoneSpeakerRegular: FluentIcon;
export declare const PhoneStatusBarFilled: FluentIcon;
export declare const PhoneStatusBarRegular: FluentIcon;
export declare const PhoneTabletFilled: FluentIcon;
export declare const PhoneTabletRegular: FluentIcon;
export declare const PhoneUpdateFilled: FluentIcon;
export declare const PhoneUpdateRegular: FluentIcon;
export declare const PhoneUpdateCheckmarkFilled: FluentIcon;
export declare const PhoneUpdateCheckmarkRegular: FluentIcon;
export declare const PhoneVerticalScrollFilled: FluentIcon;
export declare const PhoneVerticalScrollRegular: FluentIcon;
export declare const PhoneVibrateFilled: FluentIcon;
export declare const PhoneVibrateRegular: FluentIcon;
export declare const PhotoFilterFilled: FluentIcon;
export declare const PhotoFilterRegular: FluentIcon;
export declare const PiFilled: FluentIcon;
export declare const PiRegular: FluentIcon;
export declare const PictureInPictureFilled: FluentIcon;
export declare const PictureInPictureRegular: FluentIcon;
export declare const PictureInPictureEnterFilled: FluentIcon;
export declare const PictureInPictureEnterRegular: FluentIcon;
export declare const PictureInPictureExitFilled: FluentIcon;
export declare const PictureInPictureExitRegular: FluentIcon;
export declare const PillFilled: FluentIcon;
export declare const PillRegular: FluentIcon;
export declare const PinColor: FluentIcon;
export declare const PinFilled: FluentIcon;
export declare const PinRegular: FluentIcon;
export declare const PinGlobeFilled: FluentIcon;
export declare const PinGlobeRegular: FluentIcon;
export declare const PinOffFilled: FluentIcon;
export declare const PinOffRegular: FluentIcon;
export declare const PipelineFilled: FluentIcon;
export declare const PipelineRegular: FluentIcon;
export declare const PipelineAddFilled: FluentIcon;
export declare const PipelineAddRegular: FluentIcon;
export declare const PipelineArrowCurveDownFilled: FluentIcon;
export declare const PipelineArrowCurveDownRegular: FluentIcon;
export declare const PipelinePlayFilled: FluentIcon;
export declare const PipelinePlayRegular: FluentIcon;
export declare const PivotFilled: FluentIcon;
export declare const PivotRegular: FluentIcon;
export declare const PlanetColor: FluentIcon;
export declare const PlanetFilled: FluentIcon;
export declare const PlanetRegular: FluentIcon;
export declare const PlantCattailFilled: FluentIcon;
export declare const PlantCattailRegular: FluentIcon;
export declare const PlantGrassFilled: FluentIcon;
export declare const PlantGrassRegular: FluentIcon;
export declare const PlantRagweedFilled: FluentIcon;
export declare const PlantRagweedRegular: FluentIcon;
export declare const PlayFilled: FluentIcon;
export declare const PlayRegular: FluentIcon;
export declare const PlayCircleFilled: FluentIcon;
export declare const PlayCircleRegular: FluentIcon;
export declare const PlayCircleHintFilled: FluentIcon;
export declare const PlayCircleHintRegular: FluentIcon;
export declare const PlayCircleHintHalfFilled: FluentIcon;
export declare const PlayCircleHintHalfRegular: FluentIcon;
export declare const PlayCircleSparkleFilled: FluentIcon;
export declare const PlayCircleSparkleRegular: FluentIcon;
export declare const PlaySettingsFilled: FluentIcon;
export declare const PlaySettingsRegular: FluentIcon;
export declare const PlayingCardsFilled: FluentIcon;
export declare const PlayingCardsRegular: FluentIcon;
export declare const PlugConnectedFilled: FluentIcon;
export declare const PlugConnectedRegular: FluentIcon;
export declare const PlugConnectedAddFilled: FluentIcon;
export declare const PlugConnectedAddRegular: FluentIcon;
export declare const PlugConnectedCheckmarkFilled: FluentIcon;
export declare const PlugConnectedCheckmarkRegular: FluentIcon;
export declare const PlugConnectedSettingsFilled: FluentIcon;
export declare const PlugConnectedSettingsRegular: FluentIcon;
export declare const PlugDisconnectedFilled: FluentIcon;
export declare const PlugDisconnectedRegular: FluentIcon;
export declare const PointScanFilled: FluentIcon;
export declare const PointScanRegular: FluentIcon;
export declare const PollColor: FluentIcon;
export declare const PollFilled: FluentIcon;
export declare const PollRegular: FluentIcon;
export declare const PollHorizontalFilled: FluentIcon;
export declare const PollHorizontalRegular: FluentIcon;
export declare const PollOffFilled: FluentIcon;
export declare const PollOffRegular: FluentIcon;
export declare const PortHdmiFilled: FluentIcon;
export declare const PortHdmiRegular: FluentIcon;
export declare const PortMicroUsbFilled: FluentIcon;
export declare const PortMicroUsbRegular: FluentIcon;
export declare const PortUsbAFilled: FluentIcon;
export declare const PortUsbARegular: FluentIcon;
export declare const PortUsbCFilled: FluentIcon;
export declare const PortUsbCRegular: FluentIcon;
export declare const PositionBackwardFilled: FluentIcon;
export declare const PositionBackwardRegular: FluentIcon;
export declare const PositionForwardFilled: FluentIcon;
export declare const PositionForwardRegular: FluentIcon;
export declare const PositionToBackFilled: FluentIcon;
export declare const PositionToBackRegular: FluentIcon;
export declare const PositionToFrontFilled: FluentIcon;
export declare const PositionToFrontRegular: FluentIcon;
export declare const PowerFilled: FluentIcon;
export declare const PowerRegular: FluentIcon;
export declare const PredictionsFilled: FluentIcon;
export declare const PredictionsRegular: FluentIcon;
export declare const PremiumColor: FluentIcon;
export declare const PremiumFilled: FluentIcon;
export declare const PremiumRegular: FluentIcon;
export declare const PremiumPersonFilled: FluentIcon;
export declare const PremiumPersonRegular: FluentIcon;
export declare const PresenceAvailableFilled: FluentIcon;
export declare const PresenceAvailableRegular: FluentIcon;
export declare const PresenceAwayFilled: FluentIcon;
export declare const PresenceAwayRegular: FluentIcon;
export declare const PresenceBlockedRegular: FluentIcon;
export declare const PresenceBusyFilled: FluentIcon;
export declare const PresenceDndFilled: FluentIcon;
export declare const PresenceDndRegular: FluentIcon;
export declare const PresenceOfflineRegular: FluentIcon;
export declare const PresenceOofRegular: FluentIcon;
export declare const PresenceTentativeRegular: FluentIcon;
export declare const PresenceUnknownRegular: FluentIcon;
export declare const PresenterFilled: FluentIcon;
export declare const PresenterRegular: FluentIcon;
export declare const PresenterOffFilled: FluentIcon;
export declare const PresenterOffRegular: FluentIcon;
export declare const PreviewLinkFilled: FluentIcon;
export declare const PreviewLinkRegular: FluentIcon;
export declare const PreviousFilled: FluentIcon;
export declare const PreviousRegular: FluentIcon;
export declare const PreviousFrameFilled: FluentIcon;
export declare const PreviousFrameRegular: FluentIcon;
export declare const PrintFilled: FluentIcon;
export declare const PrintRegular: FluentIcon;
export declare const PrintAddFilled: FluentIcon;
export declare const PrintAddRegular: FluentIcon;
export declare const ProductionFilled: FluentIcon;
export declare const ProductionRegular: FluentIcon;
export declare const ProductionCheckmarkFilled: FluentIcon;
export declare const ProductionCheckmarkRegular: FluentIcon;
export declare const ProhibitedFilled: FluentIcon;
export declare const ProhibitedRegular: FluentIcon;
export declare const ProhibitedMultipleFilled: FluentIcon;
export declare const ProhibitedMultipleRegular: FluentIcon;
export declare const ProhibitedNoteFilled: FluentIcon;
export declare const ProhibitedNoteRegular: FluentIcon;
export declare const ProjectionScreenFilled: FluentIcon;
export declare const ProjectionScreenRegular: FluentIcon;
export declare const ProjectionScreenDismissFilled: FluentIcon;
export declare const ProjectionScreenDismissRegular: FluentIcon;
export declare const ProjectionScreenTextFilled: FluentIcon;
export declare const ProjectionScreenTextRegular: FluentIcon;
export declare const ProjectionScreenTextSparkleFilled: FluentIcon;
export declare const ProjectionScreenTextSparkleRegular: FluentIcon;
export declare const PromptFilled: FluentIcon;
export declare const PromptRegular: FluentIcon;
export declare const ProtocolHandlerFilled: FluentIcon;
export declare const ProtocolHandlerRegular: FluentIcon;
export declare const PulseFilled: FluentIcon;
export declare const PulseRegular: FluentIcon;
export declare const PulseSquareFilled: FluentIcon;
export declare const PulseSquareRegular: FluentIcon;
export declare const PuzzleCubeFilled: FluentIcon;
export declare const PuzzleCubeRegular: FluentIcon;
export declare const PuzzleCubePieceFilled: FluentIcon;
export declare const PuzzleCubePieceRegular: FluentIcon;
export declare const PuzzlePieceColor: FluentIcon;
export declare const PuzzlePieceFilled: FluentIcon;
export declare const PuzzlePieceRegular: FluentIcon;
export declare const PuzzlePieceShieldFilled: FluentIcon;
export declare const PuzzlePieceShieldRegular: FluentIcon;
export declare const QrCodeFilled: FluentIcon;
export declare const QrCodeRegular: FluentIcon;
export declare const QuestionFilled: FluentIcon;
export declare const QuestionRegular: FluentIcon;
export declare const QuestionCircleColor: FluentIcon;
export declare const QuestionCircleFilled: FluentIcon;
export declare const QuestionCircleRegular: FluentIcon;
export declare const QuizNewFilled: FluentIcon;
export declare const QuizNewRegular: FluentIcon;
export declare const RadarFilled: FluentIcon;
export declare const RadarRegular: FluentIcon;
export declare const RadarCheckmarkFilled: FluentIcon;
export declare const RadarCheckmarkRegular: FluentIcon;
export declare const RadarRectangleMultipleFilled: FluentIcon;
export declare const RadarRectangleMultipleRegular: FluentIcon;
export declare const RadioButtonFilled: FluentIcon;
export declare const RadioButtonRegular: FluentIcon;
export declare const RamFilled: FluentIcon;
export declare const RamRegular: FluentIcon;
export declare const RatingMatureFilled: FluentIcon;
export declare const RatingMatureRegular: FluentIcon;
export declare const RatioOneToOneFilled: FluentIcon;
export declare const RatioOneToOneRegular: FluentIcon;
export declare const ReOrderFilled: FluentIcon;
export declare const ReOrderRegular: FluentIcon;
export declare const ReOrderDotsHorizontalFilled: FluentIcon;
export declare const ReOrderDotsHorizontalRegular: FluentIcon;
export declare const ReOrderDotsVerticalFilled: FluentIcon;
export declare const ReOrderDotsVerticalRegular: FluentIcon;
export declare const ReOrderVerticalFilled: FluentIcon;
export declare const ReOrderVerticalRegular: FluentIcon;
export declare const ReadAloudFilled: FluentIcon;
export declare const ReadAloudRegular: FluentIcon;
export declare const ReadingListFilled: FluentIcon;
export declare const ReadingListRegular: FluentIcon;
export declare const ReadingListAddFilled: FluentIcon;
export declare const ReadingListAddRegular: FluentIcon;
export declare const ReadingModeMobileFilled: FluentIcon;
export declare const ReadingModeMobileRegular: FluentIcon;
export declare const RealEstateFilled: FluentIcon;
export declare const RealEstateRegular: FluentIcon;
export declare const ReceiptColor: FluentIcon;
export declare const ReceiptFilled: FluentIcon;
export declare const ReceiptRegular: FluentIcon;
export declare const ReceiptAddFilled: FluentIcon;
export declare const ReceiptAddRegular: FluentIcon;
export declare const ReceiptBagFilled: FluentIcon;
export declare const ReceiptBagRegular: FluentIcon;
export declare const ReceiptCubeFilled: FluentIcon;
export declare const ReceiptCubeRegular: FluentIcon;
export declare const ReceiptMoneyFilled: FluentIcon;
export declare const ReceiptMoneyRegular: FluentIcon;
export declare const ReceiptPlayFilled: FluentIcon;
export declare const ReceiptPlayRegular: FluentIcon;
export declare const ReceiptSearchFilled: FluentIcon;
export declare const ReceiptSearchRegular: FluentIcon;
export declare const ReceiptSparklesFilled: FluentIcon;
export declare const ReceiptSparklesRegular: FluentIcon;
export declare const RecordFilled: FluentIcon;
export declare const RecordRegular: FluentIcon;
export declare const RecordStopFilled: FluentIcon;
export declare const RecordStopRegular: FluentIcon;
export declare const RectangleLandscapeFilled: FluentIcon;
export declare const RectangleLandscapeRegular: FluentIcon;
export declare const RectangleLandscapeHintCopyFilled: FluentIcon;
export declare const RectangleLandscapeHintCopyRegular: FluentIcon;
export declare const RectangleLandscapeSparkleFilled: FluentIcon;
export declare const RectangleLandscapeSparkleRegular: FluentIcon;
export declare const RectangleLandscapeSyncFilled: FluentIcon;
export declare const RectangleLandscapeSyncRegular: FluentIcon;
export declare const RectangleLandscapeSyncOffFilled: FluentIcon;
export declare const RectangleLandscapeSyncOffRegular: FluentIcon;
export declare const RectanglePortraitFilled: FluentIcon;
export declare const RectanglePortraitRegular: FluentIcon;
export declare const RectanglePortraitLocationTargetFilled: FluentIcon;
export declare const RectanglePortraitLocationTargetRegular: FluentIcon;
export declare const RecycleFilled: FluentIcon;
export declare const RecycleRegular: FluentIcon;
export declare const RemixAddFilled: FluentIcon;
export declare const RemixAddRegular: FluentIcon;
export declare const RemoteFilled: FluentIcon;
export declare const RemoteRegular: FluentIcon;
export declare const RenameFilled: FluentIcon;
export declare const RenameRegular: FluentIcon;
export declare const RenameAFilled: FluentIcon;
export declare const RenameARegular: FluentIcon;
export declare const ReorderFilled: FluentIcon;
export declare const ReorderRegular: FluentIcon;
export declare const ReplayFilled: FluentIcon;
export declare const ReplayRegular: FluentIcon;
export declare const ResizeFilled: FluentIcon;
export declare const ResizeRegular: FluentIcon;
export declare const ResizeImageFilled: FluentIcon;
export declare const ResizeImageRegular: FluentIcon;
export declare const ResizeLargeFilled: FluentIcon;
export declare const ResizeLargeRegular: FluentIcon;
export declare const ResizeSmallFilled: FluentIcon;
export declare const ResizeSmallRegular: FluentIcon;
export declare const ResizeTableFilled: FluentIcon;
export declare const ResizeTableRegular: FluentIcon;
export declare const ResizeVideoFilled: FluentIcon;
export declare const ResizeVideoRegular: FluentIcon;
export declare const RewardColor: FluentIcon;
export declare const RewardFilled: FluentIcon;
export declare const RewardRegular: FluentIcon;
export declare const RewindFilled: FluentIcon;
export declare const RewindRegular: FluentIcon;
export declare const RhombusFilled: FluentIcon;
export declare const RhombusRegular: FluentIcon;
export declare const RibbonColor: FluentIcon;
export declare const RibbonFilled: FluentIcon;
export declare const RibbonRegular: FluentIcon;
export declare const RibbonAddFilled: FluentIcon;
export declare const RibbonAddRegular: FluentIcon;
export declare const RibbonOffFilled: FluentIcon;
export declare const RibbonOffRegular: FluentIcon;
export declare const RibbonStarColor: FluentIcon;
export declare const RibbonStarFilled: FluentIcon;
export declare const RibbonStarRegular: FluentIcon;
export declare const RoadFilled: FluentIcon;
export declare const RoadRegular: FluentIcon;
export declare const RoadConeFilled: FluentIcon;
export declare const RoadConeRegular: FluentIcon;
export declare const RocketFilled: FluentIcon;
export declare const RocketRegular: FluentIcon;
export declare const RotateLeftFilled: FluentIcon;
export declare const RotateLeftRegular: FluentIcon;
export declare const RotateRightFilled: FluentIcon;
export declare const RotateRightRegular: FluentIcon;
export declare const RouterFilled: FluentIcon;
export declare const RouterRegular: FluentIcon;
export declare const RowChildFilled: FluentIcon;
export declare const RowChildRegular: FluentIcon;
export declare const RowTripleFilled: FluentIcon;
export declare const RowTripleRegular: FluentIcon;
export declare const RssFilled: FluentIcon;
export declare const RssRegular: FluentIcon;
export declare const RulerFilled: FluentIcon;
export declare const RulerRegular: FluentIcon;
export declare const RunFilled: FluentIcon;
export declare const RunRegular: FluentIcon;
export declare const SanitizeFilled: FluentIcon;
export declare const SanitizeRegular: FluentIcon;
export declare const SaveFilled: FluentIcon;
export declare const SaveRegular: FluentIcon;
export declare const SaveArrowRightFilled: FluentIcon;
export declare const SaveArrowRightRegular: FluentIcon;
export declare const SaveCopyFilled: FluentIcon;
export declare const SaveCopyRegular: FluentIcon;
export declare const SaveEditFilled: FluentIcon;
export declare const SaveEditRegular: FluentIcon;
export declare const SaveImageFilled: FluentIcon;
export declare const SaveImageRegular: FluentIcon;
export declare const SaveMultipleFilled: FluentIcon;
export declare const SaveMultipleRegular: FluentIcon;
export declare const SaveSearchFilled: FluentIcon;
export declare const SaveSearchRegular: FluentIcon;
export declare const SaveSyncFilled: FluentIcon;
export declare const SaveSyncRegular: FluentIcon;
export declare const SavingsColor: FluentIcon;
export declare const SavingsFilled: FluentIcon;
export declare const SavingsRegular: FluentIcon;
export declare const ScaleFillFilled: FluentIcon;
export declare const ScaleFillRegular: FluentIcon;
export declare const ScaleFitFilled: FluentIcon;
export declare const ScaleFitRegular: FluentIcon;
export declare const ScalesFilled: FluentIcon;
export declare const ScalesRegular: FluentIcon;
export declare const ScanFilled: FluentIcon;
export declare const ScanRegular: FluentIcon;
export declare const ScanCameraFilled: FluentIcon;
export declare const ScanCameraRegular: FluentIcon;
export declare const ScanDashFilled: FluentIcon;
export declare const ScanDashRegular: FluentIcon;
export declare const ScanObjectFilled: FluentIcon;
export declare const ScanObjectRegular: FluentIcon;
export declare const ScanPersonColor: FluentIcon;
export declare const ScanPersonFilled: FluentIcon;
export declare const ScanPersonRegular: FluentIcon;
export declare const ScanTableFilled: FluentIcon;
export declare const ScanTableRegular: FluentIcon;
export declare const ScanTextFilled: FluentIcon;
export declare const ScanTextRegular: FluentIcon;
