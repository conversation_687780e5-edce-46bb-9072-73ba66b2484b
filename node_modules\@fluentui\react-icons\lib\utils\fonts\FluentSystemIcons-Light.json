{"AccessibilityCheckmark32Light": 57344, "Add32Light": 57345, "Alert32Light": 57346, "AppFolder32Light": 57347, "AppGeneric32Light": 57348, "Archive32Light": 57349, "ArchiveSettings32Light": 57350, "ArrowClockwise32Light": 57351, "ArrowDown32Light": 57352, "ArrowDownload32Light": 57353, "ArrowForward32Light": 57354, "ArrowHookDownLeft32Light": 57355, "ArrowHookDownRight32Light": 57356, "ArrowHookUpLeft32Light": 57357, "ArrowHookUpRight32Light": 57358, "ArrowRedo32Light": 57359, "ArrowReply32Light": 57360, "ArrowReplyAll32Light": 57361, "ArrowUndo32Light": 57362, "Attach32Light": 57363, "AutoFit32Light": 57364, "AutoFitWidth32Light": 57365, "Autocorrect32Light": 57366, "BookContacts32Light": 57367, "BreakoutRoom32Light": 57368, "Broom32Light": 57369, "Calendar3Day32Light": 57370, "CalendarClock32Light": 57371, "CalendarDataBar32Light": 57372, "CalendarDay32Light": 57373, "CalendarEdit32Light": 57374, "CalendarEmpty32Light": 57375, "CalendarLtr32Light": 57376, "CalendarMonth32Light": 57377, "CalendarMultiple32Light": 57378, "CalendarPattern32Light": 57379, "CalendarReply32Light": 57380, "CalendarSparkle32Light": 57381, "CalendarTodo32Light": 57382, "CalendarWorkWeek32Light": 57383, "Chat32Light": 57384, "Checkmark32Light": 57385, "CheckmarkCircle32Light": 57386, "Classification32Light": 57387, "ClipboardPaste32Light": 57388, "Clock32Light": 57389, "ClockAlarm32Light": 57390, "Color32Light": 57391, "ColorFill32Light": 57392, "ColorFillAccent32Light": 57393, "Comment32Light": 57394, "CommentAdd32Light": 57395, "Compose32Light": 57396, "Copy32Light": 57397, "Crop32Light": 57398, "Cursor32Light": 57399, "Cut32Light": 57400, "Delete32Light": 57401, "Dismiss32Light": 57402, "DismissCircle32Light": 57403, "Document24Light": 57404, "Document28Light": 57405, "Document32Light": 57406, "Document48Light": 57407, "DocumentLightning32Light": 57408, "DocumentSignature32Light": 57409, "DocumentSparkle24Light": 57410, "DocumentSparkle28Light": 57411, "DocumentSparkle32Light": 57412, "DocumentSparkle48Light": 57413, "DoorArrowRight32Light": 57414, "Edit32Light": 57415, "Emoji32Light": 57416, "Eye32Light": 57417, "EyeOff32Light": 57418, "Filter32Light": 57419, "Flag32Light": 57420, "FlagOff32Light": 57421, "Flash32Light": 57422, "FolderArrowRight32Light": 57423, "FolderMail32Light": 57424, "HandDraw32Light": 57425, "History32Light": 57426, "ImageAdd32Light": 57427, "ImageAltText32Light": 57428, "ImageCopy32Light": 57429, "ImageReflection32Light": 57430, "ImageShadow32Light": 57431, "ImmersiveReader32Light": 57432, "Important32Light": 57433, "Lasso32Light": 57434, "LayoutColumnTwo32Light": 57435, "LayoutColumnTwoFocusLeft32Light": 57436, "LayoutColumnTwoFocusRight32Light": 57437, "LayoutRowTwo32Light": 57438, "LayoutRowTwoFocusTop32Light": 57439, "LayoutRowTwoFocusTopSettings32Light": 57440, "LayoutRowTwoSettings32Light": 57441, "Lightbulb32Light": 57442, "Link32Light": 57443, "LockClosed32Light": 57444, "LockOpen32Light": 57445, "Mail32Light": 57446, "MailAlert32Light": 57447, "MailArrowClockwise32Light": 57448, "MailArrowDoubleBack32Light": 57449, "MailCopy32Light": 57450, "MailEdit32Light": 57451, "MailList32Light": 57452, "MailMultiple32Light": 57453, "MailRead32Light": 57454, "MailReadMultiple32Light": 57455, "MailRewind32Light": 57456, "MailSettings32Light": 57457, "MailTemplate32Light": 57458, "MailUnread32Light": 57459, "Mic32Light": 57460, "Molecule32Light": 57461, "Note32Light": 57462, "Options32Light": 57463, "PaintBrush32Light": 57464, "PanelLeftDefault32Light": 57465, "PanelLeftFocusRight32Light": 57466, "PenSparkle32Light": 57467, "People32Light": 57468, "PeopleAdd32Light": 57469, "PeopleCheckmark32Light": 57470, "PeopleCommunity32Light": 57471, "PeopleEdit32Light": 57472, "PeopleList32Light": 57473, "PeopleSettings32Light": 57474, "PeopleSync32Light": 57475, "Person32Light": 57476, "PersonAdd32Light": 57477, "PersonAvailable32Light": 57478, "PersonFeedback32Light": 57479, "PersonMail32Light": 57480, "PersonProhibited32Light": 57481, "PersonSuport32Light": 57482, "Phone32Light": 57483, "PictureInPicture32Light": 57484, "Pin32Light": 57485, "PinOff32Light": 57486, "Poll32Light": 57487, "Print32Light": 57488, "Question32Light": 57489, "ReadAloud32Light": 57490, "RectangleLandscape32Light": 57491, "RotateLeft32Light": 57492, "Save32Light": 57493, "SendClock32Light": 57494, "Settings32Light": 57495, "Share32Light": 57496, "ShieldError32Light": 57497, "Signature32Light": 57498, "SpeakerMute32Light": 57499, "SquareArrowForward32Light": 57500, "Stamp32Light": 57501, "StarAdd32Light": 57502, "StarArrowRight32Light": 57503, "Sticker32Light": 57504, "TabAdd32Light": 57505, "Table32Light": 57506, "TableAltText32Light": 57507, "TableCellsMerge32Light": 57508, "TableCellsSplit32Light": 57509, "TableDismiss32Light": 57510, "TableMoveAbove32Light": 57511, "TableMoveBelow32Light": 57512, "TableMoveLeft32Light": 57513, "TableMoveRight32Light": 57514, "TableSettings32Light": 57515, "TableSimple32Light": 57516, "Tag32Light": 57517, "Text32Light": 57518, "TextClearFormatting32Light": 57519, "TextCollapse32Light": 57520, "TextDensity32Light": 57521, "TextEditStyle32Light": 57522, "TextExpand32Light": 57523, "TextboxAlignTopLeft32Light": 57524, "Toolbox32Light": 57525, "Translate32Light": 57526, "Video32Light": 57527, "VideoClip32Light": 57528, "WeatherMoon32Light": 57529, "WeatherSunny32Light": 57530, "Window32Light": 57531, "WrenchScrewdriver32Light": 57532, "ZoomIn32Light": 57533, "ZoomOut32Light": 57534}