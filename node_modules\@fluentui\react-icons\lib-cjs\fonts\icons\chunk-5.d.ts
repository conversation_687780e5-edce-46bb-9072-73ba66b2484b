import type { FluentFontIcon } from "../../utils/fonts/createFluentFontIcon";
export declare const ImageRegular: FluentFontIcon;
export declare const ImageAddFilled: FluentFontIcon;
export declare const ImageAddRegular: FluentFontIcon;
export declare const ImageAltTextFilled: FluentFontIcon;
export declare const ImageAltTextRegular: FluentFontIcon;
export declare const ImageArrowBackFilled: FluentFontIcon;
export declare const ImageArrowBackRegular: FluentFontIcon;
export declare const ImageArrowCounterclockwiseFilled: FluentFontIcon;
export declare const ImageArrowCounterclockwiseRegular: FluentFontIcon;
export declare const ImageArrowForwardFilled: FluentFontIcon;
export declare const ImageArrowForwardRegular: FluentFontIcon;
export declare const ImageBorderFilled: FluentFontIcon;
export declare const ImageBorderRegular: FluentFontIcon;
export declare const ImageCircleFilled: FluentFontIcon;
export declare const ImageCircleRegular: FluentFontIcon;
export declare const ImageCopyFilled: FluentFontIcon;
export declare const ImageCopyRegular: FluentFontIcon;
export declare const ImageEditFilled: FluentFontIcon;
export declare const ImageEditRegular: FluentFontIcon;
export declare const ImageGlobeFilled: FluentFontIcon;
export declare const ImageGlobeRegular: FluentFontIcon;
export declare const ImageMultipleFilled: FluentFontIcon;
export declare const ImageMultipleRegular: FluentFontIcon;
export declare const ImageMultipleOffFilled: FluentFontIcon;
export declare const ImageMultipleOffRegular: FluentFontIcon;
export declare const ImageOffFilled: FluentFontIcon;
export declare const ImageOffRegular: FluentFontIcon;
export declare const ImageProhibitedFilled: FluentFontIcon;
export declare const ImageProhibitedRegular: FluentFontIcon;
export declare const ImageReflectionFilled: FluentFontIcon;
export declare const ImageReflectionRegular: FluentFontIcon;
export declare const ImageSearchFilled: FluentFontIcon;
export declare const ImageSearchRegular: FluentFontIcon;
export declare const ImageShadowFilled: FluentFontIcon;
export declare const ImageShadowRegular: FluentFontIcon;
export declare const ImageSparkleFilled: FluentFontIcon;
export declare const ImageSparkleRegular: FluentFontIcon;
export declare const ImageSplitFilled: FluentFontIcon;
export declare const ImageSplitRegular: FluentFontIcon;
export declare const ImageStackFilled: FluentFontIcon;
export declare const ImageStackRegular: FluentFontIcon;
export declare const ImageTableFilled: FluentFontIcon;
export declare const ImageTableRegular: FluentFontIcon;
export declare const ImmersiveReaderFilled: FluentFontIcon;
export declare const ImmersiveReaderRegular: FluentFontIcon;
export declare const ImportantFilled: FluentFontIcon;
export declare const ImportantRegular: FluentFontIcon;
export declare const IncognitoFilled: FluentFontIcon;
export declare const IncognitoRegular: FluentFontIcon;
export declare const InfoFilled: FluentFontIcon;
export declare const InfoRegular: FluentFontIcon;
export declare const InfoShieldFilled: FluentFontIcon;
export declare const InfoShieldRegular: FluentFontIcon;
export declare const InfoSparkleFilled: FluentFontIcon;
export declare const InfoSparkleRegular: FluentFontIcon;
export declare const InkStrokeFilled: FluentFontIcon;
export declare const InkStrokeRegular: FluentFontIcon;
export declare const InkStrokeArrowDownFilled: FluentFontIcon;
export declare const InkStrokeArrowDownRegular: FluentFontIcon;
export declare const InkStrokeArrowUpDownFilled: FluentFontIcon;
export declare const InkStrokeArrowUpDownRegular: FluentFontIcon;
export declare const InkingToolFilled: FluentFontIcon;
export declare const InkingToolRegular: FluentFontIcon;
export declare const InkingToolAccentFilled: FluentFontIcon;
export declare const InprivateAccountFilled: FluentFontIcon;
export declare const InprivateAccountRegular: FluentFontIcon;
export declare const InsertFilled: FluentFontIcon;
export declare const InsertRegular: FluentFontIcon;
export declare const IosChevronRightFilled: FluentFontIcon;
export declare const IosChevronRightRegular: FluentFontIcon;
export declare const IotFilled: FluentFontIcon;
export declare const IotRegular: FluentFontIcon;
export declare const IotAlertFilled: FluentFontIcon;
export declare const IotAlertRegular: FluentFontIcon;
export declare const JavascriptFilled: FluentFontIcon;
export declare const JavascriptRegular: FluentFontIcon;
export declare const JoystickFilled: FluentFontIcon;
export declare const JoystickRegular: FluentFontIcon;
export declare const KeyFilled: FluentFontIcon;
export declare const KeyRegular: FluentFontIcon;
export declare const KeyCommandFilled: FluentFontIcon;
export declare const KeyCommandRegular: FluentFontIcon;
export declare const KeyMultipleFilled: FluentFontIcon;
export declare const KeyMultipleRegular: FluentFontIcon;
export declare const KeyResetFilled: FluentFontIcon;
export declare const KeyResetRegular: FluentFontIcon;
export declare const Keyboard123Filled: FluentFontIcon;
export declare const Keyboard123Regular: FluentFontIcon;
export declare const KeyboardFilled: FluentFontIcon;
export declare const KeyboardRegular: FluentFontIcon;
export declare const KeyboardDockFilled: FluentFontIcon;
export declare const KeyboardDockRegular: FluentFontIcon;
export declare const KeyboardLayoutFloatFilled: FluentFontIcon;
export declare const KeyboardLayoutFloatRegular: FluentFontIcon;
export declare const KeyboardLayoutOneHandedLeftFilled: FluentFontIcon;
export declare const KeyboardLayoutOneHandedLeftRegular: FluentFontIcon;
export declare const KeyboardLayoutResizeFilled: FluentFontIcon;
export declare const KeyboardLayoutResizeRegular: FluentFontIcon;
export declare const KeyboardLayoutSplitFilled: FluentFontIcon;
export declare const KeyboardLayoutSplitRegular: FluentFontIcon;
export declare const KeyboardShiftFilled: FluentFontIcon;
export declare const KeyboardShiftRegular: FluentFontIcon;
export declare const KeyboardShiftUppercaseFilled: FluentFontIcon;
export declare const KeyboardShiftUppercaseRegular: FluentFontIcon;
export declare const KeyboardTabFilled: FluentFontIcon;
export declare const KeyboardTabRegular: FluentFontIcon;
export declare const LaptopFilled: FluentFontIcon;
export declare const LaptopRegular: FluentFontIcon;
export declare const LaptopBriefcaseFilled: FluentFontIcon;
export declare const LaptopBriefcaseRegular: FluentFontIcon;
export declare const LaptopDismissFilled: FluentFontIcon;
export declare const LaptopDismissRegular: FluentFontIcon;
export declare const LaptopPersonFilled: FluentFontIcon;
export declare const LaptopPersonRegular: FluentFontIcon;
export declare const LaptopSettingsFilled: FluentFontIcon;
export declare const LaptopSettingsRegular: FluentFontIcon;
export declare const LaptopShieldFilled: FluentFontIcon;
export declare const LaptopShieldRegular: FluentFontIcon;
export declare const LaserToolFilled: FluentFontIcon;
export declare const LaserToolRegular: FluentFontIcon;
export declare const LassoFilled: FluentFontIcon;
export declare const LassoRegular: FluentFontIcon;
export declare const LauncherSettingsFilled: FluentFontIcon;
export declare const LauncherSettingsRegular: FluentFontIcon;
export declare const LayerFilled: FluentFontIcon;
export declare const LayerRegular: FluentFontIcon;
export declare const LayerDiagonalFilled: FluentFontIcon;
export declare const LayerDiagonalRegular: FluentFontIcon;
export declare const LayerDiagonalAddFilled: FluentFontIcon;
export declare const LayerDiagonalAddRegular: FluentFontIcon;
export declare const LayerDiagonalPersonFilled: FluentFontIcon;
export declare const LayerDiagonalPersonRegular: FluentFontIcon;
export declare const LayerDiagonalSparkleFilled: FluentFontIcon;
export declare const LayerDiagonalSparkleRegular: FluentFontIcon;
export declare const LayoutCellFourFilled: FluentFontIcon;
export declare const LayoutCellFourRegular: FluentFontIcon;
export declare const LayoutCellFourFocusBottomLeftFilled: FluentFontIcon;
export declare const LayoutCellFourFocusBottomRightFilled: FluentFontIcon;
export declare const LayoutCellFourFocusTopLeftFilled: FluentFontIcon;
export declare const LayoutCellFourFocusTopRightFilled: FluentFontIcon;
export declare const LayoutColumnFourFilled: FluentFontIcon;
export declare const LayoutColumnFourRegular: FluentFontIcon;
export declare const LayoutColumnFourFocusCenterLeftFilled: FluentFontIcon;
export declare const LayoutColumnFourFocusCenterRightFilled: FluentFontIcon;
export declare const LayoutColumnFourFocusLeftFilled: FluentFontIcon;
export declare const LayoutColumnFourFocusRightFilled: FluentFontIcon;
export declare const LayoutColumnOneThirdLeftFilled: FluentFontIcon;
export declare const LayoutColumnOneThirdLeftRegular: FluentFontIcon;
export declare const LayoutColumnOneThirdRightFilled: FluentFontIcon;
export declare const LayoutColumnOneThirdRightRegular: FluentFontIcon;
export declare const LayoutColumnOneThirdRightHintFilled: FluentFontIcon;
export declare const LayoutColumnOneThirdRightHintRegular: FluentFontIcon;
export declare const LayoutColumnThreeFilled: FluentFontIcon;
export declare const LayoutColumnThreeRegular: FluentFontIcon;
export declare const LayoutColumnThreeFocusCenterFilled: FluentFontIcon;
export declare const LayoutColumnThreeFocusLeftFilled: FluentFontIcon;
export declare const LayoutColumnThreeFocusRightFilled: FluentFontIcon;
export declare const LayoutColumnTwoFilled: FluentFontIcon;
export declare const LayoutColumnTwoRegular: FluentFontIcon;
export declare const LayoutColumnTwoFocusLeftFilled: FluentFontIcon;
export declare const LayoutColumnTwoFocusRightFilled: FluentFontIcon;
export declare const LayoutColumnTwoSplitLeftFilled: FluentFontIcon;
export declare const LayoutColumnTwoSplitLeftRegular: FluentFontIcon;
export declare const LayoutColumnTwoSplitLeftFocusBottomLeftFilled: FluentFontIcon;
export declare const LayoutColumnTwoSplitLeftFocusRightFilled: FluentFontIcon;
export declare const LayoutColumnTwoSplitLeftFocusTopLeftFilled: FluentFontIcon;
export declare const LayoutColumnTwoSplitRightFilled: FluentFontIcon;
export declare const LayoutColumnTwoSplitRightRegular: FluentFontIcon;
export declare const LayoutColumnTwoSplitRightFocusBottomRightFilled: FluentFontIcon;
export declare const LayoutColumnTwoSplitRightFocusLeftFilled: FluentFontIcon;
export declare const LayoutColumnTwoSplitRightFocusTopRightFilled: FluentFontIcon;
export declare const LayoutDynamicFilled: FluentFontIcon;
export declare const LayoutDynamicRegular: FluentFontIcon;
export declare const LayoutRowFourFilled: FluentFontIcon;
export declare const LayoutRowFourRegular: FluentFontIcon;
export declare const LayoutRowFourFocusBottomFilled: FluentFontIcon;
export declare const LayoutRowFourFocusCenterBottomFilled: FluentFontIcon;
export declare const LayoutRowFourFocusCenterTopFilled: FluentFontIcon;
export declare const LayoutRowFourFocusTopFilled: FluentFontIcon;
export declare const LayoutRowThreeFilled: FluentFontIcon;
export declare const LayoutRowThreeRegular: FluentFontIcon;
export declare const LayoutRowThreeFocusBottomFilled: FluentFontIcon;
export declare const LayoutRowThreeFocusCenterFilled: FluentFontIcon;
export declare const LayoutRowThreeFocusTopFilled: FluentFontIcon;
export declare const LayoutRowTwoFilled: FluentFontIcon;
export declare const LayoutRowTwoRegular: FluentFontIcon;
export declare const LayoutRowTwoFocusBottomFilled: FluentFontIcon;
export declare const LayoutRowTwoFocusTopFilled: FluentFontIcon;
export declare const LayoutRowTwoFocusTopSettingsFilled: FluentFontIcon;
export declare const LayoutRowTwoSettingsFilled: FluentFontIcon;
export declare const LayoutRowTwoSettingsRegular: FluentFontIcon;
export declare const LayoutRowTwoSplitBottomFilled: FluentFontIcon;
export declare const LayoutRowTwoSplitBottomRegular: FluentFontIcon;
export declare const LayoutRowTwoSplitBottomFocusBottomLeftFilled: FluentFontIcon;
export declare const LayoutRowTwoSplitBottomFocusBottomRightFilled: FluentFontIcon;
export declare const LayoutRowTwoSplitBottomFocusTopFilled: FluentFontIcon;
export declare const LayoutRowTwoSplitTopFilled: FluentFontIcon;
export declare const LayoutRowTwoSplitTopRegular: FluentFontIcon;
export declare const LayoutRowTwoSplitTopFocusBottomFilled: FluentFontIcon;
export declare const LayoutRowTwoSplitTopFocusTopLeftFilled: FluentFontIcon;
export declare const LayoutRowTwoSplitTopFocusTopRightFilled: FluentFontIcon;
export declare const LeafOneFilled: FluentFontIcon;
export declare const LeafOneRegular: FluentFontIcon;
export declare const LeafThreeFilled: FluentFontIcon;
export declare const LeafThreeRegular: FluentFontIcon;
export declare const LeafTwoFilled: FluentFontIcon;
export declare const LeafTwoRegular: FluentFontIcon;
export declare const LearningAppFilled: FluentFontIcon;
export declare const LearningAppRegular: FluentFontIcon;
export declare const LibraryFilled: FluentFontIcon;
export declare const LibraryRegular: FluentFontIcon;
export declare const LightbulbFilled: FluentFontIcon;
export declare const LightbulbRegular: FluentFontIcon;
export declare const LightbulbCheckmarkFilled: FluentFontIcon;
export declare const LightbulbCheckmarkRegular: FluentFontIcon;
export declare const LightbulbCircleFilled: FluentFontIcon;
export declare const LightbulbCircleRegular: FluentFontIcon;
export declare const LightbulbFilamentFilled: FluentFontIcon;
export declare const LightbulbFilamentRegular: FluentFontIcon;
export declare const LightbulbPersonFilled: FluentFontIcon;
export declare const LightbulbPersonRegular: FluentFontIcon;
export declare const LikertFilled: FluentFontIcon;
export declare const LikertRegular: FluentFontIcon;
export declare const LineFilled: FluentFontIcon;
export declare const LineRegular: FluentFontIcon;
export declare const LineDashesFilled: FluentFontIcon;
export declare const LineDashesRegular: FluentFontIcon;
export declare const LineFlowDiagonalUpRightFilled: FluentFontIcon;
export declare const LineFlowDiagonalUpRightRegular: FluentFontIcon;
export declare const LineHorizontal1Filled: FluentFontIcon;
export declare const LineHorizontal1Regular: FluentFontIcon;
export declare const LineHorizontal1DashDotDashFilled: FluentFontIcon;
export declare const LineHorizontal1DashDotDashRegular: FluentFontIcon;
export declare const LineHorizontal1DashesFilled: FluentFontIcon;
export declare const LineHorizontal1DashesRegular: FluentFontIcon;
export declare const LineHorizontal1DotFilled: FluentFontIcon;
export declare const LineHorizontal1DotRegular: FluentFontIcon;
export declare const LineHorizontal2DashesSolidFilled: FluentFontIcon;
export declare const LineHorizontal2DashesSolidRegular: FluentFontIcon;
export declare const LineHorizontal3Filled: FluentFontIcon;
export declare const LineHorizontal3Regular: FluentFontIcon;
export declare const LineHorizontal4Filled: FluentFontIcon;
export declare const LineHorizontal4Regular: FluentFontIcon;
export declare const LineHorizontal4SearchFilled: FluentFontIcon;
export declare const LineHorizontal4SearchRegular: FluentFontIcon;
export declare const LineHorizontal5Filled: FluentFontIcon;
export declare const LineHorizontal5Regular: FluentFontIcon;
export declare const LineHorizontal5ErrorFilled: FluentFontIcon;
export declare const LineHorizontal5ErrorRegular: FluentFontIcon;
export declare const LineStyleFilled: FluentFontIcon;
export declare const LineStyleRegular: FluentFontIcon;
export declare const LineStyleSketchFilled: FluentFontIcon;
export declare const LineStyleSketchRegular: FluentFontIcon;
export declare const LineThicknessFilled: FluentFontIcon;
export declare const LineThicknessRegular: FluentFontIcon;
export declare const LinkFilled: FluentFontIcon;
export declare const LinkRegular: FluentFontIcon;
export declare const LinkAddFilled: FluentFontIcon;
export declare const LinkAddRegular: FluentFontIcon;
export declare const LinkDismissFilled: FluentFontIcon;
export declare const LinkDismissRegular: FluentFontIcon;
export declare const LinkEditFilled: FluentFontIcon;
export declare const LinkEditRegular: FluentFontIcon;
export declare const LinkMultipleFilled: FluentFontIcon;
export declare const LinkMultipleRegular: FluentFontIcon;
export declare const LinkPersonFilled: FluentFontIcon;
export declare const LinkPersonRegular: FluentFontIcon;
export declare const LinkSquareFilled: FluentFontIcon;
export declare const LinkSquareRegular: FluentFontIcon;
export declare const LinkToolboxFilled: FluentFontIcon;
export declare const LinkToolboxRegular: FluentFontIcon;
export declare const ListFilled: FluentFontIcon;
export declare const ListRegular: FluentFontIcon;
export declare const ListBarFilled: FluentFontIcon;
export declare const ListBarRegular: FluentFontIcon;
export declare const ListBarTreeFilled: FluentFontIcon;
export declare const ListBarTreeRegular: FluentFontIcon;
export declare const ListBarTreeOffsetFilled: FluentFontIcon;
export declare const ListBarTreeOffsetRegular: FluentFontIcon;
export declare const ListRtlFilled: FluentFontIcon;
export declare const ListRtlRegular: FluentFontIcon;
export declare const LiveFilled: FluentFontIcon;
export declare const LiveRegular: FluentFontIcon;
export declare const LiveOffFilled: FluentFontIcon;
export declare const LiveOffRegular: FluentFontIcon;
export declare const LocalLanguageFilled: FluentFontIcon;
export declare const LocalLanguageRegular: FluentFontIcon;
export declare const LocationFilled: FluentFontIcon;
export declare const LocationRegular: FluentFontIcon;
export declare const LocationAddFilled: FluentFontIcon;
export declare const LocationAddRegular: FluentFontIcon;
export declare const LocationAddLeftFilled: FluentFontIcon;
export declare const LocationAddLeftRegular: FluentFontIcon;
export declare const LocationAddRightFilled: FluentFontIcon;
export declare const LocationAddRightRegular: FluentFontIcon;
export declare const LocationAddUpFilled: FluentFontIcon;
export declare const LocationAddUpRegular: FluentFontIcon;
export declare const LocationArrowFilled: FluentFontIcon;
export declare const LocationArrowRegular: FluentFontIcon;
export declare const LocationArrowLeftFilled: FluentFontIcon;
export declare const LocationArrowLeftRegular: FluentFontIcon;
export declare const LocationArrowRightFilled: FluentFontIcon;
export declare const LocationArrowRightRegular: FluentFontIcon;
export declare const LocationArrowUpFilled: FluentFontIcon;
export declare const LocationArrowUpRegular: FluentFontIcon;
export declare const LocationCheckmarkFilled: FluentFontIcon;
export declare const LocationCheckmarkRegular: FluentFontIcon;
export declare const LocationDismissFilled: FluentFontIcon;
export declare const LocationDismissRegular: FluentFontIcon;
export declare const LocationLiveFilled: FluentFontIcon;
export declare const LocationLiveRegular: FluentFontIcon;
export declare const LocationOffFilled: FluentFontIcon;
export declare const LocationOffRegular: FluentFontIcon;
export declare const LocationRippleFilled: FluentFontIcon;
export declare const LocationRippleRegular: FluentFontIcon;
export declare const LocationSettingsFilled: FluentFontIcon;
export declare const LocationSettingsRegular: FluentFontIcon;
export declare const LocationTargetSquareFilled: FluentFontIcon;
export declare const LocationTargetSquareRegular: FluentFontIcon;
export declare const LockClosedFilled: FluentFontIcon;
export declare const LockClosedRegular: FluentFontIcon;
export declare const LockClosedKeyFilled: FluentFontIcon;
export declare const LockClosedKeyRegular: FluentFontIcon;
export declare const LockClosedRibbonFilled: FluentFontIcon;
export declare const LockClosedRibbonRegular: FluentFontIcon;
export declare const LockMultipleFilled: FluentFontIcon;
export declare const LockMultipleRegular: FluentFontIcon;
export declare const LockOpenFilled: FluentFontIcon;
export declare const LockOpenRegular: FluentFontIcon;
export declare const LockShieldFilled: FluentFontIcon;
export declare const LockShieldRegular: FluentFontIcon;
export declare const LotteryFilled: FluentFontIcon;
export declare const LotteryRegular: FluentFontIcon;
export declare const LuggageFilled: FluentFontIcon;
export declare const LuggageRegular: FluentFontIcon;
export declare const MailFilled: FluentFontIcon;
export declare const MailRegular: FluentFontIcon;
export declare const MailAddFilled: FluentFontIcon;
export declare const MailAddRegular: FluentFontIcon;
export declare const MailAlertFilled: FluentFontIcon;
export declare const MailAlertRegular: FluentFontIcon;
export declare const MailAllReadFilled: FluentFontIcon;
export declare const MailAllReadRegular: FluentFontIcon;
export declare const MailAllUnreadFilled: FluentFontIcon;
export declare const MailAllUnreadRegular: FluentFontIcon;
export declare const MailArrowClockwiseFilled: FluentFontIcon;
export declare const MailArrowClockwiseRegular: FluentFontIcon;
export declare const MailArrowDoubleBackFilled: FluentFontIcon;
export declare const MailArrowDoubleBackRegular: FluentFontIcon;
export declare const MailArrowDownFilled: FluentFontIcon;
export declare const MailArrowDownRegular: FluentFontIcon;
export declare const MailArrowForwardFilled: FluentFontIcon;
export declare const MailArrowForwardRegular: FluentFontIcon;
export declare const MailArrowUpFilled: FluentFontIcon;
export declare const MailArrowUpRegular: FluentFontIcon;
export declare const MailAttachFilled: FluentFontIcon;
export declare const MailAttachRegular: FluentFontIcon;
export declare const MailCheckmarkFilled: FluentFontIcon;
export declare const MailCheckmarkRegular: FluentFontIcon;
export declare const MailClockFilled: FluentFontIcon;
export declare const MailClockRegular: FluentFontIcon;
export declare const MailCopyFilled: FluentFontIcon;
export declare const MailCopyRegular: FluentFontIcon;
export declare const MailDataBarFilled: FluentFontIcon;
export declare const MailDataBarRegular: FluentFontIcon;
export declare const MailDismissFilled: FluentFontIcon;
export declare const MailDismissRegular: FluentFontIcon;
export declare const MailEditFilled: FluentFontIcon;
export declare const MailEditRegular: FluentFontIcon;
export declare const MailErrorFilled: FluentFontIcon;
export declare const MailErrorRegular: FluentFontIcon;
export declare const MailFishHookFilled: FluentFontIcon;
export declare const MailFishHookRegular: FluentFontIcon;
export declare const MailInboxFilled: FluentFontIcon;
export declare const MailInboxRegular: FluentFontIcon;
export declare const MailInboxAddFilled: FluentFontIcon;
export declare const MailInboxAddRegular: FluentFontIcon;
export declare const MailInboxAllFilled: FluentFontIcon;
export declare const MailInboxAllRegular: FluentFontIcon;
export declare const MailInboxArrowDownFilled: FluentFontIcon;
export declare const MailInboxArrowDownRegular: FluentFontIcon;
export declare const MailInboxArrowRightFilled: FluentFontIcon;
export declare const MailInboxArrowRightRegular: FluentFontIcon;
export declare const MailInboxArrowUpFilled: FluentFontIcon;
export declare const MailInboxArrowUpRegular: FluentFontIcon;
export declare const MailInboxCheckmarkFilled: FluentFontIcon;
export declare const MailInboxCheckmarkRegular: FluentFontIcon;
export declare const MailInboxDismissFilled: FluentFontIcon;
export declare const MailInboxDismissRegular: FluentFontIcon;
export declare const MailInboxPersonFilled: FluentFontIcon;
export declare const MailInboxPersonRegular: FluentFontIcon;
export declare const MailLinkFilled: FluentFontIcon;
export declare const MailLinkRegular: FluentFontIcon;
export declare const MailListFilled: FluentFontIcon;
export declare const MailListRegular: FluentFontIcon;
export declare const MailMultipleFilled: FluentFontIcon;
export declare const MailMultipleRegular: FluentFontIcon;
export declare const MailOffFilled: FluentFontIcon;
export declare const MailOffRegular: FluentFontIcon;
export declare const MailOpenPersonFilled: FluentFontIcon;
export declare const MailOpenPersonRegular: FluentFontIcon;
export declare const MailPauseFilled: FluentFontIcon;
export declare const MailPauseRegular: FluentFontIcon;
export declare const MailProhibitedFilled: FluentFontIcon;
export declare const MailProhibitedRegular: FluentFontIcon;
export declare const MailReadFilled: FluentFontIcon;
export declare const MailReadRegular: FluentFontIcon;
export declare const MailReadBriefcaseFilled: FluentFontIcon;
export declare const MailReadBriefcaseRegular: FluentFontIcon;
export declare const MailReadMultipleFilled: FluentFontIcon;
export declare const MailReadMultipleRegular: FluentFontIcon;
export declare const MailRewindFilled: FluentFontIcon;
export declare const MailRewindRegular: FluentFontIcon;
export declare const MailSettingsFilled: FluentFontIcon;
export declare const MailSettingsRegular: FluentFontIcon;
export declare const MailShieldFilled: FluentFontIcon;
export declare const MailShieldRegular: FluentFontIcon;
export declare const MailTemplateFilled: FluentFontIcon;
export declare const MailTemplateRegular: FluentFontIcon;
export declare const MailUnreadFilled: FluentFontIcon;
export declare const MailUnreadRegular: FluentFontIcon;
export declare const MailWarningFilled: FluentFontIcon;
export declare const MailWarningRegular: FluentFontIcon;
export declare const MailboxFilled: FluentFontIcon;
export declare const MailboxRegular: FluentFontIcon;
export declare const MapFilled: FluentFontIcon;
export declare const MapRegular: FluentFontIcon;
export declare const MapDriveFilled: FluentFontIcon;
export declare const MapDriveRegular: FluentFontIcon;
export declare const MarkdownFilled: FluentFontIcon;
export declare const MarkdownRegular: FluentFontIcon;
export declare const MatchAppLayoutFilled: FluentFontIcon;
export declare const MatchAppLayoutRegular: FluentFontIcon;
export declare const MathFormatLinearFilled: FluentFontIcon;
export declare const MathFormatLinearRegular: FluentFontIcon;
export declare const MathFormatProfessionalFilled: FluentFontIcon;
export declare const MathFormatProfessionalRegular: FluentFontIcon;
export declare const MathFormulaFilled: FluentFontIcon;
export declare const MathFormulaRegular: FluentFontIcon;
export declare const MathFormulaSparkleFilled: FluentFontIcon;
export declare const MathFormulaSparkleRegular: FluentFontIcon;
export declare const MathSymbolsFilled: FluentFontIcon;
export declare const MathSymbolsRegular: FluentFontIcon;
export declare const MaximizeFilled: FluentFontIcon;
export declare const MaximizeRegular: FluentFontIcon;
export declare const MeetNowFilled: FluentFontIcon;
export declare const MeetNowRegular: FluentFontIcon;
export declare const MegaphoneFilled: FluentFontIcon;
export declare const MegaphoneRegular: FluentFontIcon;
export declare const MegaphoneCircleFilled: FluentFontIcon;
export declare const MegaphoneCircleRegular: FluentFontIcon;
export declare const MegaphoneLoudFilled: FluentFontIcon;
export declare const MegaphoneLoudRegular: FluentFontIcon;
export declare const MegaphoneOffFilled: FluentFontIcon;
export declare const MegaphoneOffRegular: FluentFontIcon;
export declare const MentionFilled: FluentFontIcon;
export declare const MentionRegular: FluentFontIcon;
export declare const MentionArrowDownFilled: FluentFontIcon;
export declare const MentionArrowDownRegular: FluentFontIcon;
export declare const MentionBracketsFilled: FluentFontIcon;
export declare const MentionBracketsRegular: FluentFontIcon;
export declare const MergeFilled: FluentFontIcon;
export declare const MergeRegular: FluentFontIcon;
export declare const MicFilled: FluentFontIcon;
export declare const MicRegular: FluentFontIcon;
export declare const MicLinkFilled: FluentFontIcon;
export declare const MicLinkRegular: FluentFontIcon;
export declare const MicOffFilled: FluentFontIcon;
export declare const MicOffRegular: FluentFontIcon;
export declare const MicProhibitedFilled: FluentFontIcon;
export declare const MicProhibitedRegular: FluentFontIcon;
export declare const MicPulseFilled: FluentFontIcon;
export declare const MicPulseRegular: FluentFontIcon;
export declare const MicPulseOffFilled: FluentFontIcon;
export declare const MicPulseOffRegular: FluentFontIcon;
export declare const MicRecordFilled: FluentFontIcon;
export declare const MicRecordRegular: FluentFontIcon;
export declare const MicSettingsFilled: FluentFontIcon;
export declare const MicSettingsRegular: FluentFontIcon;
export declare const MicSparkleFilled: FluentFontIcon;
export declare const MicSparkleRegular: FluentFontIcon;
export declare const MicSyncFilled: FluentFontIcon;
export declare const MicSyncRegular: FluentFontIcon;
export declare const MicroscopeFilled: FluentFontIcon;
export declare const MicroscopeRegular: FluentFontIcon;
export declare const MidiFilled: FluentFontIcon;
export declare const MidiRegular: FluentFontIcon;
export declare const MobileOptimizedFilled: FluentFontIcon;
export declare const MobileOptimizedRegular: FluentFontIcon;
export declare const MoldFilled: FluentFontIcon;
export declare const MoldRegular: FluentFontIcon;
export declare const MoleculeFilled: FluentFontIcon;
export declare const MoleculeRegular: FluentFontIcon;
export declare const MoneyFilled: FluentFontIcon;
export declare const MoneyRegular: FluentFontIcon;
export declare const MoneyCalculatorFilled: FluentFontIcon;
export declare const MoneyCalculatorRegular: FluentFontIcon;
export declare const MoneyDismissFilled: FluentFontIcon;
export declare const MoneyDismissRegular: FluentFontIcon;
export declare const MoneyHandFilled: FluentFontIcon;
