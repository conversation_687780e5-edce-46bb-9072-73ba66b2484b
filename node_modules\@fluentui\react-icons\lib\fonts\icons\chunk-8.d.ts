import type { FluentFontIcon } from "../../utils/fonts/createFluentFontIcon";
export declare const ShiftsQuestionMarkFilled: FluentFontIcon;
export declare const ShiftsQuestionMarkRegular: FluentFontIcon;
export declare const ShiftsTeamFilled: FluentFontIcon;
export declare const ShiftsTeamRegular: FluentFontIcon;
export declare const ShoppingBagFilled: FluentFontIcon;
export declare const ShoppingBagRegular: FluentFontIcon;
export declare const ShoppingBagAddFilled: FluentFontIcon;
export declare const ShoppingBagAddRegular: FluentFontIcon;
export declare const ShoppingBagArrowLeftFilled: FluentFontIcon;
export declare const ShoppingBagArrowLeftRegular: FluentFontIcon;
export declare const ShoppingBagCheckmarkFilled: FluentFontIcon;
export declare const ShoppingBagCheckmarkRegular: FluentFontIcon;
export declare const ShoppingBagDismissFilled: FluentFontIcon;
export declare const ShoppingBagDismissRegular: FluentFontIcon;
export declare const ShoppingBagPauseFilled: FluentFontIcon;
export declare const ShoppingBagPauseRegular: FluentFontIcon;
export declare const ShoppingBagPercentFilled: FluentFontIcon;
export declare const ShoppingBagPercentRegular: FluentFontIcon;
export declare const ShoppingBagPlayFilled: FluentFontIcon;
export declare const ShoppingBagPlayRegular: FluentFontIcon;
export declare const ShoppingBagTagFilled: FluentFontIcon;
export declare const ShoppingBagTagRegular: FluentFontIcon;
export declare const ShortpickFilled: FluentFontIcon;
export declare const ShortpickRegular: FluentFontIcon;
export declare const ShowerheadFilled: FluentFontIcon;
export declare const ShowerheadRegular: FluentFontIcon;
export declare const SidebarSearchLtrFilled: FluentFontIcon;
export declare const SidebarSearchLtrRegular: FluentFontIcon;
export declare const SidebarSearchRtlFilled: FluentFontIcon;
export declare const SidebarSearchRtlRegular: FluentFontIcon;
export declare const SignOutFilled: FluentFontIcon;
export declare const SignOutRegular: FluentFontIcon;
export declare const SignatureFilled: FluentFontIcon;
export declare const SignatureRegular: FluentFontIcon;
export declare const SimFilled: FluentFontIcon;
export declare const SimRegular: FluentFontIcon;
export declare const SkipBack10Filled: FluentFontIcon;
export declare const SkipBack10Regular: FluentFontIcon;
export declare const SkipBack15Filled: FluentFontIcon;
export declare const SkipBack15Regular: FluentFontIcon;
export declare const SkipForward10Filled: FluentFontIcon;
export declare const SkipForward10Regular: FluentFontIcon;
export declare const SkipForward15Filled: FluentFontIcon;
export declare const SkipForward15Regular: FluentFontIcon;
export declare const SkipForward30Filled: FluentFontIcon;
export declare const SkipForward30Regular: FluentFontIcon;
export declare const SkipForwardTabFilled: FluentFontIcon;
export declare const SkipForwardTabRegular: FluentFontIcon;
export declare const SlashForwardFilled: FluentFontIcon;
export declare const SlashForwardRegular: FluentFontIcon;
export declare const SleepFilled: FluentFontIcon;
export declare const SleepRegular: FluentFontIcon;
export declare const SlideAddFilled: FluentFontIcon;
export declare const SlideAddRegular: FluentFontIcon;
export declare const SlideArrowRightFilled: FluentFontIcon;
export declare const SlideArrowRightRegular: FluentFontIcon;
export declare const SlideEraserFilled: FluentFontIcon;
export declare const SlideEraserRegular: FluentFontIcon;
export declare const SlideGridFilled: FluentFontIcon;
export declare const SlideGridRegular: FluentFontIcon;
export declare const SlideHideFilled: FluentFontIcon;
export declare const SlideHideRegular: FluentFontIcon;
export declare const SlideLayoutFilled: FluentFontIcon;
export declare const SlideLayoutRegular: FluentFontIcon;
export declare const SlideLinkFilled: FluentFontIcon;
export declare const SlideLinkRegular: FluentFontIcon;
export declare const SlideMicrophoneFilled: FluentFontIcon;
export declare const SlideMicrophoneRegular: FluentFontIcon;
export declare const SlideMultipleFilled: FluentFontIcon;
export declare const SlideMultipleRegular: FluentFontIcon;
export declare const SlideMultipleArrowRightFilled: FluentFontIcon;
export declare const SlideMultipleArrowRightRegular: FluentFontIcon;
export declare const SlideMultipleSearchFilled: FluentFontIcon;
export declare const SlideMultipleSearchRegular: FluentFontIcon;
export declare const SlidePlayFilled: FluentFontIcon;
export declare const SlidePlayRegular: FluentFontIcon;
export declare const SlideRecordFilled: FluentFontIcon;
export declare const SlideRecordRegular: FluentFontIcon;
export declare const SlideSearchFilled: FluentFontIcon;
export declare const SlideSearchRegular: FluentFontIcon;
export declare const SlideSettingsFilled: FluentFontIcon;
export declare const SlideSettingsRegular: FluentFontIcon;
export declare const SlideSizeFilled: FluentFontIcon;
export declare const SlideSizeRegular: FluentFontIcon;
export declare const SlideTextFilled: FluentFontIcon;
export declare const SlideTextRegular: FluentFontIcon;
export declare const SlideTextCallFilled: FluentFontIcon;
export declare const SlideTextCallRegular: FluentFontIcon;
export declare const SlideTextCursorFilled: FluentFontIcon;
export declare const SlideTextCursorRegular: FluentFontIcon;
export declare const SlideTextEditFilled: FluentFontIcon;
export declare const SlideTextEditRegular: FluentFontIcon;
export declare const SlideTextMultipleFilled: FluentFontIcon;
export declare const SlideTextMultipleRegular: FluentFontIcon;
export declare const SlideTextPersonFilled: FluentFontIcon;
export declare const SlideTextPersonRegular: FluentFontIcon;
export declare const SlideTextSparkleFilled: FluentFontIcon;
export declare const SlideTextSparkleRegular: FluentFontIcon;
export declare const SlideTextTitleFilled: FluentFontIcon;
export declare const SlideTextTitleRegular: FluentFontIcon;
export declare const SlideTextTitleAddFilled: FluentFontIcon;
export declare const SlideTextTitleAddRegular: FluentFontIcon;
export declare const SlideTextTitleCheckmarkFilled: FluentFontIcon;
export declare const SlideTextTitleCheckmarkRegular: FluentFontIcon;
export declare const SlideTextTitleEditFilled: FluentFontIcon;
export declare const SlideTextTitleEditRegular: FluentFontIcon;
export declare const SlideTopicAddFilled: FluentFontIcon;
export declare const SlideTopicAddRegular: FluentFontIcon;
export declare const SlideTransitionFilled: FluentFontIcon;
export declare const SlideTransitionRegular: FluentFontIcon;
export declare const SmartwatchFilled: FluentFontIcon;
export declare const SmartwatchRegular: FluentFontIcon;
export declare const SmartwatchDotFilled: FluentFontIcon;
export declare const SmartwatchDotRegular: FluentFontIcon;
export declare const SnoozeFilled: FluentFontIcon;
export declare const SnoozeRegular: FluentFontIcon;
export declare const SoundSourceFilled: FluentFontIcon;
export declare const SoundSourceRegular: FluentFontIcon;
export declare const SoundWaveCircleFilled: FluentFontIcon;
export declare const SoundWaveCircleRegular: FluentFontIcon;
export declare const SoundWaveCircleSparkleFilled: FluentFontIcon;
export declare const SoundWaveCircleSparkleRegular: FluentFontIcon;
export declare const Space3DFilled: FluentFontIcon;
export declare const Space3DRegular: FluentFontIcon;
export declare const SpacebarFilled: FluentFontIcon;
export declare const SpacebarRegular: FluentFontIcon;
export declare const SparkleFilled: FluentFontIcon;
export declare const SparkleRegular: FluentFontIcon;
export declare const SparkleActionFilled: FluentFontIcon;
export declare const SparkleActionRegular: FluentFontIcon;
export declare const SparkleCircleFilled: FluentFontIcon;
export declare const SparkleCircleRegular: FluentFontIcon;
export declare const SparkleInfoFilled: FluentFontIcon;
export declare const SparkleInfoRegular: FluentFontIcon;
export declare const SpatulaSpoonFilled: FluentFontIcon;
export declare const SpatulaSpoonRegular: FluentFontIcon;
export declare const Speaker0Filled: FluentFontIcon;
export declare const Speaker0Regular: FluentFontIcon;
export declare const Speaker1Filled: FluentFontIcon;
export declare const Speaker1Regular: FluentFontIcon;
export declare const Speaker2Filled: FluentFontIcon;
export declare const Speaker2Regular: FluentFontIcon;
export declare const SpeakerBluetoothFilled: FluentFontIcon;
export declare const SpeakerBluetoothRegular: FluentFontIcon;
export declare const SpeakerBoxFilled: FluentFontIcon;
export declare const SpeakerBoxRegular: FluentFontIcon;
export declare const SpeakerEditFilled: FluentFontIcon;
export declare const SpeakerEditRegular: FluentFontIcon;
export declare const SpeakerMuteFilled: FluentFontIcon;
export declare const SpeakerMuteRegular: FluentFontIcon;
export declare const SpeakerOffFilled: FluentFontIcon;
export declare const SpeakerOffRegular: FluentFontIcon;
export declare const SpeakerSettingsFilled: FluentFontIcon;
export declare const SpeakerSettingsRegular: FluentFontIcon;
export declare const SpeakerUsbFilled: FluentFontIcon;
export declare const SpeakerUsbRegular: FluentFontIcon;
export declare const SpinnerIosFilled: FluentFontIcon;
export declare const SpinnerIosRegular: FluentFontIcon;
export declare const SplitHintFilled: FluentFontIcon;
export declare const SplitHintRegular: FluentFontIcon;
export declare const SplitHorizontalFilled: FluentFontIcon;
export declare const SplitHorizontalRegular: FluentFontIcon;
export declare const SplitVerticalFilled: FluentFontIcon;
export declare const SplitVerticalRegular: FluentFontIcon;
export declare const SportFilled: FluentFontIcon;
export declare const SportRegular: FluentFontIcon;
export declare const SportAmericanFootballFilled: FluentFontIcon;
export declare const SportAmericanFootballRegular: FluentFontIcon;
export declare const SportBaseballFilled: FluentFontIcon;
export declare const SportBaseballRegular: FluentFontIcon;
export declare const SportBasketballFilled: FluentFontIcon;
export declare const SportBasketballRegular: FluentFontIcon;
export declare const SportHockeyFilled: FluentFontIcon;
export declare const SportHockeyRegular: FluentFontIcon;
export declare const SportSoccerFilled: FluentFontIcon;
export declare const SportSoccerRegular: FluentFontIcon;
export declare const SquareFilled: FluentFontIcon;
export declare const SquareRegular: FluentFontIcon;
export declare const SquareAddFilled: FluentFontIcon;
export declare const SquareAddRegular: FluentFontIcon;
export declare const SquareArrowForwardFilled: FluentFontIcon;
export declare const SquareArrowForwardRegular: FluentFontIcon;
export declare const SquareDismissFilled: FluentFontIcon;
export declare const SquareDismissRegular: FluentFontIcon;
export declare const SquareDovetailJointFilled: FluentFontIcon;
export declare const SquareDovetailJointRegular: FluentFontIcon;
export declare const SquareEraserFilled: FluentFontIcon;
export declare const SquareEraserRegular: FluentFontIcon;
export declare const SquareHintFilled: FluentFontIcon;
export declare const SquareHintRegular: FluentFontIcon;
export declare const SquareHintAppsFilled: FluentFontIcon;
export declare const SquareHintAppsRegular: FluentFontIcon;
export declare const SquareHintArrowBackFilled: FluentFontIcon;
export declare const SquareHintArrowBackRegular: FluentFontIcon;
export declare const SquareHintHexagonFilled: FluentFontIcon;
export declare const SquareHintHexagonRegular: FluentFontIcon;
export declare const SquareHintSparklesFilled: FluentFontIcon;
export declare const SquareHintSparklesRegular: FluentFontIcon;
export declare const SquareMultipleFilled: FluentFontIcon;
export declare const SquareMultipleRegular: FluentFontIcon;
export declare const SquareShadowFilled: FluentFontIcon;
export declare const SquareShadowRegular: FluentFontIcon;
export declare const SquareTextArrowRepeatAllFilled: FluentFontIcon;
export declare const SquareTextArrowRepeatAllRegular: FluentFontIcon;
export declare const SquaresNestedFilled: FluentFontIcon;
export declare const SquaresNestedRegular: FluentFontIcon;
export declare const StackFilled: FluentFontIcon;
export declare const StackRegular: FluentFontIcon;
export declare const StackAddFilled: FluentFontIcon;
export declare const StackAddRegular: FluentFontIcon;
export declare const StackArrowForwardFilled: FluentFontIcon;
export declare const StackArrowForwardRegular: FluentFontIcon;
export declare const StackOffFilled: FluentFontIcon;
export declare const StackOffRegular: FluentFontIcon;
export declare const StackStarFilled: FluentFontIcon;
export declare const StackStarRegular: FluentFontIcon;
export declare const StackVerticalFilled: FluentFontIcon;
export declare const StackVerticalRegular: FluentFontIcon;
export declare const StarFilled: FluentFontIcon;
export declare const StarRegular: FluentFontIcon;
export declare const StarAddFilled: FluentFontIcon;
export declare const StarAddRegular: FluentFontIcon;
export declare const StarArrowBackFilled: FluentFontIcon;
export declare const StarArrowBackRegular: FluentFontIcon;
export declare const StarArrowRightEndFilled: FluentFontIcon;
export declare const StarArrowRightEndRegular: FluentFontIcon;
export declare const StarArrowRightStartFilled: FluentFontIcon;
export declare const StarArrowRightStartRegular: FluentFontIcon;
export declare const StarCheckmarkFilled: FluentFontIcon;
export declare const StarCheckmarkRegular: FluentFontIcon;
export declare const StarDismissFilled: FluentFontIcon;
export declare const StarDismissRegular: FluentFontIcon;
export declare const StarEditFilled: FluentFontIcon;
export declare const StarEditRegular: FluentFontIcon;
export declare const StarEmphasisFilled: FluentFontIcon;
export declare const StarEmphasisRegular: FluentFontIcon;
export declare const StarHalfFilled: FluentFontIcon;
export declare const StarHalfRegular: FluentFontIcon;
export declare const StarLineHorizontal3Filled: FluentFontIcon;
export declare const StarLineHorizontal3Regular: FluentFontIcon;
export declare const StarOffFilled: FluentFontIcon;
export declare const StarOffRegular: FluentFontIcon;
export declare const StarOneQuarterFilled: FluentFontIcon;
export declare const StarOneQuarterRegular: FluentFontIcon;
export declare const StarProhibitedFilled: FluentFontIcon;
export declare const StarProhibitedRegular: FluentFontIcon;
export declare const StarSettingsFilled: FluentFontIcon;
export declare const StarSettingsRegular: FluentFontIcon;
export declare const StarThreeQuarterFilled: FluentFontIcon;
export declare const StarThreeQuarterRegular: FluentFontIcon;
export declare const StatusFilled: FluentFontIcon;
export declare const StatusRegular: FluentFontIcon;
export declare const StepFilled: FluentFontIcon;
export declare const StepRegular: FluentFontIcon;
export declare const StepsFilled: FluentFontIcon;
export declare const StepsRegular: FluentFontIcon;
export declare const StethoscopeFilled: FluentFontIcon;
export declare const StethoscopeRegular: FluentFontIcon;
export declare const StickerFilled: FluentFontIcon;
export declare const StickerRegular: FluentFontIcon;
export declare const StickerAddFilled: FluentFontIcon;
export declare const StickerAddRegular: FluentFontIcon;
export declare const StopFilled: FluentFontIcon;
export declare const StopRegular: FluentFontIcon;
export declare const StorageFilled: FluentFontIcon;
export declare const StorageRegular: FluentFontIcon;
export declare const StoreMicrosoftFilled: FluentFontIcon;
export declare const StoreMicrosoftRegular: FluentFontIcon;
export declare const StreamFilled: FluentFontIcon;
export declare const StreamRegular: FluentFontIcon;
export declare const StreamInputFilled: FluentFontIcon;
export declare const StreamInputRegular: FluentFontIcon;
export declare const StreamInputOutputFilled: FluentFontIcon;
export declare const StreamInputOutputRegular: FluentFontIcon;
export declare const StreamOutputFilled: FluentFontIcon;
export declare const StreamOutputRegular: FluentFontIcon;
export declare const StreetSignFilled: FluentFontIcon;
export declare const StreetSignRegular: FluentFontIcon;
export declare const StyleGuideFilled: FluentFontIcon;
export declare const StyleGuideRegular: FluentFontIcon;
export declare const SubGridFilled: FluentFontIcon;
export declare const SubGridRegular: FluentFontIcon;
export declare const SubtitlesFilled: FluentFontIcon;
export declare const SubtitlesRegular: FluentFontIcon;
export declare const SubtractFilled: FluentFontIcon;
export declare const SubtractRegular: FluentFontIcon;
export declare const SubtractCircleFilled: FluentFontIcon;
export declare const SubtractCircleRegular: FluentFontIcon;
export declare const SubtractCircleArrowBackFilled: FluentFontIcon;
export declare const SubtractCircleArrowBackRegular: FluentFontIcon;
export declare const SubtractCircleArrowForwardFilled: FluentFontIcon;
export declare const SubtractCircleArrowForwardRegular: FluentFontIcon;
export declare const SubtractParenthesesFilled: FluentFontIcon;
export declare const SubtractParenthesesRegular: FluentFontIcon;
export declare const SubtractSquareFilled: FluentFontIcon;
export declare const SubtractSquareRegular: FluentFontIcon;
export declare const SubtractSquareMultipleFilled: FluentFontIcon;
export declare const SubtractSquareMultipleRegular: FluentFontIcon;
export declare const SurfaceEarbudsFilled: FluentFontIcon;
export declare const SurfaceEarbudsRegular: FluentFontIcon;
export declare const SurfaceHubFilled: FluentFontIcon;
export declare const SurfaceHubRegular: FluentFontIcon;
export declare const SwimmingPoolFilled: FluentFontIcon;
export declare const SwimmingPoolRegular: FluentFontIcon;
export declare const SwipeDownFilled: FluentFontIcon;
export declare const SwipeDownRegular: FluentFontIcon;
export declare const SwipeRightFilled: FluentFontIcon;
export declare const SwipeRightRegular: FluentFontIcon;
export declare const SwipeUpFilled: FluentFontIcon;
export declare const SwipeUpRegular: FluentFontIcon;
export declare const SymbolsFilled: FluentFontIcon;
export declare const SymbolsRegular: FluentFontIcon;
export declare const SyncOffFilled: FluentFontIcon;
export declare const SyncOffRegular: FluentFontIcon;
export declare const SyringeFilled: FluentFontIcon;
export declare const SyringeRegular: FluentFontIcon;
export declare const SystemFilled: FluentFontIcon;
export declare const SystemRegular: FluentFontIcon;
export declare const TabFilled: FluentFontIcon;
export declare const TabRegular: FluentFontIcon;
export declare const TabAddFilled: FluentFontIcon;
export declare const TabAddRegular: FluentFontIcon;
export declare const TabArrowLeftFilled: FluentFontIcon;
export declare const TabArrowLeftRegular: FluentFontIcon;
export declare const TabDesktopFilled: FluentFontIcon;
export declare const TabDesktopRegular: FluentFontIcon;
export declare const TabDesktopArrowClockwiseFilled: FluentFontIcon;
export declare const TabDesktopArrowClockwiseRegular: FluentFontIcon;
export declare const TabDesktopArrowLeftFilled: FluentFontIcon;
export declare const TabDesktopArrowLeftRegular: FluentFontIcon;
export declare const TabDesktopBottomFilled: FluentFontIcon;
export declare const TabDesktopBottomRegular: FluentFontIcon;
export declare const TabDesktopClockFilled: FluentFontIcon;
export declare const TabDesktopClockRegular: FluentFontIcon;
export declare const TabDesktopCopyFilled: FluentFontIcon;
export declare const TabDesktopCopyRegular: FluentFontIcon;
export declare const TabDesktopImageFilled: FluentFontIcon;
export declare const TabDesktopImageRegular: FluentFontIcon;
export declare const TabDesktopLinkFilled: FluentFontIcon;
export declare const TabDesktopLinkRegular: FluentFontIcon;
export declare const TabDesktopMultipleFilled: FluentFontIcon;
export declare const TabDesktopMultipleRegular: FluentFontIcon;
export declare const TabDesktopMultipleAddFilled: FluentFontIcon;
export declare const TabDesktopMultipleAddRegular: FluentFontIcon;
export declare const TabDesktopMultipleBottomFilled: FluentFontIcon;
export declare const TabDesktopMultipleBottomRegular: FluentFontIcon;
export declare const TabDesktopMultipleSparkleFilled: FluentFontIcon;
export declare const TabDesktopMultipleSparkleRegular: FluentFontIcon;
export declare const TabDesktopNewPageFilled: FluentFontIcon;
export declare const TabDesktopNewPageRegular: FluentFontIcon;
export declare const TabDesktopSearchFilled: FluentFontIcon;
export declare const TabDesktopSearchRegular: FluentFontIcon;
export declare const TabGroupFilled: FluentFontIcon;
export declare const TabGroupRegular: FluentFontIcon;
export declare const TabInPrivateFilled: FluentFontIcon;
export declare const TabInPrivateRegular: FluentFontIcon;
export declare const TabInprivateAccountFilled: FluentFontIcon;
export declare const TabInprivateAccountRegular: FluentFontIcon;
export declare const TabProhibitedFilled: FluentFontIcon;
export declare const TabProhibitedRegular: FluentFontIcon;
export declare const TabShieldDismissFilled: FluentFontIcon;
export declare const TabShieldDismissRegular: FluentFontIcon;
export declare const TableFilled: FluentFontIcon;
export declare const TableRegular: FluentFontIcon;
export declare const TableAddFilled: FluentFontIcon;
export declare const TableAddRegular: FluentFontIcon;
export declare const TableAltTextFilled: FluentFontIcon;
export declare const TableAltTextRegular: FluentFontIcon;
export declare const TableArrowRepeatAllFilled: FluentFontIcon;
export declare const TableArrowRepeatAllRegular: FluentFontIcon;
export declare const TableArrowUpFilled: FluentFontIcon;
export declare const TableArrowUpRegular: FluentFontIcon;
export declare const TableBottomRowFilled: FluentFontIcon;
export declare const TableBottomRowRegular: FluentFontIcon;
export declare const TableCalculatorFilled: FluentFontIcon;
export declare const TableCalculatorRegular: FluentFontIcon;
export declare const TableCellAddFilled: FluentFontIcon;
export declare const TableCellAddRegular: FluentFontIcon;
export declare const TableCellCenterFilled: FluentFontIcon;
export declare const TableCellCenterRegular: FluentFontIcon;
export declare const TableCellCenterArrowRepeatAllFilled: FluentFontIcon;
export declare const TableCellCenterArrowRepeatAllRegular: FluentFontIcon;
export declare const TableCellCenterEditFilled: FluentFontIcon;
export declare const TableCellCenterEditRegular: FluentFontIcon;
export declare const TableCellCenterLinkFilled: FluentFontIcon;
export declare const TableCellCenterLinkRegular: FluentFontIcon;
export declare const TableCellCenterSearchFilled: FluentFontIcon;
export declare const TableCellCenterSearchRegular: FluentFontIcon;
export declare const TableCellEditFilled: FluentFontIcon;
export declare const TableCellEditRegular: FluentFontIcon;
export declare const TableCellsMergeFilled: FluentFontIcon;
export declare const TableCellsMergeRegular: FluentFontIcon;
export declare const TableCellsSplitFilled: FluentFontIcon;
export declare const TableCellsSplitRegular: FluentFontIcon;
export declare const TableCheckerFilled: FluentFontIcon;
export declare const TableCheckerRegular: FluentFontIcon;
export declare const TableColumnTopBottomFilled: FluentFontIcon;
export declare const TableColumnTopBottomRegular: FluentFontIcon;
export declare const TableColumnTopBottomArrowRepeatAllFilled: FluentFontIcon;
export declare const TableColumnTopBottomArrowRepeatAllRegular: FluentFontIcon;
export declare const TableColumnTopBottomEditFilled: FluentFontIcon;
export declare const TableColumnTopBottomEditRegular: FluentFontIcon;
export declare const TableColumnTopBottomLinkFilled: FluentFontIcon;
export declare const TableColumnTopBottomLinkRegular: FluentFontIcon;
export declare const TableColumnTopBottomSearchFilled: FluentFontIcon;
export declare const TableColumnTopBottomSearchRegular: FluentFontIcon;
export declare const TableCopyFilled: FluentFontIcon;
export declare const TableCopyRegular: FluentFontIcon;
export declare const TableCursorFilled: FluentFontIcon;
export declare const TableCursorRegular: FluentFontIcon;
export declare const TableDeleteColumnFilled: FluentFontIcon;
export declare const TableDeleteColumnRegular: FluentFontIcon;
export declare const TableDeleteRowFilled: FluentFontIcon;
export declare const TableDeleteRowRegular: FluentFontIcon;
export declare const TableDismissFilled: FluentFontIcon;
export declare const TableDismissRegular: FluentFontIcon;
export declare const TableEditFilled: FluentFontIcon;
export declare const TableEditRegular: FluentFontIcon;
export declare const TableFreezeColumnFilled: FluentFontIcon;
export declare const TableFreezeColumnRegular: FluentFontIcon;
export declare const TableFreezeColumnAndRowFilled: FluentFontIcon;
export declare const TableFreezeColumnAndRowRegular: FluentFontIcon;
export declare const TableFreezeColumnAndRowDismissFilled: FluentFontIcon;
export declare const TableFreezeColumnAndRowDismissRegular: FluentFontIcon;
export declare const TableFreezeColumnDismissFilled: FluentFontIcon;
export declare const TableFreezeColumnDismissRegular: FluentFontIcon;
export declare const TableFreezeRowFilled: FluentFontIcon;
export declare const TableFreezeRowRegular: FluentFontIcon;
export declare const TableFreezeRowDismissFilled: FluentFontIcon;
export declare const TableFreezeRowDismissRegular: FluentFontIcon;
export declare const TableImageFilled: FluentFontIcon;
export declare const TableImageRegular: FluentFontIcon;
export declare const TableInsertColumnFilled: FluentFontIcon;
export declare const TableInsertColumnRegular: FluentFontIcon;
export declare const TableInsertRowFilled: FluentFontIcon;
export declare const TableInsertRowRegular: FluentFontIcon;
export declare const TableLightningFilled: FluentFontIcon;
export declare const TableLightningRegular: FluentFontIcon;
export declare const TableLinkFilled: FluentFontIcon;
export declare const TableLinkRegular: FluentFontIcon;
export declare const TableLockFilled: FluentFontIcon;
export declare const TableLockRegular: FluentFontIcon;
export declare const TableMoveAboveFilled: FluentFontIcon;
export declare const TableMoveAboveRegular: FluentFontIcon;
export declare const TableMoveBelowFilled: FluentFontIcon;
export declare const TableMoveBelowRegular: FluentFontIcon;
export declare const TableMoveLeftFilled: FluentFontIcon;
export declare const TableMoveLeftRegular: FluentFontIcon;
export declare const TableMoveRightFilled: FluentFontIcon;
export declare const TableMoveRightRegular: FluentFontIcon;
export declare const TableMultipleFilled: FluentFontIcon;
export declare const TableMultipleRegular: FluentFontIcon;
export declare const TableOffsetFilled: FluentFontIcon;
export declare const TableOffsetRegular: FluentFontIcon;
export declare const TableOffsetAddFilled: FluentFontIcon;
export declare const TableOffsetAddRegular: FluentFontIcon;
export declare const TableOffsetLessThanOrEqualToFilled: FluentFontIcon;
export declare const TableOffsetLessThanOrEqualToRegular: FluentFontIcon;
export declare const TableOffsetSettingsFilled: FluentFontIcon;
export declare const TableOffsetSettingsRegular: FluentFontIcon;
export declare const TableResizeColumnFilled: FluentFontIcon;
export declare const TableResizeColumnRegular: FluentFontIcon;
export declare const TableResizeRowFilled: FluentFontIcon;
export declare const TableResizeRowRegular: FluentFontIcon;
export declare const TableSearchFilled: FluentFontIcon;
export declare const TableSearchRegular: FluentFontIcon;
export declare const TableSettingsFilled: FluentFontIcon;
export declare const TableSettingsRegular: FluentFontIcon;
export declare const TableSimpleFilled: FluentFontIcon;
export declare const TableSimpleRegular: FluentFontIcon;
export declare const TableSimpleCheckmarkFilled: FluentFontIcon;
export declare const TableSimpleCheckmarkRegular: FluentFontIcon;
export declare const TableSimpleExcludeFilled: FluentFontIcon;
export declare const TableSimpleExcludeRegular: FluentFontIcon;
export declare const TableSimpleIncludeFilled: FluentFontIcon;
export declare const TableSimpleIncludeRegular: FluentFontIcon;
export declare const TableSimpleMultipleFilled: FluentFontIcon;
export declare const TableSimpleMultipleRegular: FluentFontIcon;
export declare const TableSparkleFilled: FluentFontIcon;
export declare const TableSparkleRegular: FluentFontIcon;
export declare const TableSplitFilled: FluentFontIcon;
export declare const TableSplitRegular: FluentFontIcon;
export declare const TableStackAboveFilled: FluentFontIcon;
export declare const TableStackAboveRegular: FluentFontIcon;
export declare const TableStackBelowFilled: FluentFontIcon;
export declare const TableStackBelowRegular: FluentFontIcon;
export declare const TableStackLeftFilled: FluentFontIcon;
export declare const TableStackLeftRegular: FluentFontIcon;
export declare const TableStackRightFilled: FluentFontIcon;
export declare const TableStackRightRegular: FluentFontIcon;
export declare const TableSwitchFilled: FluentFontIcon;
export declare const TableSwitchRegular: FluentFontIcon;
export declare const TabletFilled: FluentFontIcon;
export declare const TabletRegular: FluentFontIcon;
export declare const TabletLaptopFilled: FluentFontIcon;
export declare const TabletLaptopRegular: FluentFontIcon;
export declare const TabletSpeakerFilled: FluentFontIcon;
export declare const TabletSpeakerRegular: FluentFontIcon;
export declare const TabsFilled: FluentFontIcon;
export declare const TabsRegular: FluentFontIcon;
