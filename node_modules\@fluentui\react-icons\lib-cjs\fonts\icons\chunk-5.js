"use client";
"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.InfoFilled = exports.IncognitoRegular = exports.IncognitoFilled = exports.ImportantRegular = exports.ImportantFilled = exports.ImmersiveReaderRegular = exports.ImmersiveReaderFilled = exports.ImageTableRegular = exports.ImageTableFilled = exports.ImageStackRegular = exports.ImageStackFilled = exports.ImageSplitRegular = exports.ImageSplitFilled = exports.ImageSparkleRegular = exports.ImageSparkleFilled = exports.ImageShadowRegular = exports.ImageShadowFilled = exports.ImageSearchRegular = exports.ImageSearchFilled = exports.ImageReflectionRegular = exports.ImageReflectionFilled = exports.ImageProhibitedRegular = exports.ImageProhibitedFilled = exports.ImageOffRegular = exports.ImageOffFilled = exports.ImageMultipleOffRegular = exports.ImageMultipleOffFilled = exports.ImageMultipleRegular = exports.ImageMultipleFilled = exports.ImageGlobeRegular = exports.ImageGlobeFilled = exports.ImageEditRegular = exports.ImageEditFilled = exports.ImageCopyRegular = exports.ImageCopyFilled = exports.ImageCircleRegular = exports.ImageCircleFilled = exports.ImageBorderRegular = exports.ImageBorderFilled = exports.ImageArrowForwardRegular = exports.ImageArrowForwardFilled = exports.ImageArrowCounterclockwiseRegular = exports.ImageArrowCounterclockwiseFilled = exports.ImageArrowBackRegular = exports.ImageArrowBackFilled = exports.ImageAltTextRegular = exports.ImageAltTextFilled = exports.ImageAddRegular = exports.ImageAddFilled = exports.ImageRegular = void 0;
exports.KeyboardLayoutSplitRegular = exports.KeyboardLayoutSplitFilled = exports.KeyboardLayoutResizeRegular = exports.KeyboardLayoutResizeFilled = exports.KeyboardLayoutOneHandedLeftRegular = exports.KeyboardLayoutOneHandedLeftFilled = exports.KeyboardLayoutFloatRegular = exports.KeyboardLayoutFloatFilled = exports.KeyboardDockRegular = exports.KeyboardDockFilled = exports.KeyboardRegular = exports.KeyboardFilled = exports.Keyboard123Regular = exports.Keyboard123Filled = exports.KeyResetRegular = exports.KeyResetFilled = exports.KeyMultipleRegular = exports.KeyMultipleFilled = exports.KeyCommandRegular = exports.KeyCommandFilled = exports.KeyRegular = exports.KeyFilled = exports.JoystickRegular = exports.JoystickFilled = exports.JavascriptRegular = exports.JavascriptFilled = exports.IotAlertRegular = exports.IotAlertFilled = exports.IotRegular = exports.IotFilled = exports.IosChevronRightRegular = exports.IosChevronRightFilled = exports.InsertRegular = exports.InsertFilled = exports.InprivateAccountRegular = exports.InprivateAccountFilled = exports.InkingToolAccentFilled = exports.InkingToolRegular = exports.InkingToolFilled = exports.InkStrokeArrowUpDownRegular = exports.InkStrokeArrowUpDownFilled = exports.InkStrokeArrowDownRegular = exports.InkStrokeArrowDownFilled = exports.InkStrokeRegular = exports.InkStrokeFilled = exports.InfoSparkleRegular = exports.InfoSparkleFilled = exports.InfoShieldRegular = exports.InfoShieldFilled = exports.InfoRegular = void 0;
exports.LayoutColumnOneThirdRightRegular = exports.LayoutColumnOneThirdRightFilled = exports.LayoutColumnOneThirdLeftRegular = exports.LayoutColumnOneThirdLeftFilled = exports.LayoutColumnFourFocusRightFilled = exports.LayoutColumnFourFocusLeftFilled = exports.LayoutColumnFourFocusCenterRightFilled = exports.LayoutColumnFourFocusCenterLeftFilled = exports.LayoutColumnFourRegular = exports.LayoutColumnFourFilled = exports.LayoutCellFourFocusTopRightFilled = exports.LayoutCellFourFocusTopLeftFilled = exports.LayoutCellFourFocusBottomRightFilled = exports.LayoutCellFourFocusBottomLeftFilled = exports.LayoutCellFourRegular = exports.LayoutCellFourFilled = exports.LayerDiagonalSparkleRegular = exports.LayerDiagonalSparkleFilled = exports.LayerDiagonalPersonRegular = exports.LayerDiagonalPersonFilled = exports.LayerDiagonalAddRegular = exports.LayerDiagonalAddFilled = exports.LayerDiagonalRegular = exports.LayerDiagonalFilled = exports.LayerRegular = exports.LayerFilled = exports.LauncherSettingsRegular = exports.LauncherSettingsFilled = exports.LassoRegular = exports.LassoFilled = exports.LaserToolRegular = exports.LaserToolFilled = exports.LaptopShieldRegular = exports.LaptopShieldFilled = exports.LaptopSettingsRegular = exports.LaptopSettingsFilled = exports.LaptopPersonRegular = exports.LaptopPersonFilled = exports.LaptopDismissRegular = exports.LaptopDismissFilled = exports.LaptopBriefcaseRegular = exports.LaptopBriefcaseFilled = exports.LaptopRegular = exports.LaptopFilled = exports.KeyboardTabRegular = exports.KeyboardTabFilled = exports.KeyboardShiftUppercaseRegular = exports.KeyboardShiftUppercaseFilled = exports.KeyboardShiftRegular = exports.KeyboardShiftFilled = void 0;
exports.LayoutRowTwoSplitTopFocusTopLeftFilled = exports.LayoutRowTwoSplitTopFocusBottomFilled = exports.LayoutRowTwoSplitTopRegular = exports.LayoutRowTwoSplitTopFilled = exports.LayoutRowTwoSplitBottomFocusTopFilled = exports.LayoutRowTwoSplitBottomFocusBottomRightFilled = exports.LayoutRowTwoSplitBottomFocusBottomLeftFilled = exports.LayoutRowTwoSplitBottomRegular = exports.LayoutRowTwoSplitBottomFilled = exports.LayoutRowTwoSettingsRegular = exports.LayoutRowTwoSettingsFilled = exports.LayoutRowTwoFocusTopSettingsFilled = exports.LayoutRowTwoFocusTopFilled = exports.LayoutRowTwoFocusBottomFilled = exports.LayoutRowTwoRegular = exports.LayoutRowTwoFilled = exports.LayoutRowThreeFocusTopFilled = exports.LayoutRowThreeFocusCenterFilled = exports.LayoutRowThreeFocusBottomFilled = exports.LayoutRowThreeRegular = exports.LayoutRowThreeFilled = exports.LayoutRowFourFocusTopFilled = exports.LayoutRowFourFocusCenterTopFilled = exports.LayoutRowFourFocusCenterBottomFilled = exports.LayoutRowFourFocusBottomFilled = exports.LayoutRowFourRegular = exports.LayoutRowFourFilled = exports.LayoutDynamicRegular = exports.LayoutDynamicFilled = exports.LayoutColumnTwoSplitRightFocusTopRightFilled = exports.LayoutColumnTwoSplitRightFocusLeftFilled = exports.LayoutColumnTwoSplitRightFocusBottomRightFilled = exports.LayoutColumnTwoSplitRightRegular = exports.LayoutColumnTwoSplitRightFilled = exports.LayoutColumnTwoSplitLeftFocusTopLeftFilled = exports.LayoutColumnTwoSplitLeftFocusRightFilled = exports.LayoutColumnTwoSplitLeftFocusBottomLeftFilled = exports.LayoutColumnTwoSplitLeftRegular = exports.LayoutColumnTwoSplitLeftFilled = exports.LayoutColumnTwoFocusRightFilled = exports.LayoutColumnTwoFocusLeftFilled = exports.LayoutColumnTwoRegular = exports.LayoutColumnTwoFilled = exports.LayoutColumnThreeFocusRightFilled = exports.LayoutColumnThreeFocusLeftFilled = exports.LayoutColumnThreeFocusCenterFilled = exports.LayoutColumnThreeRegular = exports.LayoutColumnThreeFilled = exports.LayoutColumnOneThirdRightHintRegular = exports.LayoutColumnOneThirdRightHintFilled = void 0;
exports.LineStyleFilled = exports.LineHorizontal5ErrorRegular = exports.LineHorizontal5ErrorFilled = exports.LineHorizontal5Regular = exports.LineHorizontal5Filled = exports.LineHorizontal4SearchRegular = exports.LineHorizontal4SearchFilled = exports.LineHorizontal4Regular = exports.LineHorizontal4Filled = exports.LineHorizontal3Regular = exports.LineHorizontal3Filled = exports.LineHorizontal2DashesSolidRegular = exports.LineHorizontal2DashesSolidFilled = exports.LineHorizontal1DotRegular = exports.LineHorizontal1DotFilled = exports.LineHorizontal1DashesRegular = exports.LineHorizontal1DashesFilled = exports.LineHorizontal1DashDotDashRegular = exports.LineHorizontal1DashDotDashFilled = exports.LineHorizontal1Regular = exports.LineHorizontal1Filled = exports.LineFlowDiagonalUpRightRegular = exports.LineFlowDiagonalUpRightFilled = exports.LineDashesRegular = exports.LineDashesFilled = exports.LineRegular = exports.LineFilled = exports.LikertRegular = exports.LikertFilled = exports.LightbulbPersonRegular = exports.LightbulbPersonFilled = exports.LightbulbFilamentRegular = exports.LightbulbFilamentFilled = exports.LightbulbCircleRegular = exports.LightbulbCircleFilled = exports.LightbulbCheckmarkRegular = exports.LightbulbCheckmarkFilled = exports.LightbulbRegular = exports.LightbulbFilled = exports.LibraryRegular = exports.LibraryFilled = exports.LearningAppRegular = exports.LearningAppFilled = exports.LeafTwoRegular = exports.LeafTwoFilled = exports.LeafThreeRegular = exports.LeafThreeFilled = exports.LeafOneRegular = exports.LeafOneFilled = exports.LayoutRowTwoSplitTopFocusTopRightFilled = void 0;
exports.LocationArrowLeftFilled = exports.LocationArrowRegular = exports.LocationArrowFilled = exports.LocationAddUpRegular = exports.LocationAddUpFilled = exports.LocationAddRightRegular = exports.LocationAddRightFilled = exports.LocationAddLeftRegular = exports.LocationAddLeftFilled = exports.LocationAddRegular = exports.LocationAddFilled = exports.LocationRegular = exports.LocationFilled = exports.LocalLanguageRegular = exports.LocalLanguageFilled = exports.LiveOffRegular = exports.LiveOffFilled = exports.LiveRegular = exports.LiveFilled = exports.ListRtlRegular = exports.ListRtlFilled = exports.ListBarTreeOffsetRegular = exports.ListBarTreeOffsetFilled = exports.ListBarTreeRegular = exports.ListBarTreeFilled = exports.ListBarRegular = exports.ListBarFilled = exports.ListRegular = exports.ListFilled = exports.LinkToolboxRegular = exports.LinkToolboxFilled = exports.LinkSquareRegular = exports.LinkSquareFilled = exports.LinkPersonRegular = exports.LinkPersonFilled = exports.LinkMultipleRegular = exports.LinkMultipleFilled = exports.LinkEditRegular = exports.LinkEditFilled = exports.LinkDismissRegular = exports.LinkDismissFilled = exports.LinkAddRegular = exports.LinkAddFilled = exports.LinkRegular = exports.LinkFilled = exports.LineThicknessRegular = exports.LineThicknessFilled = exports.LineStyleSketchRegular = exports.LineStyleSketchFilled = exports.LineStyleRegular = void 0;
exports.MailArrowDownFilled = exports.MailArrowDoubleBackRegular = exports.MailArrowDoubleBackFilled = exports.MailArrowClockwiseRegular = exports.MailArrowClockwiseFilled = exports.MailAllUnreadRegular = exports.MailAllUnreadFilled = exports.MailAllReadRegular = exports.MailAllReadFilled = exports.MailAlertRegular = exports.MailAlertFilled = exports.MailAddRegular = exports.MailAddFilled = exports.MailRegular = exports.MailFilled = exports.LuggageRegular = exports.LuggageFilled = exports.LotteryRegular = exports.LotteryFilled = exports.LockShieldRegular = exports.LockShieldFilled = exports.LockOpenRegular = exports.LockOpenFilled = exports.LockMultipleRegular = exports.LockMultipleFilled = exports.LockClosedRibbonRegular = exports.LockClosedRibbonFilled = exports.LockClosedKeyRegular = exports.LockClosedKeyFilled = exports.LockClosedRegular = exports.LockClosedFilled = exports.LocationTargetSquareRegular = exports.LocationTargetSquareFilled = exports.LocationSettingsRegular = exports.LocationSettingsFilled = exports.LocationRippleRegular = exports.LocationRippleFilled = exports.LocationOffRegular = exports.LocationOffFilled = exports.LocationLiveRegular = exports.LocationLiveFilled = exports.LocationDismissRegular = exports.LocationDismissFilled = exports.LocationCheckmarkRegular = exports.LocationCheckmarkFilled = exports.LocationArrowUpRegular = exports.LocationArrowUpFilled = exports.LocationArrowRightRegular = exports.LocationArrowRightFilled = exports.LocationArrowLeftRegular = void 0;
exports.MailOpenPersonFilled = exports.MailOffRegular = exports.MailOffFilled = exports.MailMultipleRegular = exports.MailMultipleFilled = exports.MailListRegular = exports.MailListFilled = exports.MailLinkRegular = exports.MailLinkFilled = exports.MailInboxPersonRegular = exports.MailInboxPersonFilled = exports.MailInboxDismissRegular = exports.MailInboxDismissFilled = exports.MailInboxCheckmarkRegular = exports.MailInboxCheckmarkFilled = exports.MailInboxArrowUpRegular = exports.MailInboxArrowUpFilled = exports.MailInboxArrowRightRegular = exports.MailInboxArrowRightFilled = exports.MailInboxArrowDownRegular = exports.MailInboxArrowDownFilled = exports.MailInboxAllRegular = exports.MailInboxAllFilled = exports.MailInboxAddRegular = exports.MailInboxAddFilled = exports.MailInboxRegular = exports.MailInboxFilled = exports.MailFishHookRegular = exports.MailFishHookFilled = exports.MailErrorRegular = exports.MailErrorFilled = exports.MailEditRegular = exports.MailEditFilled = exports.MailDismissRegular = exports.MailDismissFilled = exports.MailDataBarRegular = exports.MailDataBarFilled = exports.MailCopyRegular = exports.MailCopyFilled = exports.MailClockRegular = exports.MailClockFilled = exports.MailCheckmarkRegular = exports.MailCheckmarkFilled = exports.MailAttachRegular = exports.MailAttachFilled = exports.MailArrowUpRegular = exports.MailArrowUpFilled = exports.MailArrowForwardRegular = exports.MailArrowForwardFilled = exports.MailArrowDownRegular = void 0;
exports.MegaphoneCircleFilled = exports.MegaphoneRegular = exports.MegaphoneFilled = exports.MeetNowRegular = exports.MeetNowFilled = exports.MaximizeRegular = exports.MaximizeFilled = exports.MathSymbolsRegular = exports.MathSymbolsFilled = exports.MathFormulaSparkleRegular = exports.MathFormulaSparkleFilled = exports.MathFormulaRegular = exports.MathFormulaFilled = exports.MathFormatProfessionalRegular = exports.MathFormatProfessionalFilled = exports.MathFormatLinearRegular = exports.MathFormatLinearFilled = exports.MatchAppLayoutRegular = exports.MatchAppLayoutFilled = exports.MarkdownRegular = exports.MarkdownFilled = exports.MapDriveRegular = exports.MapDriveFilled = exports.MapRegular = exports.MapFilled = exports.MailboxRegular = exports.MailboxFilled = exports.MailWarningRegular = exports.MailWarningFilled = exports.MailUnreadRegular = exports.MailUnreadFilled = exports.MailTemplateRegular = exports.MailTemplateFilled = exports.MailShieldRegular = exports.MailShieldFilled = exports.MailSettingsRegular = exports.MailSettingsFilled = exports.MailRewindRegular = exports.MailRewindFilled = exports.MailReadMultipleRegular = exports.MailReadMultipleFilled = exports.MailReadBriefcaseRegular = exports.MailReadBriefcaseFilled = exports.MailReadRegular = exports.MailReadFilled = exports.MailProhibitedRegular = exports.MailProhibitedFilled = exports.MailPauseRegular = exports.MailPauseFilled = exports.MailOpenPersonRegular = void 0;
exports.MoneyHandFilled = exports.MoneyDismissRegular = exports.MoneyDismissFilled = exports.MoneyCalculatorRegular = exports.MoneyCalculatorFilled = exports.MoneyRegular = exports.MoneyFilled = exports.MoleculeRegular = exports.MoleculeFilled = exports.MoldRegular = exports.MoldFilled = exports.MobileOptimizedRegular = exports.MobileOptimizedFilled = exports.MidiRegular = exports.MidiFilled = exports.MicroscopeRegular = exports.MicroscopeFilled = exports.MicSyncRegular = exports.MicSyncFilled = exports.MicSparkleRegular = exports.MicSparkleFilled = exports.MicSettingsRegular = exports.MicSettingsFilled = exports.MicRecordRegular = exports.MicRecordFilled = exports.MicPulseOffRegular = exports.MicPulseOffFilled = exports.MicPulseRegular = exports.MicPulseFilled = exports.MicProhibitedRegular = exports.MicProhibitedFilled = exports.MicOffRegular = exports.MicOffFilled = exports.MicLinkRegular = exports.MicLinkFilled = exports.MicRegular = exports.MicFilled = exports.MergeRegular = exports.MergeFilled = exports.MentionBracketsRegular = exports.MentionBracketsFilled = exports.MentionArrowDownRegular = exports.MentionArrowDownFilled = exports.MentionRegular = exports.MentionFilled = exports.MegaphoneOffRegular = exports.MegaphoneOffFilled = exports.MegaphoneLoudRegular = exports.MegaphoneLoudFilled = exports.MegaphoneCircleRegular = void 0;
const createFluentFontIcon_1 = require("../../utils/fonts/createFluentFontIcon");
exports.ImageRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageRegular", "", 2, undefined));
exports.ImageAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageAddFilled", "", 2, undefined));
exports.ImageAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageAddRegular", "", 2, undefined));
exports.ImageAltTextFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageAltTextFilled", "", 2, undefined));
exports.ImageAltTextRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageAltTextRegular", "", 2, undefined));
exports.ImageArrowBackFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageArrowBackFilled", "", 2, undefined));
exports.ImageArrowBackRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageArrowBackRegular", "", 2, undefined));
exports.ImageArrowCounterclockwiseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageArrowCounterclockwiseFilled", "", 2, undefined));
exports.ImageArrowCounterclockwiseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageArrowCounterclockwiseRegular", "", 2, undefined));
exports.ImageArrowForwardFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageArrowForwardFilled", "", 2, undefined));
exports.ImageArrowForwardRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageArrowForwardRegular", "", 2, undefined));
exports.ImageBorderFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageBorderFilled", "", 2, undefined));
exports.ImageBorderRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageBorderRegular", "", 2, undefined));
exports.ImageCircleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageCircleFilled", "", 2, undefined));
exports.ImageCircleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageCircleRegular", "", 2, undefined));
exports.ImageCopyFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageCopyFilled", "", 2, undefined));
exports.ImageCopyRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageCopyRegular", "", 2, undefined));
exports.ImageEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageEditFilled", "", 2, undefined));
exports.ImageEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageEditRegular", "", 2, undefined));
exports.ImageGlobeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageGlobeFilled", "", 2, undefined));
exports.ImageGlobeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageGlobeRegular", "", 2, undefined));
exports.ImageMultipleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageMultipleFilled", "", 2, undefined));
exports.ImageMultipleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageMultipleRegular", "", 2, undefined));
exports.ImageMultipleOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageMultipleOffFilled", "", 2, undefined));
exports.ImageMultipleOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageMultipleOffRegular", "", 2, undefined));
exports.ImageOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageOffFilled", "", 2, undefined));
exports.ImageOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageOffRegular", "", 2, undefined));
exports.ImageProhibitedFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageProhibitedFilled", "", 2, undefined));
exports.ImageProhibitedRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageProhibitedRegular", "", 2, undefined));
exports.ImageReflectionFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageReflectionFilled", "", 2, undefined));
exports.ImageReflectionRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageReflectionRegular", "", 2, undefined));
exports.ImageSearchFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageSearchFilled", "", 2, undefined));
exports.ImageSearchRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageSearchRegular", "", 2, undefined));
exports.ImageShadowFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageShadowFilled", "", 2, undefined));
exports.ImageShadowRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageShadowRegular", "", 2, undefined));
exports.ImageSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageSparkleFilled", "", 2, undefined));
exports.ImageSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageSparkleRegular", "", 2, undefined));
exports.ImageSplitFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageSplitFilled", "", 2, undefined));
exports.ImageSplitRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageSplitRegular", "", 2, undefined));
exports.ImageStackFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageStackFilled", "", 2, undefined));
exports.ImageStackRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageStackRegular", "", 2, undefined));
exports.ImageTableFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageTableFilled", "", 2, undefined));
exports.ImageTableRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImageTableRegular", "", 2, undefined));
exports.ImmersiveReaderFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImmersiveReaderFilled", "", 2, undefined));
exports.ImmersiveReaderRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImmersiveReaderRegular", "", 2, undefined));
exports.ImportantFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImportantFilled", "", 2, undefined));
exports.ImportantRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ImportantRegular", "", 2, undefined));
exports.IncognitoFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("IncognitoFilled", "", 2, undefined));
exports.IncognitoRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("IncognitoRegular", "", 2, undefined));
exports.InfoFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("InfoFilled", "", 2, undefined));
exports.InfoRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("InfoRegular", "", 2, undefined));
exports.InfoShieldFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("InfoShieldFilled", "", 2, undefined));
exports.InfoShieldRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("InfoShieldRegular", "", 2, undefined));
exports.InfoSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("InfoSparkleFilled", "", 2, undefined));
exports.InfoSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("InfoSparkleRegular", "", 2, undefined));
exports.InkStrokeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("InkStrokeFilled", "", 2, undefined));
exports.InkStrokeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("InkStrokeRegular", "", 2, undefined));
exports.InkStrokeArrowDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("InkStrokeArrowDownFilled", "", 2, undefined));
exports.InkStrokeArrowDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("InkStrokeArrowDownRegular", "", 2, undefined));
exports.InkStrokeArrowUpDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("InkStrokeArrowUpDownFilled", "", 2, undefined));
exports.InkStrokeArrowUpDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("InkStrokeArrowUpDownRegular", "", 2, undefined));
exports.InkingToolFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("InkingToolFilled", "", 2, undefined));
exports.InkingToolRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("InkingToolRegular", "", 2, undefined));
exports.InkingToolAccentFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("InkingToolAccentFilled", "", 2, undefined));
exports.InprivateAccountFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("InprivateAccountFilled", "", 2, undefined));
exports.InprivateAccountRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("InprivateAccountRegular", "", 2, undefined));
exports.InsertFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("InsertFilled", "", 2, undefined));
exports.InsertRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("InsertRegular", "", 2, undefined));
exports.IosChevronRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("IosChevronRightFilled", "", 2, undefined));
exports.IosChevronRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("IosChevronRightRegular", "", 2, undefined));
exports.IotFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("IotFilled", "", 2, undefined));
exports.IotRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("IotRegular", "", 2, undefined));
exports.IotAlertFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("IotAlertFilled", "", 2, undefined));
exports.IotAlertRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("IotAlertRegular", "", 2, undefined));
exports.JavascriptFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("JavascriptFilled", "", 2, undefined));
exports.JavascriptRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("JavascriptRegular", "", 2, undefined));
exports.JoystickFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("JoystickFilled", "", 2, undefined));
exports.JoystickRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("JoystickRegular", "", 2, undefined));
exports.KeyFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyFilled", "", 2, undefined));
exports.KeyRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyRegular", "", 2, undefined));
exports.KeyCommandFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyCommandFilled", "", 2, undefined));
exports.KeyCommandRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyCommandRegular", "", 2, undefined));
exports.KeyMultipleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyMultipleFilled", "", 2, undefined));
exports.KeyMultipleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyMultipleRegular", "", 2, undefined));
exports.KeyResetFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyResetFilled", "", 2, undefined));
exports.KeyResetRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyResetRegular", "", 2, undefined));
exports.Keyboard123Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Keyboard123Filled", "", 2, undefined));
exports.Keyboard123Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("Keyboard123Regular", "", 2, undefined));
exports.KeyboardFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyboardFilled", "", 2, undefined));
exports.KeyboardRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyboardRegular", "", 2, undefined));
exports.KeyboardDockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyboardDockFilled", "", 2, undefined));
exports.KeyboardDockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyboardDockRegular", "", 2, undefined));
exports.KeyboardLayoutFloatFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyboardLayoutFloatFilled", "", 2, undefined));
exports.KeyboardLayoutFloatRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyboardLayoutFloatRegular", "", 2, undefined));
exports.KeyboardLayoutOneHandedLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyboardLayoutOneHandedLeftFilled", "", 2, undefined));
exports.KeyboardLayoutOneHandedLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyboardLayoutOneHandedLeftRegular", "", 2, undefined));
exports.KeyboardLayoutResizeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyboardLayoutResizeFilled", "", 2, undefined));
exports.KeyboardLayoutResizeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyboardLayoutResizeRegular", "", 2, undefined));
exports.KeyboardLayoutSplitFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyboardLayoutSplitFilled", "", 2, undefined));
exports.KeyboardLayoutSplitRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyboardLayoutSplitRegular", "", 2, undefined));
exports.KeyboardShiftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyboardShiftFilled", "", 2, undefined));
exports.KeyboardShiftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyboardShiftRegular", "", 2, undefined));
exports.KeyboardShiftUppercaseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyboardShiftUppercaseFilled", "", 2, undefined));
exports.KeyboardShiftUppercaseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyboardShiftUppercaseRegular", "", 2, undefined));
exports.KeyboardTabFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyboardTabFilled", "", 2, undefined));
exports.KeyboardTabRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("KeyboardTabRegular", "", 2, undefined));
exports.LaptopFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LaptopFilled", "", 2, undefined));
exports.LaptopRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LaptopRegular", "", 2, undefined));
exports.LaptopBriefcaseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LaptopBriefcaseFilled", "", 2, undefined));
exports.LaptopBriefcaseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LaptopBriefcaseRegular", "", 2, undefined));
exports.LaptopDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LaptopDismissFilled", "", 2, undefined));
exports.LaptopDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LaptopDismissRegular", "", 2, undefined));
exports.LaptopPersonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LaptopPersonFilled", "", 2, undefined));
exports.LaptopPersonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LaptopPersonRegular", "", 2, undefined));
exports.LaptopSettingsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LaptopSettingsFilled", "", 2, undefined));
exports.LaptopSettingsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LaptopSettingsRegular", "", 2, undefined));
exports.LaptopShieldFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LaptopShieldFilled", "", 2, undefined));
exports.LaptopShieldRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LaptopShieldRegular", "", 2, undefined));
exports.LaserToolFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LaserToolFilled", "", 2, undefined));
exports.LaserToolRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LaserToolRegular", "", 2, undefined));
exports.LassoFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LassoFilled", "", 2, undefined));
exports.LassoRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LassoRegular", "", 2, undefined));
exports.LauncherSettingsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LauncherSettingsFilled", "", 2, undefined));
exports.LauncherSettingsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LauncherSettingsRegular", "", 2, undefined));
exports.LayerFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayerFilled", "", 2, undefined));
exports.LayerRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayerRegular", "", 2, undefined));
exports.LayerDiagonalFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayerDiagonalFilled", "", 2, undefined));
exports.LayerDiagonalRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayerDiagonalRegular", "", 2, undefined));
exports.LayerDiagonalAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayerDiagonalAddFilled", "", 2, undefined));
exports.LayerDiagonalAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayerDiagonalAddRegular", "", 2, undefined));
exports.LayerDiagonalPersonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayerDiagonalPersonFilled", "", 2, undefined));
exports.LayerDiagonalPersonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayerDiagonalPersonRegular", "", 2, undefined));
exports.LayerDiagonalSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayerDiagonalSparkleFilled", "", 2, undefined));
exports.LayerDiagonalSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayerDiagonalSparkleRegular", "", 2, undefined));
exports.LayoutCellFourFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutCellFourFilled", "", 2, undefined));
exports.LayoutCellFourRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutCellFourRegular", "", 2, undefined));
exports.LayoutCellFourFocusBottomLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutCellFourFocusBottomLeftFilled", "", 2, undefined));
exports.LayoutCellFourFocusBottomRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutCellFourFocusBottomRightFilled", "", 2, undefined));
exports.LayoutCellFourFocusTopLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutCellFourFocusTopLeftFilled", "", 2, undefined));
exports.LayoutCellFourFocusTopRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutCellFourFocusTopRightFilled", "", 2, undefined));
exports.LayoutColumnFourFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnFourFilled", "", 2, undefined));
exports.LayoutColumnFourRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnFourRegular", "", 2, undefined));
exports.LayoutColumnFourFocusCenterLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnFourFocusCenterLeftFilled", "", 2, undefined));
exports.LayoutColumnFourFocusCenterRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnFourFocusCenterRightFilled", "", 2, undefined));
exports.LayoutColumnFourFocusLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnFourFocusLeftFilled", "", 2, undefined));
exports.LayoutColumnFourFocusRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnFourFocusRightFilled", "", 2, undefined));
exports.LayoutColumnOneThirdLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnOneThirdLeftFilled", "", 2, undefined));
exports.LayoutColumnOneThirdLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnOneThirdLeftRegular", "", 2, undefined));
exports.LayoutColumnOneThirdRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnOneThirdRightFilled", "", 2, undefined));
exports.LayoutColumnOneThirdRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnOneThirdRightRegular", "", 2, undefined));
exports.LayoutColumnOneThirdRightHintFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnOneThirdRightHintFilled", "", 2, undefined));
exports.LayoutColumnOneThirdRightHintRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnOneThirdRightHintRegular", "", 2, undefined));
exports.LayoutColumnThreeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnThreeFilled", "", 2, undefined));
exports.LayoutColumnThreeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnThreeRegular", "", 2, undefined));
exports.LayoutColumnThreeFocusCenterFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnThreeFocusCenterFilled", "", 2, undefined));
exports.LayoutColumnThreeFocusLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnThreeFocusLeftFilled", "", 2, undefined));
exports.LayoutColumnThreeFocusRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnThreeFocusRightFilled", "", 2, undefined));
exports.LayoutColumnTwoFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnTwoFilled", "", 2, undefined));
exports.LayoutColumnTwoRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnTwoRegular", "", 2, undefined));
exports.LayoutColumnTwoFocusLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnTwoFocusLeftFilled", "", 2, undefined));
exports.LayoutColumnTwoFocusRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnTwoFocusRightFilled", "", 2, undefined));
exports.LayoutColumnTwoSplitLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnTwoSplitLeftFilled", "", 2, undefined));
exports.LayoutColumnTwoSplitLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnTwoSplitLeftRegular", "", 2, undefined));
exports.LayoutColumnTwoSplitLeftFocusBottomLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnTwoSplitLeftFocusBottomLeftFilled", "", 2, undefined));
exports.LayoutColumnTwoSplitLeftFocusRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnTwoSplitLeftFocusRightFilled", "", 2, undefined));
exports.LayoutColumnTwoSplitLeftFocusTopLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnTwoSplitLeftFocusTopLeftFilled", "", 2, undefined));
exports.LayoutColumnTwoSplitRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnTwoSplitRightFilled", "", 2, undefined));
exports.LayoutColumnTwoSplitRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnTwoSplitRightRegular", "", 2, undefined));
exports.LayoutColumnTwoSplitRightFocusBottomRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnTwoSplitRightFocusBottomRightFilled", "", 2, undefined));
exports.LayoutColumnTwoSplitRightFocusLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnTwoSplitRightFocusLeftFilled", "", 2, undefined));
exports.LayoutColumnTwoSplitRightFocusTopRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutColumnTwoSplitRightFocusTopRightFilled", "", 2, undefined));
exports.LayoutDynamicFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutDynamicFilled", "", 2, undefined));
exports.LayoutDynamicRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutDynamicRegular", "", 2, undefined));
exports.LayoutRowFourFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowFourFilled", "", 2, undefined));
exports.LayoutRowFourRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowFourRegular", "", 2, undefined));
exports.LayoutRowFourFocusBottomFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowFourFocusBottomFilled", "", 2, undefined));
exports.LayoutRowFourFocusCenterBottomFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowFourFocusCenterBottomFilled", "", 2, undefined));
exports.LayoutRowFourFocusCenterTopFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowFourFocusCenterTopFilled", "", 2, undefined));
exports.LayoutRowFourFocusTopFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowFourFocusTopFilled", "", 2, undefined));
exports.LayoutRowThreeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowThreeFilled", "", 2, undefined));
exports.LayoutRowThreeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowThreeRegular", "", 2, undefined));
exports.LayoutRowThreeFocusBottomFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowThreeFocusBottomFilled", "", 2, undefined));
exports.LayoutRowThreeFocusCenterFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowThreeFocusCenterFilled", "", 2, undefined));
exports.LayoutRowThreeFocusTopFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowThreeFocusTopFilled", "", 2, undefined));
exports.LayoutRowTwoFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowTwoFilled", "", 2, undefined));
exports.LayoutRowTwoRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowTwoRegular", "", 2, undefined));
exports.LayoutRowTwoFocusBottomFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowTwoFocusBottomFilled", "", 2, undefined));
exports.LayoutRowTwoFocusTopFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowTwoFocusTopFilled", "", 2, undefined));
exports.LayoutRowTwoFocusTopSettingsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowTwoFocusTopSettingsFilled", "", 2, undefined));
exports.LayoutRowTwoSettingsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowTwoSettingsFilled", "", 2, undefined));
exports.LayoutRowTwoSettingsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowTwoSettingsRegular", "", 2, undefined));
exports.LayoutRowTwoSplitBottomFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowTwoSplitBottomFilled", "", 2, undefined));
exports.LayoutRowTwoSplitBottomRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowTwoSplitBottomRegular", "", 2, undefined));
exports.LayoutRowTwoSplitBottomFocusBottomLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowTwoSplitBottomFocusBottomLeftFilled", "", 2, undefined));
exports.LayoutRowTwoSplitBottomFocusBottomRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowTwoSplitBottomFocusBottomRightFilled", "", 2, undefined));
exports.LayoutRowTwoSplitBottomFocusTopFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowTwoSplitBottomFocusTopFilled", "", 2, undefined));
exports.LayoutRowTwoSplitTopFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowTwoSplitTopFilled", "", 2, undefined));
exports.LayoutRowTwoSplitTopRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowTwoSplitTopRegular", "", 2, undefined));
exports.LayoutRowTwoSplitTopFocusBottomFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowTwoSplitTopFocusBottomFilled", "", 2, undefined));
exports.LayoutRowTwoSplitTopFocusTopLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowTwoSplitTopFocusTopLeftFilled", "", 2, undefined));
exports.LayoutRowTwoSplitTopFocusTopRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LayoutRowTwoSplitTopFocusTopRightFilled", "", 2, undefined));
exports.LeafOneFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LeafOneFilled", "", 2, undefined));
exports.LeafOneRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LeafOneRegular", "", 2, undefined));
exports.LeafThreeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LeafThreeFilled", "", 2, undefined));
exports.LeafThreeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LeafThreeRegular", "", 2, undefined));
exports.LeafTwoFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LeafTwoFilled", "", 2, undefined));
exports.LeafTwoRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LeafTwoRegular", "", 2, undefined));
exports.LearningAppFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LearningAppFilled", "", 2, undefined));
exports.LearningAppRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LearningAppRegular", "", 2, undefined));
exports.LibraryFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LibraryFilled", "", 2, undefined));
exports.LibraryRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LibraryRegular", "", 2, undefined));
exports.LightbulbFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LightbulbFilled", "", 2, undefined));
exports.LightbulbRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LightbulbRegular", "", 2, undefined));
exports.LightbulbCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LightbulbCheckmarkFilled", "", 2, undefined));
exports.LightbulbCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LightbulbCheckmarkRegular", "", 2, undefined));
exports.LightbulbCircleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LightbulbCircleFilled", "", 2, undefined));
exports.LightbulbCircleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LightbulbCircleRegular", "", 2, undefined));
exports.LightbulbFilamentFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LightbulbFilamentFilled", "", 2, undefined));
exports.LightbulbFilamentRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LightbulbFilamentRegular", "", 2, undefined));
exports.LightbulbPersonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LightbulbPersonFilled", "", 2, undefined));
exports.LightbulbPersonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LightbulbPersonRegular", "", 2, undefined));
exports.LikertFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LikertFilled", "", 2, undefined));
exports.LikertRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LikertRegular", "", 2, undefined));
exports.LineFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineFilled", "", 2, undefined));
exports.LineRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineRegular", "", 2, undefined));
exports.LineDashesFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineDashesFilled", "", 2, undefined));
exports.LineDashesRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineDashesRegular", "", 2, undefined));
exports.LineFlowDiagonalUpRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineFlowDiagonalUpRightFilled", "", 2, undefined, { flipInRtl: true }));
exports.LineFlowDiagonalUpRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineFlowDiagonalUpRightRegular", "", 2, undefined, { flipInRtl: true }));
exports.LineHorizontal1Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineHorizontal1Filled", "", 2, undefined));
exports.LineHorizontal1Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineHorizontal1Regular", "", 2, undefined));
exports.LineHorizontal1DashDotDashFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineHorizontal1DashDotDashFilled", "", 2, undefined));
exports.LineHorizontal1DashDotDashRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineHorizontal1DashDotDashRegular", "", 2, undefined));
exports.LineHorizontal1DashesFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineHorizontal1DashesFilled", "", 2, undefined));
exports.LineHorizontal1DashesRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineHorizontal1DashesRegular", "", 2, undefined));
exports.LineHorizontal1DotFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineHorizontal1DotFilled", "", 2, undefined));
exports.LineHorizontal1DotRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineHorizontal1DotRegular", "", 2, undefined));
exports.LineHorizontal2DashesSolidFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineHorizontal2DashesSolidFilled", "", 2, undefined));
exports.LineHorizontal2DashesSolidRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineHorizontal2DashesSolidRegular", "", 2, undefined));
exports.LineHorizontal3Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineHorizontal3Filled", "", 2, undefined));
exports.LineHorizontal3Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineHorizontal3Regular", "", 2, undefined));
exports.LineHorizontal4Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineHorizontal4Filled", "", 2, undefined));
exports.LineHorizontal4Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineHorizontal4Regular", "", 2, undefined));
exports.LineHorizontal4SearchFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineHorizontal4SearchFilled", "", 2, undefined));
exports.LineHorizontal4SearchRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineHorizontal4SearchRegular", "", 2, undefined));
exports.LineHorizontal5Filled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineHorizontal5Filled", "", 2, undefined));
exports.LineHorizontal5Regular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineHorizontal5Regular", "", 2, undefined));
exports.LineHorizontal5ErrorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineHorizontal5ErrorFilled", "", 2, undefined));
exports.LineHorizontal5ErrorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineHorizontal5ErrorRegular", "", 2, undefined));
exports.LineStyleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineStyleFilled", "", 2, undefined));
exports.LineStyleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineStyleRegular", "", 2, undefined));
exports.LineStyleSketchFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineStyleSketchFilled", "", 2, undefined));
exports.LineStyleSketchRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineStyleSketchRegular", "", 2, undefined));
exports.LineThicknessFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineThicknessFilled", "", 2, undefined));
exports.LineThicknessRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LineThicknessRegular", "", 2, undefined));
exports.LinkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LinkFilled", "", 2, undefined));
exports.LinkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LinkRegular", "", 2, undefined));
exports.LinkAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LinkAddFilled", "", 2, undefined));
exports.LinkAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LinkAddRegular", "", 2, undefined));
exports.LinkDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LinkDismissFilled", "", 2, undefined));
exports.LinkDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LinkDismissRegular", "", 2, undefined));
exports.LinkEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LinkEditFilled", "", 2, undefined));
exports.LinkEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LinkEditRegular", "", 2, undefined));
exports.LinkMultipleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LinkMultipleFilled", "", 2, undefined));
exports.LinkMultipleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LinkMultipleRegular", "", 2, undefined));
exports.LinkPersonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LinkPersonFilled", "", 2, undefined));
exports.LinkPersonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LinkPersonRegular", "", 2, undefined));
exports.LinkSquareFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LinkSquareFilled", "", 2, undefined));
exports.LinkSquareRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LinkSquareRegular", "", 2, undefined));
exports.LinkToolboxFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LinkToolboxFilled", "", 2, undefined));
exports.LinkToolboxRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LinkToolboxRegular", "", 2, undefined));
exports.ListFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ListFilled", "", 2, undefined));
exports.ListRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ListRegular", "", 2, undefined));
exports.ListBarFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ListBarFilled", "", 2, undefined));
exports.ListBarRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ListBarRegular", "", 2, undefined));
exports.ListBarTreeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ListBarTreeFilled", "", 2, undefined));
exports.ListBarTreeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ListBarTreeRegular", "", 2, undefined));
exports.ListBarTreeOffsetFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ListBarTreeOffsetFilled", "", 2, undefined));
exports.ListBarTreeOffsetRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ListBarTreeOffsetRegular", "", 2, undefined));
exports.ListRtlFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ListRtlFilled", "", 2, undefined));
exports.ListRtlRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("ListRtlRegular", "", 2, undefined));
exports.LiveFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LiveFilled", "", 2, undefined));
exports.LiveRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LiveRegular", "", 2, undefined));
exports.LiveOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LiveOffFilled", "", 2, undefined));
exports.LiveOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LiveOffRegular", "", 2, undefined));
exports.LocalLanguageFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocalLanguageFilled", "", 2, undefined));
exports.LocalLanguageRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocalLanguageRegular", "", 2, undefined));
exports.LocationFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationFilled", "", 2, undefined));
exports.LocationRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationRegular", "", 2, undefined));
exports.LocationAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationAddFilled", "", 2, undefined));
exports.LocationAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationAddRegular", "", 2, undefined));
exports.LocationAddLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationAddLeftFilled", "", 2, undefined));
exports.LocationAddLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationAddLeftRegular", "", 2, undefined));
exports.LocationAddRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationAddRightFilled", "", 2, undefined));
exports.LocationAddRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationAddRightRegular", "", 2, undefined));
exports.LocationAddUpFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationAddUpFilled", "", 2, undefined));
exports.LocationAddUpRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationAddUpRegular", "", 2, undefined));
exports.LocationArrowFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationArrowFilled", "", 2, undefined));
exports.LocationArrowRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationArrowRegular", "", 2, undefined));
exports.LocationArrowLeftFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationArrowLeftFilled", "", 2, undefined));
exports.LocationArrowLeftRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationArrowLeftRegular", "", 2, undefined));
exports.LocationArrowRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationArrowRightFilled", "", 2, undefined));
exports.LocationArrowRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationArrowRightRegular", "", 2, undefined));
exports.LocationArrowUpFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationArrowUpFilled", "", 2, undefined));
exports.LocationArrowUpRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationArrowUpRegular", "", 2, undefined));
exports.LocationCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationCheckmarkFilled", "", 2, undefined));
exports.LocationCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationCheckmarkRegular", "", 2, undefined));
exports.LocationDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationDismissFilled", "", 2, undefined));
exports.LocationDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationDismissRegular", "", 2, undefined));
exports.LocationLiveFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationLiveFilled", "", 2, undefined));
exports.LocationLiveRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationLiveRegular", "", 2, undefined));
exports.LocationOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationOffFilled", "", 2, undefined));
exports.LocationOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationOffRegular", "", 2, undefined));
exports.LocationRippleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationRippleFilled", "", 2, undefined));
exports.LocationRippleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationRippleRegular", "", 2, undefined));
exports.LocationSettingsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationSettingsFilled", "", 2, undefined));
exports.LocationSettingsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationSettingsRegular", "", 2, undefined));
exports.LocationTargetSquareFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationTargetSquareFilled", "", 2, undefined));
exports.LocationTargetSquareRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LocationTargetSquareRegular", "", 2, undefined));
exports.LockClosedFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LockClosedFilled", "", 2, undefined));
exports.LockClosedRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LockClosedRegular", "", 2, undefined));
exports.LockClosedKeyFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LockClosedKeyFilled", "", 2, undefined));
exports.LockClosedKeyRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LockClosedKeyRegular", "", 2, undefined));
exports.LockClosedRibbonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LockClosedRibbonFilled", "", 2, undefined));
exports.LockClosedRibbonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LockClosedRibbonRegular", "", 2, undefined));
exports.LockMultipleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LockMultipleFilled", "", 2, undefined));
exports.LockMultipleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LockMultipleRegular", "", 2, undefined));
exports.LockOpenFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LockOpenFilled", "", 2, undefined));
exports.LockOpenRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LockOpenRegular", "", 2, undefined));
exports.LockShieldFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LockShieldFilled", "", 2, undefined));
exports.LockShieldRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LockShieldRegular", "", 2, undefined));
exports.LotteryFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LotteryFilled", "", 2, undefined));
exports.LotteryRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LotteryRegular", "", 2, undefined));
exports.LuggageFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LuggageFilled", "", 2, undefined));
exports.LuggageRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("LuggageRegular", "", 2, undefined));
exports.MailFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailFilled", "", 2, undefined));
exports.MailRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailRegular", "", 2, undefined));
exports.MailAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailAddFilled", "", 2, undefined));
exports.MailAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailAddRegular", "", 2, undefined));
exports.MailAlertFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailAlertFilled", "", 2, undefined));
exports.MailAlertRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailAlertRegular", "", 2, undefined));
exports.MailAllReadFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailAllReadFilled", "", 2, undefined));
exports.MailAllReadRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailAllReadRegular", "", 2, undefined));
exports.MailAllUnreadFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailAllUnreadFilled", "", 2, undefined));
exports.MailAllUnreadRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailAllUnreadRegular", "", 2, undefined));
exports.MailArrowClockwiseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailArrowClockwiseFilled", "", 2, undefined));
exports.MailArrowClockwiseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailArrowClockwiseRegular", "", 2, undefined));
exports.MailArrowDoubleBackFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailArrowDoubleBackFilled", "", 2, undefined));
exports.MailArrowDoubleBackRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailArrowDoubleBackRegular", "", 2, undefined));
exports.MailArrowDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailArrowDownFilled", "", 2, undefined));
exports.MailArrowDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailArrowDownRegular", "", 2, undefined));
exports.MailArrowForwardFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailArrowForwardFilled", "", 2, undefined));
exports.MailArrowForwardRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailArrowForwardRegular", "", 2, undefined));
exports.MailArrowUpFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailArrowUpFilled", "", 2, undefined));
exports.MailArrowUpRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailArrowUpRegular", "", 2, undefined));
exports.MailAttachFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailAttachFilled", "", 2, undefined));
exports.MailAttachRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailAttachRegular", "", 2, undefined));
exports.MailCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailCheckmarkFilled", "", 2, undefined));
exports.MailCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailCheckmarkRegular", "", 2, undefined));
exports.MailClockFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailClockFilled", "", 2, undefined));
exports.MailClockRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailClockRegular", "", 2, undefined));
exports.MailCopyFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailCopyFilled", "", 2, undefined));
exports.MailCopyRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailCopyRegular", "", 2, undefined));
exports.MailDataBarFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailDataBarFilled", "", 2, undefined));
exports.MailDataBarRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailDataBarRegular", "", 2, undefined));
exports.MailDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailDismissFilled", "", 2, undefined));
exports.MailDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailDismissRegular", "", 2, undefined));
exports.MailEditFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailEditFilled", "", 2, undefined));
exports.MailEditRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailEditRegular", "", 2, undefined));
exports.MailErrorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailErrorFilled", "", 2, undefined));
exports.MailErrorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailErrorRegular", "", 2, undefined));
exports.MailFishHookFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailFishHookFilled", "", 2, undefined));
exports.MailFishHookRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailFishHookRegular", "", 2, undefined));
exports.MailInboxFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailInboxFilled", "", 2, undefined));
exports.MailInboxRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailInboxRegular", "", 2, undefined));
exports.MailInboxAddFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailInboxAddFilled", "", 2, undefined));
exports.MailInboxAddRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailInboxAddRegular", "", 2, undefined));
exports.MailInboxAllFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailInboxAllFilled", "", 2, undefined));
exports.MailInboxAllRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailInboxAllRegular", "", 2, undefined));
exports.MailInboxArrowDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailInboxArrowDownFilled", "", 2, undefined));
exports.MailInboxArrowDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailInboxArrowDownRegular", "", 2, undefined));
exports.MailInboxArrowRightFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailInboxArrowRightFilled", "", 2, undefined));
exports.MailInboxArrowRightRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailInboxArrowRightRegular", "", 2, undefined));
exports.MailInboxArrowUpFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailInboxArrowUpFilled", "", 2, undefined));
exports.MailInboxArrowUpRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailInboxArrowUpRegular", "", 2, undefined));
exports.MailInboxCheckmarkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailInboxCheckmarkFilled", "", 2, undefined));
exports.MailInboxCheckmarkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailInboxCheckmarkRegular", "", 2, undefined));
exports.MailInboxDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailInboxDismissFilled", "", 2, undefined));
exports.MailInboxDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailInboxDismissRegular", "", 2, undefined));
exports.MailInboxPersonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailInboxPersonFilled", "", 2, undefined));
exports.MailInboxPersonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailInboxPersonRegular", "", 2, undefined));
exports.MailLinkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailLinkFilled", "", 2, undefined));
exports.MailLinkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailLinkRegular", "", 2, undefined));
exports.MailListFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailListFilled", "", 2, undefined));
exports.MailListRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailListRegular", "", 2, undefined));
exports.MailMultipleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailMultipleFilled", "", 2, undefined));
exports.MailMultipleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailMultipleRegular", "", 2, undefined));
exports.MailOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailOffFilled", "", 2, undefined));
exports.MailOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailOffRegular", "", 2, undefined));
exports.MailOpenPersonFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailOpenPersonFilled", "", 2, undefined));
exports.MailOpenPersonRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailOpenPersonRegular", "", 2, undefined));
exports.MailPauseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailPauseFilled", "", 2, undefined));
exports.MailPauseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailPauseRegular", "", 2, undefined));
exports.MailProhibitedFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailProhibitedFilled", "", 2, undefined));
exports.MailProhibitedRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailProhibitedRegular", "", 2, undefined));
exports.MailReadFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailReadFilled", "", 2, undefined));
exports.MailReadRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailReadRegular", "", 2, undefined));
exports.MailReadBriefcaseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailReadBriefcaseFilled", "", 2, undefined));
exports.MailReadBriefcaseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailReadBriefcaseRegular", "", 2, undefined));
exports.MailReadMultipleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailReadMultipleFilled", "", 2, undefined));
exports.MailReadMultipleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailReadMultipleRegular", "", 2, undefined));
exports.MailRewindFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailRewindFilled", "", 2, undefined));
exports.MailRewindRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailRewindRegular", "", 2, undefined));
exports.MailSettingsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailSettingsFilled", "", 2, undefined));
exports.MailSettingsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailSettingsRegular", "", 2, undefined));
exports.MailShieldFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailShieldFilled", "", 2, undefined));
exports.MailShieldRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailShieldRegular", "", 2, undefined));
exports.MailTemplateFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailTemplateFilled", "", 2, undefined));
exports.MailTemplateRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailTemplateRegular", "", 2, undefined));
exports.MailUnreadFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailUnreadFilled", "", 2, undefined));
exports.MailUnreadRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailUnreadRegular", "", 2, undefined));
exports.MailWarningFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailWarningFilled", "", 2, undefined));
exports.MailWarningRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailWarningRegular", "", 2, undefined));
exports.MailboxFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailboxFilled", "", 2, undefined));
exports.MailboxRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MailboxRegular", "", 2, undefined));
exports.MapFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MapFilled", "", 2, undefined));
exports.MapRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MapRegular", "", 2, undefined));
exports.MapDriveFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MapDriveFilled", "", 2, undefined));
exports.MapDriveRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MapDriveRegular", "", 2, undefined));
exports.MarkdownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MarkdownFilled", "", 2, undefined));
exports.MarkdownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MarkdownRegular", "", 2, undefined));
exports.MatchAppLayoutFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MatchAppLayoutFilled", "", 2, undefined));
exports.MatchAppLayoutRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MatchAppLayoutRegular", "", 2, undefined));
exports.MathFormatLinearFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MathFormatLinearFilled", "", 2, undefined));
exports.MathFormatLinearRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MathFormatLinearRegular", "", 2, undefined));
exports.MathFormatProfessionalFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MathFormatProfessionalFilled", "", 2, undefined));
exports.MathFormatProfessionalRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MathFormatProfessionalRegular", "", 2, undefined));
exports.MathFormulaFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MathFormulaFilled", "", 2, undefined));
exports.MathFormulaRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MathFormulaRegular", "", 2, undefined));
exports.MathFormulaSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MathFormulaSparkleFilled", "", 2, undefined));
exports.MathFormulaSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MathFormulaSparkleRegular", "", 2, undefined));
exports.MathSymbolsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MathSymbolsFilled", "", 2, undefined));
exports.MathSymbolsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MathSymbolsRegular", "", 2, undefined));
exports.MaximizeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MaximizeFilled", "", 2, undefined));
exports.MaximizeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MaximizeRegular", "", 2, undefined));
exports.MeetNowFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MeetNowFilled", "", 2, undefined));
exports.MeetNowRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MeetNowRegular", "", 2, undefined));
exports.MegaphoneFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MegaphoneFilled", "", 2, undefined));
exports.MegaphoneRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MegaphoneRegular", "", 2, undefined));
exports.MegaphoneCircleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MegaphoneCircleFilled", "", 2, undefined));
exports.MegaphoneCircleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MegaphoneCircleRegular", "", 2, undefined));
exports.MegaphoneLoudFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MegaphoneLoudFilled", "", 2, undefined));
exports.MegaphoneLoudRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MegaphoneLoudRegular", "", 2, undefined));
exports.MegaphoneOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MegaphoneOffFilled", "", 2, undefined));
exports.MegaphoneOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MegaphoneOffRegular", "", 2, undefined));
exports.MentionFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MentionFilled", "", 2, undefined));
exports.MentionRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MentionRegular", "", 2, undefined));
exports.MentionArrowDownFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MentionArrowDownFilled", "", 2, undefined));
exports.MentionArrowDownRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MentionArrowDownRegular", "", 2, undefined));
exports.MentionBracketsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MentionBracketsFilled", "", 2, undefined));
exports.MentionBracketsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MentionBracketsRegular", "", 2, undefined));
exports.MergeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MergeFilled", "", 2, undefined));
exports.MergeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MergeRegular", "", 2, undefined));
exports.MicFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicFilled", "", 2, undefined));
exports.MicRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicRegular", "", 2, undefined));
exports.MicLinkFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicLinkFilled", "", 2, undefined));
exports.MicLinkRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicLinkRegular", "", 2, undefined));
exports.MicOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicOffFilled", "", 2, undefined));
exports.MicOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicOffRegular", "", 2, undefined));
exports.MicProhibitedFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicProhibitedFilled", "", 2, undefined));
exports.MicProhibitedRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicProhibitedRegular", "", 2, undefined));
exports.MicPulseFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicPulseFilled", "", 2, undefined));
exports.MicPulseRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicPulseRegular", "", 2, undefined));
exports.MicPulseOffFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicPulseOffFilled", "", 2, undefined));
exports.MicPulseOffRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicPulseOffRegular", "", 2, undefined));
exports.MicRecordFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicRecordFilled", "", 2, undefined));
exports.MicRecordRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicRecordRegular", "", 2, undefined));
exports.MicSettingsFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicSettingsFilled", "", 2, undefined));
exports.MicSettingsRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicSettingsRegular", "", 2, undefined));
exports.MicSparkleFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicSparkleFilled", "", 2, undefined));
exports.MicSparkleRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicSparkleRegular", "", 2, undefined));
exports.MicSyncFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicSyncFilled", "", 2, undefined));
exports.MicSyncRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicSyncRegular", "", 2, undefined));
exports.MicroscopeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicroscopeFilled", "", 2, undefined));
exports.MicroscopeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MicroscopeRegular", "", 2, undefined));
exports.MidiFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MidiFilled", "", 2, undefined));
exports.MidiRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MidiRegular", "", 2, undefined));
exports.MobileOptimizedFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MobileOptimizedFilled", "", 2, undefined));
exports.MobileOptimizedRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MobileOptimizedRegular", "", 2, undefined));
exports.MoldFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoldFilled", "", 2, undefined));
exports.MoldRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoldRegular", "", 2, undefined));
exports.MoleculeFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoleculeFilled", "", 2, undefined));
exports.MoleculeRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoleculeRegular", "", 2, undefined));
exports.MoneyFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoneyFilled", "", 2, undefined));
exports.MoneyRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoneyRegular", "", 2, undefined));
exports.MoneyCalculatorFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoneyCalculatorFilled", "", 2, undefined));
exports.MoneyCalculatorRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoneyCalculatorRegular", "", 2, undefined));
exports.MoneyDismissFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoneyDismissFilled", "", 2, undefined));
exports.MoneyDismissRegular = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoneyDismissRegular", "", 2, undefined));
exports.MoneyHandFilled = ( /*#__PURE__*/createFluentFontIcon_1.createFluentFontIcon("MoneyHandFilled", "", 2, undefined));
