import type { FluentFontIcon } from "../../utils/fonts/createFluentFontIcon";
export declare const Timer2Filled: FluentFontIcon;
export declare const Timer2Regular: FluentFontIcon;
export declare const TimerFilled: FluentFontIcon;
export declare const TimerRegular: FluentFontIcon;
export declare const Timer3Filled: FluentFontIcon;
export declare const Timer3Regular: FluentFontIcon;
export declare const TimerOffFilled: FluentFontIcon;
export declare const TimerOffRegular: FluentFontIcon;
export declare const ToggleLeftFilled: FluentFontIcon;
export declare const ToggleLeftRegular: FluentFontIcon;
export declare const ToggleMultipleFilled: FluentFontIcon;
export declare const ToggleMultipleRegular: FluentFontIcon;
export declare const ToggleRightFilled: FluentFontIcon;
export declare const ToggleRightRegular: FluentFontIcon;
export declare const ToolboxFilled: FluentFontIcon;
export declare const ToolboxRegular: FluentFontIcon;
export declare const TooltipQuoteFilled: FluentFontIcon;
export declare const TooltipQuoteRegular: FluentFontIcon;
export declare const TooltipQuoteOffFilled: FluentFontIcon;
export declare const TooltipQuoteOffRegular: FluentFontIcon;
export declare const TopSpeedFilled: FluentFontIcon;
export declare const TopSpeedRegular: FluentFontIcon;
export declare const TranslateFilled: FluentFontIcon;
export declare const TranslateRegular: FluentFontIcon;
export declare const TranslateAutoFilled: FluentFontIcon;
export declare const TranslateAutoRegular: FluentFontIcon;
export declare const TranslateOffFilled: FluentFontIcon;
export declare const TranslateOffRegular: FluentFontIcon;
export declare const TransmissionFilled: FluentFontIcon;
export declare const TransmissionRegular: FluentFontIcon;
export declare const TransparencySquareFilled: FluentFontIcon;
export declare const TransparencySquareRegular: FluentFontIcon;
export declare const TrayItemAddFilled: FluentFontIcon;
export declare const TrayItemAddRegular: FluentFontIcon;
export declare const TrayItemRemoveFilled: FluentFontIcon;
export declare const TrayItemRemoveRegular: FluentFontIcon;
export declare const TreeDeciduousFilled: FluentFontIcon;
export declare const TreeDeciduousRegular: FluentFontIcon;
export declare const TreeEvergreenFilled: FluentFontIcon;
export declare const TreeEvergreenRegular: FluentFontIcon;
export declare const TriangleFilled: FluentFontIcon;
export declare const TriangleRegular: FluentFontIcon;
export declare const TriangleDownFilled: FluentFontIcon;
export declare const TriangleDownRegular: FluentFontIcon;
export declare const TriangleLeftFilled: FluentFontIcon;
export declare const TriangleLeftRegular: FluentFontIcon;
export declare const TriangleRightFilled: FluentFontIcon;
export declare const TriangleRightRegular: FluentFontIcon;
export declare const TriangleUpFilled: FluentFontIcon;
export declare const TriangleUpRegular: FluentFontIcon;
export declare const TrophyFilled: FluentFontIcon;
export declare const TrophyRegular: FluentFontIcon;
export declare const TrophyLockFilled: FluentFontIcon;
export declare const TrophyLockRegular: FluentFontIcon;
export declare const TrophyOffFilled: FluentFontIcon;
export declare const TrophyOffRegular: FluentFontIcon;
export declare const TvFilled: FluentFontIcon;
export declare const TvRegular: FluentFontIcon;
export declare const TvArrowRightFilled: FluentFontIcon;
export declare const TvArrowRightRegular: FluentFontIcon;
export declare const TvUsbFilled: FluentFontIcon;
export declare const TvUsbRegular: FluentFontIcon;
export declare const UmbrellaFilled: FluentFontIcon;
export declare const UmbrellaRegular: FluentFontIcon;
export declare const UninstallAppFilled: FluentFontIcon;
export declare const UninstallAppRegular: FluentFontIcon;
export declare const UsbPlugFilled: FluentFontIcon;
export declare const UsbPlugRegular: FluentFontIcon;
export declare const UsbStickFilled: FluentFontIcon;
export declare const UsbStickRegular: FluentFontIcon;
export declare const VaultFilled: FluentFontIcon;
export declare const VaultRegular: FluentFontIcon;
export declare const VehicleBicycleFilled: FluentFontIcon;
export declare const VehicleBicycleRegular: FluentFontIcon;
export declare const VehicleBusFilled: FluentFontIcon;
export declare const VehicleBusRegular: FluentFontIcon;
export declare const VehicleCabFilled: FluentFontIcon;
export declare const VehicleCabRegular: FluentFontIcon;
export declare const VehicleCableCarFilled: FluentFontIcon;
export declare const VehicleCableCarRegular: FluentFontIcon;
export declare const VehicleCarFilled: FluentFontIcon;
export declare const VehicleCarRegular: FluentFontIcon;
export declare const VehicleCarCollisionFilled: FluentFontIcon;
export declare const VehicleCarCollisionRegular: FluentFontIcon;
export declare const VehicleCarParkingFilled: FluentFontIcon;
export declare const VehicleCarParkingRegular: FluentFontIcon;
export declare const VehicleCarProfileFilled: FluentFontIcon;
export declare const VehicleCarProfileRegular: FluentFontIcon;
export declare const VehicleCarProfileLtrFilled: FluentFontIcon;
export declare const VehicleCarProfileLtrRegular: FluentFontIcon;
export declare const VehicleCarProfileLtrClockFilled: FluentFontIcon;
export declare const VehicleCarProfileLtrClockRegular: FluentFontIcon;
export declare const VehicleCarProfileRtlFilled: FluentFontIcon;
export declare const VehicleCarProfileRtlRegular: FluentFontIcon;
export declare const VehicleMotorcycleFilled: FluentFontIcon;
export declare const VehicleMotorcycleRegular: FluentFontIcon;
export declare const VehicleShipFilled: FluentFontIcon;
export declare const VehicleShipRegular: FluentFontIcon;
export declare const VehicleSubwayFilled: FluentFontIcon;
export declare const VehicleSubwayRegular: FluentFontIcon;
export declare const VehicleSubwayClockFilled: FluentFontIcon;
export declare const VehicleSubwayClockRegular: FluentFontIcon;
export declare const VehicleTractorFilled: FluentFontIcon;
export declare const VehicleTractorRegular: FluentFontIcon;
export declare const VehicleTruckFilled: FluentFontIcon;
export declare const VehicleTruckRegular: FluentFontIcon;
export declare const VehicleTruckBagFilled: FluentFontIcon;
export declare const VehicleTruckBagRegular: FluentFontIcon;
export declare const VehicleTruckCheckmarkFilled: FluentFontIcon;
export declare const VehicleTruckCheckmarkRegular: FluentFontIcon;
export declare const VehicleTruckCubeFilled: FluentFontIcon;
export declare const VehicleTruckCubeRegular: FluentFontIcon;
export declare const VehicleTruckProfileFilled: FluentFontIcon;
export declare const VehicleTruckProfileRegular: FluentFontIcon;
export declare const VideoFilled: FluentFontIcon;
export declare const VideoRegular: FluentFontIcon;
export declare const Video360Filled: FluentFontIcon;
export declare const Video360Regular: FluentFontIcon;
export declare const Video360OffFilled: FluentFontIcon;
export declare const Video360OffRegular: FluentFontIcon;
export declare const VideoAddFilled: FluentFontIcon;
export declare const VideoAddRegular: FluentFontIcon;
export declare const VideoBackgroundEffectFilled: FluentFontIcon;
export declare const VideoBackgroundEffectRegular: FluentFontIcon;
export declare const VideoBackgroundEffectHorizontalFilled: FluentFontIcon;
export declare const VideoBackgroundEffectHorizontalRegular: FluentFontIcon;
export declare const VideoBluetoothFilled: FluentFontIcon;
export declare const VideoBluetoothRegular: FluentFontIcon;
export declare const VideoChatFilled: FluentFontIcon;
export declare const VideoChatRegular: FluentFontIcon;
export declare const VideoClipFilled: FluentFontIcon;
export declare const VideoClipRegular: FluentFontIcon;
export declare const VideoClipMultipleFilled: FluentFontIcon;
export declare const VideoClipMultipleRegular: FluentFontIcon;
export declare const VideoClipOffFilled: FluentFontIcon;
export declare const VideoClipOffRegular: FluentFontIcon;
export declare const VideoClipOptimizeFilled: FluentFontIcon;
export declare const VideoClipOptimizeRegular: FluentFontIcon;
export declare const VideoClipWandFilled: FluentFontIcon;
export declare const VideoClipWandRegular: FluentFontIcon;
export declare const VideoMultipleFilled: FluentFontIcon;
export declare const VideoMultipleRegular: FluentFontIcon;
export declare const VideoOffFilled: FluentFontIcon;
export declare const VideoOffRegular: FluentFontIcon;
export declare const VideoPersonFilled: FluentFontIcon;
export declare const VideoPersonRegular: FluentFontIcon;
export declare const VideoPersonCallFilled: FluentFontIcon;
export declare const VideoPersonCallRegular: FluentFontIcon;
export declare const VideoPersonClockFilled: FluentFontIcon;
export declare const VideoPersonClockRegular: FluentFontIcon;
export declare const VideoPersonOffFilled: FluentFontIcon;
export declare const VideoPersonOffRegular: FluentFontIcon;
export declare const VideoPersonPulseFilled: FluentFontIcon;
export declare const VideoPersonPulseRegular: FluentFontIcon;
export declare const VideoPersonSparkleFilled: FluentFontIcon;
export declare const VideoPersonSparkleRegular: FluentFontIcon;
export declare const VideoPersonSparkleOffFilled: FluentFontIcon;
export declare const VideoPersonSparkleOffRegular: FluentFontIcon;
export declare const VideoPersonStarFilled: FluentFontIcon;
export declare const VideoPersonStarRegular: FluentFontIcon;
export declare const VideoPersonStarOffFilled: FluentFontIcon;
export declare const VideoPersonStarOffRegular: FluentFontIcon;
export declare const VideoPlayPauseFilled: FluentFontIcon;
export declare const VideoPlayPauseRegular: FluentFontIcon;
export declare const VideoProhibitedFilled: FluentFontIcon;
export declare const VideoProhibitedRegular: FluentFontIcon;
export declare const VideoRecordingFilled: FluentFontIcon;
export declare const VideoRecordingRegular: FluentFontIcon;
export declare const VideoSecurityFilled: FluentFontIcon;
export declare const VideoSecurityRegular: FluentFontIcon;
export declare const VideoSettingsFilled: FluentFontIcon;
export declare const VideoSettingsRegular: FluentFontIcon;
export declare const VideoSwitchFilled: FluentFontIcon;
export declare const VideoSwitchRegular: FluentFontIcon;
export declare const VideoSyncFilled: FluentFontIcon;
export declare const VideoSyncRegular: FluentFontIcon;
export declare const VideoUsbFilled: FluentFontIcon;
export declare const VideoUsbRegular: FluentFontIcon;
export declare const ViewDesktopFilled: FluentFontIcon;
export declare const ViewDesktopRegular: FluentFontIcon;
export declare const ViewDesktopMobileFilled: FluentFontIcon;
export declare const ViewDesktopMobileRegular: FluentFontIcon;
export declare const VirtualNetworkFilled: FluentFontIcon;
export declare const VirtualNetworkRegular: FluentFontIcon;
export declare const VirtualNetworkToolboxFilled: FluentFontIcon;
export declare const VirtualNetworkToolboxRegular: FluentFontIcon;
export declare const VoicemailFilled: FluentFontIcon;
export declare const VoicemailRegular: FluentFontIcon;
export declare const VoicemailArrowBackFilled: FluentFontIcon;
export declare const VoicemailArrowBackRegular: FluentFontIcon;
export declare const VoicemailArrowForwardFilled: FluentFontIcon;
export declare const VoicemailArrowForwardRegular: FluentFontIcon;
export declare const VoicemailArrowSubtractFilled: FluentFontIcon;
export declare const VoicemailArrowSubtractRegular: FluentFontIcon;
export declare const VoicemailShieldFilled: FluentFontIcon;
export declare const VoicemailShieldRegular: FluentFontIcon;
export declare const VoicemailSubtractFilled: FluentFontIcon;
export declare const VoicemailSubtractRegular: FluentFontIcon;
export declare const VoteFilled: FluentFontIcon;
export declare const VoteRegular: FluentFontIcon;
export declare const WalkieTalkieFilled: FluentFontIcon;
export declare const WalkieTalkieRegular: FluentFontIcon;
export declare const WalletFilled: FluentFontIcon;
export declare const WalletRegular: FluentFontIcon;
export declare const WalletCreditCardFilled: FluentFontIcon;
export declare const WalletCreditCardRegular: FluentFontIcon;
export declare const WallpaperFilled: FluentFontIcon;
export declare const WallpaperRegular: FluentFontIcon;
export declare const WandFilled: FluentFontIcon;
export declare const WandRegular: FluentFontIcon;
export declare const WarningFilled: FluentFontIcon;
export declare const WarningRegular: FluentFontIcon;
export declare const WarningLockOpenFilled: FluentFontIcon;
export declare const WarningLockOpenRegular: FluentFontIcon;
export declare const WarningShieldFilled: FluentFontIcon;
export declare const WarningShieldRegular: FluentFontIcon;
export declare const WasherFilled: FluentFontIcon;
export declare const WasherRegular: FluentFontIcon;
export declare const WaterFilled: FluentFontIcon;
export declare const WaterRegular: FluentFontIcon;
export declare const WeatherBlowingSnowFilled: FluentFontIcon;
export declare const WeatherBlowingSnowRegular: FluentFontIcon;
export declare const WeatherCloudyFilled: FluentFontIcon;
export declare const WeatherCloudyRegular: FluentFontIcon;
export declare const WeatherDrizzleFilled: FluentFontIcon;
export declare const WeatherDrizzleRegular: FluentFontIcon;
export declare const WeatherDuststormFilled: FluentFontIcon;
export declare const WeatherDuststormRegular: FluentFontIcon;
export declare const WeatherFogFilled: FluentFontIcon;
export declare const WeatherFogRegular: FluentFontIcon;
export declare const WeatherHailDayFilled: FluentFontIcon;
export declare const WeatherHailDayRegular: FluentFontIcon;
export declare const WeatherHailNightFilled: FluentFontIcon;
export declare const WeatherHailNightRegular: FluentFontIcon;
export declare const WeatherHazeFilled: FluentFontIcon;
export declare const WeatherHazeRegular: FluentFontIcon;
export declare const WeatherMoonFilled: FluentFontIcon;
export declare const WeatherMoonRegular: FluentFontIcon;
export declare const WeatherMoonOffFilled: FluentFontIcon;
export declare const WeatherMoonOffRegular: FluentFontIcon;
export declare const WeatherPartlyCloudyDayFilled: FluentFontIcon;
export declare const WeatherPartlyCloudyDayRegular: FluentFontIcon;
export declare const WeatherPartlyCloudyNightFilled: FluentFontIcon;
export declare const WeatherPartlyCloudyNightRegular: FluentFontIcon;
export declare const WeatherRainFilled: FluentFontIcon;
export declare const WeatherRainRegular: FluentFontIcon;
export declare const WeatherRainShowersDayFilled: FluentFontIcon;
export declare const WeatherRainShowersDayRegular: FluentFontIcon;
export declare const WeatherRainShowersNightFilled: FluentFontIcon;
export declare const WeatherRainShowersNightRegular: FluentFontIcon;
export declare const WeatherRainSnowFilled: FluentFontIcon;
export declare const WeatherRainSnowRegular: FluentFontIcon;
export declare const WeatherSnowFilled: FluentFontIcon;
export declare const WeatherSnowRegular: FluentFontIcon;
export declare const WeatherSnowShowerDayFilled: FluentFontIcon;
export declare const WeatherSnowShowerDayRegular: FluentFontIcon;
export declare const WeatherSnowShowerNightFilled: FluentFontIcon;
export declare const WeatherSnowShowerNightRegular: FluentFontIcon;
export declare const WeatherSnowflakeFilled: FluentFontIcon;
export declare const WeatherSnowflakeRegular: FluentFontIcon;
export declare const WeatherSquallsFilled: FluentFontIcon;
export declare const WeatherSquallsRegular: FluentFontIcon;
export declare const WeatherSunnyFilled: FluentFontIcon;
export declare const WeatherSunnyRegular: FluentFontIcon;
export declare const WeatherSunnyHighFilled: FluentFontIcon;
export declare const WeatherSunnyHighRegular: FluentFontIcon;
export declare const WeatherSunnyLowFilled: FluentFontIcon;
export declare const WeatherSunnyLowRegular: FluentFontIcon;
export declare const WeatherThunderstormFilled: FluentFontIcon;
export declare const WeatherThunderstormRegular: FluentFontIcon;
export declare const WebAssetFilled: FluentFontIcon;
export declare const WebAssetRegular: FluentFontIcon;
export declare const WheelchairAccessFilled: FluentFontIcon;
export declare const WheelchairAccessRegular: FluentFontIcon;
export declare const WhiteboardFilled: FluentFontIcon;
export declare const WhiteboardRegular: FluentFontIcon;
export declare const WhiteboardOffFilled: FluentFontIcon;
export declare const WhiteboardOffRegular: FluentFontIcon;
export declare const Wifi1Filled: FluentFontIcon;
export declare const Wifi1Regular: FluentFontIcon;
export declare const Wifi2Filled: FluentFontIcon;
export declare const Wifi2Regular: FluentFontIcon;
export declare const Wifi3Filled: FluentFontIcon;
export declare const Wifi3Regular: FluentFontIcon;
export declare const Wifi4Filled: FluentFontIcon;
export declare const Wifi4Regular: FluentFontIcon;
export declare const WifiLockFilled: FluentFontIcon;
export declare const WifiLockRegular: FluentFontIcon;
export declare const WifiOffFilled: FluentFontIcon;
export declare const WifiOffRegular: FluentFontIcon;
export declare const WifiSettingsFilled: FluentFontIcon;
export declare const WifiSettingsRegular: FluentFontIcon;
export declare const WifiWarningFilled: FluentFontIcon;
export declare const WifiWarningRegular: FluentFontIcon;
export declare const WindowFilled: FluentFontIcon;
export declare const WindowRegular: FluentFontIcon;
export declare const WindowAdFilled: FluentFontIcon;
export declare const WindowAdRegular: FluentFontIcon;
export declare const WindowAdOffFilled: FluentFontIcon;
export declare const WindowAdOffRegular: FluentFontIcon;
export declare const WindowAdPersonFilled: FluentFontIcon;
export declare const WindowAdPersonRegular: FluentFontIcon;
export declare const WindowAppsFilled: FluentFontIcon;
export declare const WindowAppsRegular: FluentFontIcon;
export declare const WindowArrowUpFilled: FluentFontIcon;
export declare const WindowArrowUpRegular: FluentFontIcon;
export declare const WindowBrushFilled: FluentFontIcon;
export declare const WindowBrushRegular: FluentFontIcon;
export declare const WindowBulletListFilled: FluentFontIcon;
export declare const WindowBulletListRegular: FluentFontIcon;
export declare const WindowBulletListAddFilled: FluentFontIcon;
export declare const WindowBulletListAddRegular: FluentFontIcon;
export declare const WindowColumnOneFourthLeftFilled: FluentFontIcon;
export declare const WindowColumnOneFourthLeftRegular: FluentFontIcon;
export declare const WindowColumnOneFourthLeftFocusLeftFilled: FluentFontIcon;
export declare const WindowColumnOneFourthLeftFocusTopFilled: FluentFontIcon;
export declare const WindowConsoleFilled: FluentFontIcon;
export declare const WindowConsoleRegular: FluentFontIcon;
export declare const WindowDatabaseFilled: FluentFontIcon;
export declare const WindowDatabaseRegular: FluentFontIcon;
export declare const WindowDevEditFilled: FluentFontIcon;
export declare const WindowDevEditRegular: FluentFontIcon;
export declare const WindowDevToolsFilled: FluentFontIcon;
export declare const WindowDevToolsRegular: FluentFontIcon;
export declare const WindowEditFilled: FluentFontIcon;
export declare const WindowEditRegular: FluentFontIcon;
export declare const WindowFingerprintFilled: FluentFontIcon;
export declare const WindowFingerprintRegular: FluentFontIcon;
export declare const WindowHeaderHorizontalFilled: FluentFontIcon;
export declare const WindowHeaderHorizontalRegular: FluentFontIcon;
export declare const WindowHeaderHorizontalOffFilled: FluentFontIcon;
export declare const WindowHeaderHorizontalOffRegular: FluentFontIcon;
export declare const WindowHeaderVerticalFilled: FluentFontIcon;
export declare const WindowHeaderVerticalRegular: FluentFontIcon;
export declare const WindowInprivateFilled: FluentFontIcon;
export declare const WindowInprivateRegular: FluentFontIcon;
export declare const WindowInprivateAccountFilled: FluentFontIcon;
export declare const WindowInprivateAccountRegular: FluentFontIcon;
export declare const WindowLocationTargetFilled: FluentFontIcon;
export declare const WindowLocationTargetRegular: FluentFontIcon;
export declare const WindowMultipleFilled: FluentFontIcon;
export declare const WindowMultipleRegular: FluentFontIcon;
export declare const WindowMultipleSwapFilled: FluentFontIcon;
export declare const WindowMultipleSwapRegular: FluentFontIcon;
export declare const WindowNewFilled: FluentFontIcon;
export declare const WindowNewRegular: FluentFontIcon;
export declare const WindowPlayFilled: FluentFontIcon;
export declare const WindowPlayRegular: FluentFontIcon;
export declare const WindowSettingsFilled: FluentFontIcon;
export declare const WindowSettingsRegular: FluentFontIcon;
export declare const WindowShieldFilled: FluentFontIcon;
export declare const WindowShieldRegular: FluentFontIcon;
export declare const WindowTextFilled: FluentFontIcon;
export declare const WindowTextRegular: FluentFontIcon;
export declare const WindowWrenchFilled: FluentFontIcon;
export declare const WindowWrenchRegular: FluentFontIcon;
export declare const WrenchFilled: FluentFontIcon;
export declare const WrenchRegular: FluentFontIcon;
export declare const WrenchScrewdriverFilled: FluentFontIcon;
export declare const WrenchScrewdriverRegular: FluentFontIcon;
export declare const WrenchSettingsFilled: FluentFontIcon;
export declare const WrenchSettingsRegular: FluentFontIcon;
export declare const XboxConsoleFilled: FluentFontIcon;
export declare const XboxConsoleRegular: FluentFontIcon;
export declare const XboxControllerFilled: FluentFontIcon;
export declare const XboxControllerRegular: FluentFontIcon;
export declare const XboxControllerErrorFilled: FluentFontIcon;
export declare const XboxControllerErrorRegular: FluentFontIcon;
export declare const XrayFilled: FluentFontIcon;
export declare const XrayRegular: FluentFontIcon;
export declare const ZoomFitFilled: FluentFontIcon;
export declare const ZoomFitRegular: FluentFontIcon;
export declare const ZoomInFilled: FluentFontIcon;
export declare const ZoomInRegular: FluentFontIcon;
export declare const ZoomOutFilled: FluentFontIcon;
export declare const ZoomOutRegular: FluentFontIcon;
