import type { FluentIcon } from "../utils/createFluentIcon";
export declare const TextIndentDecreaseLtrRegular: FluentIcon;
export declare const TextIndentDecreaseLtr90Filled: FluentIcon;
export declare const TextIndentDecreaseLtr90Regular: FluentIcon;
export declare const TextIndentDecreaseLtrRotate270Filled: FluentIcon;
export declare const TextIndentDecreaseLtrRotate270Regular: FluentIcon;
export declare const TextIndentDecreaseRotate270Filled: FluentIcon;
export declare const TextIndentDecreaseRotate270Regular: FluentIcon;
export declare const TextIndentDecreaseRotate90Filled: FluentIcon;
export declare const TextIndentDecreaseRotate90Regular: FluentIcon;
export declare const TextIndentDecreaseRtlFilled: FluentIcon;
export declare const TextIndentDecreaseRtlRegular: FluentIcon;
export declare const TextIndentDecreaseRtl90Filled: FluentIcon;
export declare const TextIndentDecreaseRtl90Regular: FluentIcon;
export declare const TextIndentDecreaseRtlRotate270Filled: FluentIcon;
export declare const TextIndentDecreaseRtlRotate270Regular: FluentIcon;
export declare const TextIndentIncreaseFilled: FluentIcon;
export declare const TextIndentIncreaseRegular: FluentIcon;
export declare const TextIndentIncreaseLtrFilled: FluentIcon;
export declare const TextIndentIncreaseLtrRegular: FluentIcon;
export declare const TextIndentIncreaseLtr90Filled: FluentIcon;
export declare const TextIndentIncreaseLtr90Regular: FluentIcon;
export declare const TextIndentIncreaseLtrRotate270Filled: FluentIcon;
export declare const TextIndentIncreaseLtrRotate270Regular: FluentIcon;
export declare const TextIndentIncreaseRotate270Filled: FluentIcon;
export declare const TextIndentIncreaseRotate270Regular: FluentIcon;
export declare const TextIndentIncreaseRotate90Filled: FluentIcon;
export declare const TextIndentIncreaseRotate90Regular: FluentIcon;
export declare const TextIndentIncreaseRtlFilled: FluentIcon;
export declare const TextIndentIncreaseRtlRegular: FluentIcon;
export declare const TextIndentIncreaseRtl90Filled: FluentIcon;
export declare const TextIndentIncreaseRtl90Regular: FluentIcon;
export declare const TextIndentIncreaseRtlRotate270Filled: FluentIcon;
export declare const TextIndentIncreaseRtlRotate270Regular: FluentIcon;
export declare const TextItalicFilled: FluentIcon;
export declare const TextItalicRegular: FluentIcon;
export declare const TextLineSpacingFilled: FluentIcon;
export declare const TextLineSpacingRegular: FluentIcon;
export declare const TextListAbcLowercaseLtrFilled: FluentIcon;
export declare const TextListAbcLowercaseLtrRegular: FluentIcon;
export declare const TextListAbcUppercaseLtrFilled: FluentIcon;
export declare const TextListAbcUppercaseLtrRegular: FluentIcon;
export declare const TextListRomanNumeralLowercaseFilled: FluentIcon;
export declare const TextListRomanNumeralLowercaseRegular: FluentIcon;
export declare const TextListRomanNumeralUppercaseFilled: FluentIcon;
export declare const TextListRomanNumeralUppercaseRegular: FluentIcon;
export declare const TextMoreFilled: FluentIcon;
export declare const TextMoreRegular: FluentIcon;
export declare const TextNumberFormatFilled: FluentIcon;
export declare const TextNumberFormatRegular: FluentIcon;
export declare const TextNumberListLtrFilled: FluentIcon;
export declare const TextNumberListLtrRegular: FluentIcon;
export declare const TextNumberListLtr90Filled: FluentIcon;
export declare const TextNumberListLtr90Regular: FluentIcon;
export declare const TextNumberListLtrRotate270Filled: FluentIcon;
export declare const TextNumberListLtrRotate270Regular: FluentIcon;
export declare const TextNumberListRotate270Filled: FluentIcon;
export declare const TextNumberListRotate270Regular: FluentIcon;
export declare const TextNumberListRotate90Filled: FluentIcon;
export declare const TextNumberListRotate90Regular: FluentIcon;
export declare const TextNumberListRtlFilled: FluentIcon;
export declare const TextNumberListRtlRegular: FluentIcon;
export declare const TextNumberListRtl90Filled: FluentIcon;
export declare const TextNumberListRtl90Regular: FluentIcon;
export declare const TextNumberListRtlRotate270Filled: FluentIcon;
export declare const TextNumberListRtlRotate270Regular: FluentIcon;
export declare const TextParagraphFilled: FluentIcon;
export declare const TextParagraphRegular: FluentIcon;
export declare const TextParagraphDirectionFilled: FluentIcon;
export declare const TextParagraphDirectionRegular: FluentIcon;
export declare const TextParagraphDirectionLeftFilled: FluentIcon;
export declare const TextParagraphDirectionLeftRegular: FluentIcon;
export declare const TextParagraphDirectionRightFilled: FluentIcon;
export declare const TextParagraphDirectionRightRegular: FluentIcon;
export declare const TextPercentFilled: FluentIcon;
export declare const TextPercentRegular: FluentIcon;
export declare const TextPeriodAsteriskFilled: FluentIcon;
export declare const TextPeriodAsteriskRegular: FluentIcon;
export declare const TextPositionBehindFilled: FluentIcon;
export declare const TextPositionBehindRegular: FluentIcon;
export declare const TextPositionFrontFilled: FluentIcon;
export declare const TextPositionFrontRegular: FluentIcon;
export declare const TextPositionLineFilled: FluentIcon;
export declare const TextPositionLineRegular: FluentIcon;
export declare const TextPositionSquareFilled: FluentIcon;
export declare const TextPositionSquareRegular: FluentIcon;
export declare const TextPositionSquareLeftFilled: FluentIcon;
export declare const TextPositionSquareLeftRegular: FluentIcon;
export declare const TextPositionSquareRightFilled: FluentIcon;
export declare const TextPositionSquareRightRegular: FluentIcon;
export declare const TextPositionThroughFilled: FluentIcon;
export declare const TextPositionThroughRegular: FluentIcon;
export declare const TextPositionTightFilled: FluentIcon;
export declare const TextPositionTightRegular: FluentIcon;
export declare const TextPositionTopBottomFilled: FluentIcon;
export declare const TextPositionTopBottomRegular: FluentIcon;
export declare const TextProofingToolsFilled: FluentIcon;
export declare const TextProofingToolsRegular: FluentIcon;
export declare const TextQuoteFilled: FluentIcon;
export declare const TextQuoteRegular: FluentIcon;
export declare const TextQuoteOpeningFilled: FluentIcon;
export declare const TextQuoteOpeningRegular: FluentIcon;
export declare const TextSortAscendingFilled: FluentIcon;
export declare const TextSortAscendingRegular: FluentIcon;
export declare const TextSortDescendingFilled: FluentIcon;
export declare const TextSortDescendingRegular: FluentIcon;
export declare const TextStrikethroughFilled: FluentIcon;
export declare const TextStrikethroughRegular: FluentIcon;
export declare const TextSubscriptFilled: FluentIcon;
export declare const TextSubscriptRegular: FluentIcon;
export declare const TextSuperscriptFilled: FluentIcon;
export declare const TextSuperscriptRegular: FluentIcon;
export declare const TextTFilled: FluentIcon;
export declare const TextTRegular: FluentIcon;
export declare const TextUnderlineFilled: FluentIcon;
export declare const TextUnderlineRegular: FluentIcon;
export declare const TextUnderlineCharacterUFilled: FluentIcon;
export declare const TextUnderlineCharacterURegular: FluentIcon;
export declare const TextUnderlineDoubleFilled: FluentIcon;
export declare const TextUnderlineDoubleRegular: FluentIcon;
export declare const TextWholeWordFilled: FluentIcon;
export declare const TextWholeWordRegular: FluentIcon;
export declare const TextWordCountFilled: FluentIcon;
export declare const TextWordCountRegular: FluentIcon;
export declare const TextWrapFilled: FluentIcon;
export declare const TextWrapRegular: FluentIcon;
export declare const TextWrapOffFilled: FluentIcon;
export declare const TextWrapOffRegular: FluentIcon;
export declare const TextboxFilled: FluentIcon;
export declare const TextboxRegular: FluentIcon;
export declare const TextboxAlignBottomFilled: FluentIcon;
export declare const TextboxAlignBottomRegular: FluentIcon;
export declare const TextboxAlignBottomCenterFilled: FluentIcon;
export declare const TextboxAlignBottomCenterRegular: FluentIcon;
export declare const TextboxAlignBottomLeftFilled: FluentIcon;
export declare const TextboxAlignBottomLeftRegular: FluentIcon;
export declare const TextboxAlignBottomRightFilled: FluentIcon;
export declare const TextboxAlignBottomRightRegular: FluentIcon;
export declare const TextboxAlignBottomRotate90Filled: FluentIcon;
export declare const TextboxAlignBottomRotate90Regular: FluentIcon;
export declare const TextboxAlignCenterFilled: FluentIcon;
export declare const TextboxAlignCenterRegular: FluentIcon;
export declare const TextboxAlignMiddleFilled: FluentIcon;
export declare const TextboxAlignMiddleRegular: FluentIcon;
export declare const TextboxAlignMiddleLeftFilled: FluentIcon;
export declare const TextboxAlignMiddleLeftRegular: FluentIcon;
export declare const TextboxAlignMiddleRightFilled: FluentIcon;
export declare const TextboxAlignMiddleRightRegular: FluentIcon;
export declare const TextboxAlignMiddleRotate90Filled: FluentIcon;
export declare const TextboxAlignMiddleRotate90Regular: FluentIcon;
export declare const TextboxAlignTopFilled: FluentIcon;
export declare const TextboxAlignTopRegular: FluentIcon;
export declare const TextboxAlignTopCenterFilled: FluentIcon;
export declare const TextboxAlignTopCenterRegular: FluentIcon;
export declare const TextboxAlignTopLeftFilled: FluentIcon;
export declare const TextboxAlignTopLeftRegular: FluentIcon;
export declare const TextboxAlignTopRightFilled: FluentIcon;
export declare const TextboxAlignTopRightRegular: FluentIcon;
export declare const TextboxAlignTopRotate90Filled: FluentIcon;
export declare const TextboxAlignTopRotate90Regular: FluentIcon;
export declare const TextboxCheckmarkFilled: FluentIcon;
export declare const TextboxCheckmarkRegular: FluentIcon;
export declare const TextboxMoreFilled: FluentIcon;
export declare const TextboxMoreRegular: FluentIcon;
export declare const TextboxRotate90Filled: FluentIcon;
export declare const TextboxRotate90Regular: FluentIcon;
export declare const TextboxSettingsFilled: FluentIcon;
export declare const TextboxSettingsRegular: FluentIcon;
export declare const ThinkingFilled: FluentIcon;
export declare const ThinkingRegular: FluentIcon;
export declare const ThumbDislikeFilled: FluentIcon;
export declare const ThumbDislikeRegular: FluentIcon;
export declare const ThumbLikeFilled: FluentIcon;
export declare const ThumbLikeRegular: FluentIcon;
export declare const ThumbLikeDislikeFilled: FluentIcon;
export declare const ThumbLikeDislikeRegular: FluentIcon;
export declare const TicketDiagonalFilled: FluentIcon;
export declare const TicketDiagonalRegular: FluentIcon;
export declare const TicketHorizontalFilled: FluentIcon;
export declare const TicketHorizontalRegular: FluentIcon;
export declare const TimeAndWeatherFilled: FluentIcon;
export declare const TimeAndWeatherRegular: FluentIcon;
export declare const TimePickerFilled: FluentIcon;
export declare const TimePickerRegular: FluentIcon;
export declare const TimelineFilled: FluentIcon;
export declare const TimelineRegular: FluentIcon;
export declare const Timer10Filled: FluentIcon;
export declare const Timer10Regular: FluentIcon;
export declare const TimerFilled: FluentIcon;
export declare const TimerRegular: FluentIcon;
export declare const Timer2Filled: FluentIcon;
export declare const Timer2Regular: FluentIcon;
export declare const Timer3Filled: FluentIcon;
export declare const Timer3Regular: FluentIcon;
export declare const TimerOffFilled: FluentIcon;
export declare const TimerOffRegular: FluentIcon;
export declare const ToggleLeftFilled: FluentIcon;
export declare const ToggleLeftRegular: FluentIcon;
export declare const ToggleMultipleFilled: FluentIcon;
export declare const ToggleMultipleRegular: FluentIcon;
export declare const ToggleRightFilled: FluentIcon;
export declare const ToggleRightRegular: FluentIcon;
export declare const ToolboxColor: FluentIcon;
export declare const ToolboxFilled: FluentIcon;
export declare const ToolboxRegular: FluentIcon;
export declare const TooltipQuoteFilled: FluentIcon;
export declare const TooltipQuoteRegular: FluentIcon;
export declare const TooltipQuoteOffFilled: FluentIcon;
export declare const TooltipQuoteOffRegular: FluentIcon;
export declare const TopSpeedFilled: FluentIcon;
export declare const TopSpeedRegular: FluentIcon;
export declare const TranslateFilled: FluentIcon;
export declare const TranslateRegular: FluentIcon;
export declare const TranslateAutoFilled: FluentIcon;
export declare const TranslateAutoRegular: FluentIcon;
export declare const TranslateOffFilled: FluentIcon;
export declare const TranslateOffRegular: FluentIcon;
export declare const TransmissionFilled: FluentIcon;
export declare const TransmissionRegular: FluentIcon;
export declare const TransparencySquareFilled: FluentIcon;
export declare const TransparencySquareRegular: FluentIcon;
export declare const TrayItemAddFilled: FluentIcon;
export declare const TrayItemAddRegular: FluentIcon;
export declare const TrayItemRemoveFilled: FluentIcon;
export declare const TrayItemRemoveRegular: FluentIcon;
export declare const TreeDeciduousFilled: FluentIcon;
export declare const TreeDeciduousRegular: FluentIcon;
export declare const TreeEvergreenFilled: FluentIcon;
export declare const TreeEvergreenRegular: FluentIcon;
export declare const TriangleFilled: FluentIcon;
export declare const TriangleRegular: FluentIcon;
export declare const TriangleDownFilled: FluentIcon;
export declare const TriangleDownRegular: FluentIcon;
export declare const TriangleLeftFilled: FluentIcon;
export declare const TriangleLeftRegular: FluentIcon;
export declare const TriangleRightFilled: FluentIcon;
export declare const TriangleRightRegular: FluentIcon;
export declare const TriangleUpFilled: FluentIcon;
export declare const TriangleUpRegular: FluentIcon;
export declare const TrophyColor: FluentIcon;
export declare const TrophyFilled: FluentIcon;
export declare const TrophyRegular: FluentIcon;
export declare const TrophyLockFilled: FluentIcon;
export declare const TrophyLockRegular: FluentIcon;
export declare const TrophyOffFilled: FluentIcon;
export declare const TrophyOffRegular: FluentIcon;
export declare const TvFilled: FluentIcon;
export declare const TvRegular: FluentIcon;
export declare const TvArrowRightFilled: FluentIcon;
export declare const TvArrowRightRegular: FluentIcon;
export declare const TvUsbFilled: FluentIcon;
export declare const TvUsbRegular: FluentIcon;
export declare const UmbrellaFilled: FluentIcon;
export declare const UmbrellaRegular: FluentIcon;
export declare const UninstallAppFilled: FluentIcon;
export declare const UninstallAppRegular: FluentIcon;
export declare const UsbPlugFilled: FluentIcon;
export declare const UsbPlugRegular: FluentIcon;
export declare const UsbStickFilled: FluentIcon;
export declare const UsbStickRegular: FluentIcon;
export declare const VaultColor: FluentIcon;
export declare const VaultFilled: FluentIcon;
export declare const VaultRegular: FluentIcon;
export declare const VehicleBicycleFilled: FluentIcon;
export declare const VehicleBicycleRegular: FluentIcon;
export declare const VehicleBusFilled: FluentIcon;
export declare const VehicleBusRegular: FluentIcon;
export declare const VehicleCabFilled: FluentIcon;
export declare const VehicleCabRegular: FluentIcon;
export declare const VehicleCableCarFilled: FluentIcon;
export declare const VehicleCableCarRegular: FluentIcon;
export declare const VehicleCarFilled: FluentIcon;
export declare const VehicleCarRegular: FluentIcon;
export declare const VehicleCarCollisionFilled: FluentIcon;
export declare const VehicleCarCollisionRegular: FluentIcon;
export declare const VehicleCarParkingFilled: FluentIcon;
export declare const VehicleCarParkingRegular: FluentIcon;
export declare const VehicleCarProfileFilled: FluentIcon;
export declare const VehicleCarProfileRegular: FluentIcon;
export declare const VehicleCarProfileLtrFilled: FluentIcon;
export declare const VehicleCarProfileLtrRegular: FluentIcon;
export declare const VehicleCarProfileLtrClockFilled: FluentIcon;
export declare const VehicleCarProfileLtrClockRegular: FluentIcon;
export declare const VehicleCarProfileRtlFilled: FluentIcon;
export declare const VehicleCarProfileRtlRegular: FluentIcon;
export declare const VehicleMotorcycleFilled: FluentIcon;
export declare const VehicleMotorcycleRegular: FluentIcon;
export declare const VehicleShipFilled: FluentIcon;
export declare const VehicleShipRegular: FluentIcon;
export declare const VehicleSubwayFilled: FluentIcon;
export declare const VehicleSubwayRegular: FluentIcon;
export declare const VehicleSubwayClockFilled: FluentIcon;
export declare const VehicleSubwayClockRegular: FluentIcon;
export declare const VehicleTractorFilled: FluentIcon;
export declare const VehicleTractorRegular: FluentIcon;
export declare const VehicleTruckFilled: FluentIcon;
export declare const VehicleTruckRegular: FluentIcon;
export declare const VehicleTruckBagFilled: FluentIcon;
export declare const VehicleTruckBagRegular: FluentIcon;
export declare const VehicleTruckCheckmarkFilled: FluentIcon;
export declare const VehicleTruckCheckmarkRegular: FluentIcon;
export declare const VehicleTruckCubeFilled: FluentIcon;
export declare const VehicleTruckCubeRegular: FluentIcon;
export declare const VehicleTruckProfileFilled: FluentIcon;
export declare const VehicleTruckProfileRegular: FluentIcon;
export declare const VideoColor: FluentIcon;
export declare const VideoFilled: FluentIcon;
export declare const VideoRegular: FluentIcon;
export declare const Video360Filled: FluentIcon;
export declare const Video360Regular: FluentIcon;
export declare const Video360OffFilled: FluentIcon;
export declare const Video360OffRegular: FluentIcon;
export declare const VideoAddFilled: FluentIcon;
export declare const VideoAddRegular: FluentIcon;
export declare const VideoBackgroundEffectFilled: FluentIcon;
export declare const VideoBackgroundEffectRegular: FluentIcon;
export declare const VideoBackgroundEffectHorizontalFilled: FluentIcon;
export declare const VideoBackgroundEffectHorizontalRegular: FluentIcon;
export declare const VideoBluetoothFilled: FluentIcon;
export declare const VideoBluetoothRegular: FluentIcon;
export declare const VideoChatFilled: FluentIcon;
export declare const VideoChatRegular: FluentIcon;
export declare const VideoClipFilled: FluentIcon;
export declare const VideoClipRegular: FluentIcon;
export declare const VideoClipMultipleFilled: FluentIcon;
export declare const VideoClipMultipleRegular: FluentIcon;
export declare const VideoClipOffFilled: FluentIcon;
export declare const VideoClipOffRegular: FluentIcon;
export declare const VideoClipOptimizeFilled: FluentIcon;
export declare const VideoClipOptimizeRegular: FluentIcon;
export declare const VideoClipWandFilled: FluentIcon;
export declare const VideoClipWandRegular: FluentIcon;
export declare const VideoMultipleFilled: FluentIcon;
export declare const VideoMultipleRegular: FluentIcon;
export declare const VideoOffFilled: FluentIcon;
export declare const VideoOffRegular: FluentIcon;
export declare const VideoPersonFilled: FluentIcon;
export declare const VideoPersonRegular: FluentIcon;
export declare const VideoPersonCallFilled: FluentIcon;
export declare const VideoPersonCallRegular: FluentIcon;
export declare const VideoPersonClockFilled: FluentIcon;
export declare const VideoPersonClockRegular: FluentIcon;
export declare const VideoPersonOffFilled: FluentIcon;
export declare const VideoPersonOffRegular: FluentIcon;
export declare const VideoPersonPulseFilled: FluentIcon;
export declare const VideoPersonPulseRegular: FluentIcon;
export declare const VideoPersonSparkleFilled: FluentIcon;
export declare const VideoPersonSparkleRegular: FluentIcon;
export declare const VideoPersonSparkleOffFilled: FluentIcon;
export declare const VideoPersonSparkleOffRegular: FluentIcon;
export declare const VideoPersonStarFilled: FluentIcon;
export declare const VideoPersonStarRegular: FluentIcon;
export declare const VideoPersonStarOffFilled: FluentIcon;
export declare const VideoPersonStarOffRegular: FluentIcon;
export declare const VideoPlayPauseFilled: FluentIcon;
export declare const VideoPlayPauseRegular: FluentIcon;
export declare const VideoProhibitedFilled: FluentIcon;
export declare const VideoProhibitedRegular: FluentIcon;
export declare const VideoRecordingFilled: FluentIcon;
export declare const VideoRecordingRegular: FluentIcon;
export declare const VideoSecurityFilled: FluentIcon;
export declare const VideoSecurityRegular: FluentIcon;
export declare const VideoSettingsFilled: FluentIcon;
export declare const VideoSettingsRegular: FluentIcon;
export declare const VideoSwitchFilled: FluentIcon;
export declare const VideoSwitchRegular: FluentIcon;
export declare const VideoSyncFilled: FluentIcon;
export declare const VideoSyncRegular: FluentIcon;
export declare const VideoUsbFilled: FluentIcon;
export declare const VideoUsbRegular: FluentIcon;
export declare const ViewDesktopFilled: FluentIcon;
export declare const ViewDesktopRegular: FluentIcon;
export declare const ViewDesktopMobileFilled: FluentIcon;
export declare const ViewDesktopMobileRegular: FluentIcon;
export declare const VirtualNetworkFilled: FluentIcon;
export declare const VirtualNetworkRegular: FluentIcon;
export declare const VirtualNetworkToolboxFilled: FluentIcon;
export declare const VirtualNetworkToolboxRegular: FluentIcon;
export declare const VoicemailFilled: FluentIcon;
export declare const VoicemailRegular: FluentIcon;
export declare const VoicemailArrowBackFilled: FluentIcon;
export declare const VoicemailArrowBackRegular: FluentIcon;
export declare const VoicemailArrowForwardFilled: FluentIcon;
export declare const VoicemailArrowForwardRegular: FluentIcon;
export declare const VoicemailArrowSubtractFilled: FluentIcon;
export declare const VoicemailArrowSubtractRegular: FluentIcon;
export declare const VoicemailShieldFilled: FluentIcon;
export declare const VoicemailShieldRegular: FluentIcon;
export declare const VoicemailSubtractFilled: FluentIcon;
export declare const VoicemailSubtractRegular: FluentIcon;
export declare const VoteFilled: FluentIcon;
export declare const VoteRegular: FluentIcon;
export declare const WalkieTalkieFilled: FluentIcon;
export declare const WalkieTalkieRegular: FluentIcon;
export declare const WalletFilled: FluentIcon;
export declare const WalletRegular: FluentIcon;
export declare const WalletCreditCardFilled: FluentIcon;
export declare const WalletCreditCardRegular: FluentIcon;
export declare const WallpaperFilled: FluentIcon;
export declare const WallpaperRegular: FluentIcon;
export declare const WandFilled: FluentIcon;
export declare const WandRegular: FluentIcon;
export declare const WarningColor: FluentIcon;
export declare const WarningFilled: FluentIcon;
export declare const WarningRegular: FluentIcon;
export declare const WarningLockOpenFilled: FluentIcon;
export declare const WarningLockOpenRegular: FluentIcon;
export declare const WarningShieldFilled: FluentIcon;
export declare const WarningShieldRegular: FluentIcon;
export declare const WasherFilled: FluentIcon;
export declare const WasherRegular: FluentIcon;
export declare const WaterFilled: FluentIcon;
export declare const WaterRegular: FluentIcon;
export declare const WeatherBlowingSnowFilled: FluentIcon;
export declare const WeatherBlowingSnowRegular: FluentIcon;
export declare const WeatherCloudyFilled: FluentIcon;
export declare const WeatherCloudyRegular: FluentIcon;
export declare const WeatherDrizzleFilled: FluentIcon;
export declare const WeatherDrizzleRegular: FluentIcon;
export declare const WeatherDuststormFilled: FluentIcon;
export declare const WeatherDuststormRegular: FluentIcon;
export declare const WeatherFogFilled: FluentIcon;
export declare const WeatherFogRegular: FluentIcon;
export declare const WeatherHailDayFilled: FluentIcon;
export declare const WeatherHailDayRegular: FluentIcon;
export declare const WeatherHailNightFilled: FluentIcon;
export declare const WeatherHailNightRegular: FluentIcon;
export declare const WeatherHazeFilled: FluentIcon;
export declare const WeatherHazeRegular: FluentIcon;
export declare const WeatherMoonFilled: FluentIcon;
export declare const WeatherMoonRegular: FluentIcon;
export declare const WeatherMoonOffFilled: FluentIcon;
export declare const WeatherMoonOffRegular: FluentIcon;
export declare const WeatherPartlyCloudyDayFilled: FluentIcon;
export declare const WeatherPartlyCloudyDayRegular: FluentIcon;
export declare const WeatherPartlyCloudyNightFilled: FluentIcon;
export declare const WeatherPartlyCloudyNightRegular: FluentIcon;
export declare const WeatherRainFilled: FluentIcon;
export declare const WeatherRainRegular: FluentIcon;
export declare const WeatherRainShowersDayFilled: FluentIcon;
export declare const WeatherRainShowersDayRegular: FluentIcon;
export declare const WeatherRainShowersNightFilled: FluentIcon;
export declare const WeatherRainShowersNightRegular: FluentIcon;
export declare const WeatherRainSnowFilled: FluentIcon;
export declare const WeatherRainSnowRegular: FluentIcon;
export declare const WeatherSnowFilled: FluentIcon;
export declare const WeatherSnowRegular: FluentIcon;
export declare const WeatherSnowShowerDayFilled: FluentIcon;
export declare const WeatherSnowShowerDayRegular: FluentIcon;
export declare const WeatherSnowShowerNightFilled: FluentIcon;
export declare const WeatherSnowShowerNightRegular: FluentIcon;
export declare const WeatherSnowflakeColor: FluentIcon;
export declare const WeatherSnowflakeFilled: FluentIcon;
export declare const WeatherSnowflakeRegular: FluentIcon;
export declare const WeatherSquallsFilled: FluentIcon;
export declare const WeatherSquallsRegular: FluentIcon;
export declare const WeatherSunnyFilled: FluentIcon;
export declare const WeatherSunnyRegular: FluentIcon;
export declare const WeatherSunnyHighFilled: FluentIcon;
export declare const WeatherSunnyHighRegular: FluentIcon;
export declare const WeatherSunnyLowColor: FluentIcon;
export declare const WeatherSunnyLowFilled: FluentIcon;
export declare const WeatherSunnyLowRegular: FluentIcon;
export declare const WeatherThunderstormFilled: FluentIcon;
export declare const WeatherThunderstormRegular: FluentIcon;
export declare const WebAssetFilled: FluentIcon;
export declare const WebAssetRegular: FluentIcon;
export declare const WheelchairAccessFilled: FluentIcon;
export declare const WheelchairAccessRegular: FluentIcon;
export declare const WhiteboardFilled: FluentIcon;
export declare const WhiteboardRegular: FluentIcon;
export declare const WhiteboardOffFilled: FluentIcon;
export declare const WhiteboardOffRegular: FluentIcon;
export declare const Wifi1Filled: FluentIcon;
export declare const Wifi1Regular: FluentIcon;
export declare const WifiColor: FluentIcon;
export declare const Wifi2Filled: FluentIcon;
export declare const Wifi2Regular: FluentIcon;
export declare const Wifi3Filled: FluentIcon;
export declare const Wifi3Regular: FluentIcon;
export declare const Wifi4Filled: FluentIcon;
export declare const Wifi4Regular: FluentIcon;
export declare const WifiLockFilled: FluentIcon;
export declare const WifiLockRegular: FluentIcon;
export declare const WifiOffFilled: FluentIcon;
export declare const WifiOffRegular: FluentIcon;
export declare const WifiSettingsFilled: FluentIcon;
export declare const WifiSettingsRegular: FluentIcon;
export declare const WifiWarningColor: FluentIcon;
export declare const WifiWarningFilled: FluentIcon;
export declare const WifiWarningRegular: FluentIcon;
export declare const WindowFilled: FluentIcon;
export declare const WindowRegular: FluentIcon;
export declare const WindowAdFilled: FluentIcon;
export declare const WindowAdRegular: FluentIcon;
export declare const WindowAdOffFilled: FluentIcon;
export declare const WindowAdOffRegular: FluentIcon;
export declare const WindowAdPersonFilled: FluentIcon;
export declare const WindowAdPersonRegular: FluentIcon;
export declare const WindowAppsFilled: FluentIcon;
export declare const WindowAppsRegular: FluentIcon;
export declare const WindowArrowUpFilled: FluentIcon;
export declare const WindowArrowUpRegular: FluentIcon;
export declare const WindowBrushFilled: FluentIcon;
export declare const WindowBrushRegular: FluentIcon;
export declare const WindowBulletListFilled: FluentIcon;
export declare const WindowBulletListRegular: FluentIcon;
export declare const WindowBulletListAddFilled: FluentIcon;
export declare const WindowBulletListAddRegular: FluentIcon;
export declare const WindowColumnOneFourthLeftFilled: FluentIcon;
export declare const WindowColumnOneFourthLeftRegular: FluentIcon;
export declare const WindowColumnOneFourthLeftFocusLeftFilled: FluentIcon;
export declare const WindowColumnOneFourthLeftFocusTopFilled: FluentIcon;
export declare const WindowConsoleFilled: FluentIcon;
export declare const WindowConsoleRegular: FluentIcon;
export declare const WindowDatabaseFilled: FluentIcon;
export declare const WindowDatabaseRegular: FluentIcon;
export declare const WindowDevEditFilled: FluentIcon;
export declare const WindowDevEditRegular: FluentIcon;
export declare const WindowDevToolsFilled: FluentIcon;
export declare const WindowDevToolsRegular: FluentIcon;
export declare const WindowEditFilled: FluentIcon;
export declare const WindowEditRegular: FluentIcon;
export declare const WindowFingerprintFilled: FluentIcon;
export declare const WindowFingerprintRegular: FluentIcon;
export declare const WindowHeaderHorizontalFilled: FluentIcon;
export declare const WindowHeaderHorizontalRegular: FluentIcon;
export declare const WindowHeaderHorizontalOffFilled: FluentIcon;
export declare const WindowHeaderHorizontalOffRegular: FluentIcon;
export declare const WindowHeaderVerticalFilled: FluentIcon;
export declare const WindowHeaderVerticalRegular: FluentIcon;
export declare const WindowInprivateFilled: FluentIcon;
export declare const WindowInprivateRegular: FluentIcon;
export declare const WindowInprivateAccountFilled: FluentIcon;
export declare const WindowInprivateAccountRegular: FluentIcon;
export declare const WindowLocationTargetFilled: FluentIcon;
export declare const WindowLocationTargetRegular: FluentIcon;
export declare const WindowMultipleFilled: FluentIcon;
export declare const WindowMultipleRegular: FluentIcon;
export declare const WindowMultipleSwapFilled: FluentIcon;
export declare const WindowMultipleSwapRegular: FluentIcon;
export declare const WindowNewFilled: FluentIcon;
export declare const WindowNewRegular: FluentIcon;
export declare const WindowPlayFilled: FluentIcon;
export declare const WindowPlayRegular: FluentIcon;
export declare const WindowSettingsFilled: FluentIcon;
export declare const WindowSettingsRegular: FluentIcon;
export declare const WindowShieldFilled: FluentIcon;
export declare const WindowShieldRegular: FluentIcon;
export declare const WindowTextFilled: FluentIcon;
export declare const WindowTextRegular: FluentIcon;
export declare const WindowWrenchFilled: FluentIcon;
export declare const WindowWrenchRegular: FluentIcon;
export declare const WrenchColor: FluentIcon;
export declare const WrenchFilled: FluentIcon;
export declare const WrenchRegular: FluentIcon;
export declare const WrenchScrewdriverColor: FluentIcon;
export declare const WrenchScrewdriverFilled: FluentIcon;
export declare const WrenchScrewdriverRegular: FluentIcon;
export declare const WrenchSettingsFilled: FluentIcon;
export declare const WrenchSettingsRegular: FluentIcon;
export declare const XboxConsoleFilled: FluentIcon;
export declare const XboxConsoleRegular: FluentIcon;
export declare const XboxControllerFilled: FluentIcon;
export declare const XboxControllerRegular: FluentIcon;
export declare const XboxControllerErrorFilled: FluentIcon;
export declare const XboxControllerErrorRegular: FluentIcon;
export declare const XrayFilled: FluentIcon;
export declare const XrayRegular: FluentIcon;
export declare const ZoomFitFilled: FluentIcon;
export declare const ZoomFitRegular: FluentIcon;
export declare const ZoomInFilled: FluentIcon;
export declare const ZoomInRegular: FluentIcon;
export declare const ZoomOutFilled: FluentIcon;
export declare const ZoomOutRegular: FluentIcon;
