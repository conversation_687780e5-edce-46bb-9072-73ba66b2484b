import type { FluentFontIcon } from "../../utils/fonts/createFluentFontIcon";
export declare const AccessibilityCheckmark32Light: FluentFontIcon;
export declare const Add32Light: FluentFontIcon;
export declare const Alert32Light: FluentFontIcon;
export declare const AppFolder32Light: FluentFontIcon;
export declare const AppGeneric32Light: FluentFontIcon;
export declare const Archive32Light: FluentFontIcon;
export declare const ArchiveSettings32Light: FluentFontIcon;
export declare const ArrowClockwise32Light: FluentFontIcon;
export declare const ArrowDown32Light: FluentFontIcon;
export declare const ArrowDownload32Light: FluentFontIcon;
export declare const ArrowForward32Light: FluentFontIcon;
export declare const ArrowHookDownLeft32Light: FluentFontIcon;
export declare const ArrowHookDownRight32Light: FluentFontIcon;
export declare const ArrowHookUpLeft32Light: FluentFontIcon;
export declare const ArrowHookUpRight32Light: FluentFontIcon;
export declare const ArrowRedo32Light: FluentFontIcon;
export declare const ArrowReply32Light: FluentFontIcon;
export declare const ArrowReplyAll32Light: FluentFontIcon;
export declare const ArrowUndo32Light: FluentFontIcon;
export declare const Attach32Light: FluentFontIcon;
export declare const AutoFit32Light: FluentFontIcon;
export declare const AutoFitWidth32Light: FluentFontIcon;
export declare const Autocorrect32Light: FluentFontIcon;
export declare const BookContacts32Light: FluentFontIcon;
export declare const BreakoutRoom32Light: FluentFontIcon;
export declare const Broom32Light: FluentFontIcon;
export declare const Calendar3Day32Light: FluentFontIcon;
export declare const CalendarClock32Light: FluentFontIcon;
export declare const CalendarDataBar32Light: FluentFontIcon;
export declare const CalendarDay32Light: FluentFontIcon;
export declare const CalendarEdit32Light: FluentFontIcon;
export declare const CalendarEmpty32Light: FluentFontIcon;
export declare const CalendarLtr32Light: FluentFontIcon;
export declare const CalendarMonth32Light: FluentFontIcon;
export declare const CalendarMultiple32Light: FluentFontIcon;
export declare const CalendarPattern32Light: FluentFontIcon;
export declare const CalendarReply32Light: FluentFontIcon;
export declare const CalendarSparkle32Light: FluentFontIcon;
export declare const CalendarTodo32Light: FluentFontIcon;
export declare const CalendarWorkWeek32Light: FluentFontIcon;
export declare const Chat32Light: FluentFontIcon;
export declare const Checkmark32Light: FluentFontIcon;
export declare const CheckmarkCircle32Light: FluentFontIcon;
export declare const Classification32Light: FluentFontIcon;
export declare const ClipboardPaste32Light: FluentFontIcon;
export declare const Clock32Light: FluentFontIcon;
export declare const ClockAlarm32Light: FluentFontIcon;
export declare const Color32Light: FluentFontIcon;
export declare const ColorFill32Light: FluentFontIcon;
export declare const ColorFillAccent32Light: FluentFontIcon;
export declare const Comment32Light: FluentFontIcon;
export declare const CommentAdd32Light: FluentFontIcon;
export declare const Compose32Light: FluentFontIcon;
export declare const Copy32Light: FluentFontIcon;
export declare const Crop32Light: FluentFontIcon;
export declare const Cursor32Light: FluentFontIcon;
export declare const Cut32Light: FluentFontIcon;
export declare const Delete32Light: FluentFontIcon;
export declare const Dismiss32Light: FluentFontIcon;
export declare const DismissCircle32Light: FluentFontIcon;
export declare const Document24Light: FluentFontIcon;
export declare const Document28Light: FluentFontIcon;
export declare const Document32Light: FluentFontIcon;
export declare const Document48Light: FluentFontIcon;
export declare const DocumentLightning32Light: FluentFontIcon;
export declare const DocumentSignature32Light: FluentFontIcon;
export declare const DocumentSparkle24Light: FluentFontIcon;
export declare const DocumentSparkle28Light: FluentFontIcon;
export declare const DocumentSparkle32Light: FluentFontIcon;
export declare const DocumentSparkle48Light: FluentFontIcon;
export declare const DoorArrowRight32Light: FluentFontIcon;
export declare const Edit32Light: FluentFontIcon;
export declare const Emoji32Light: FluentFontIcon;
export declare const Eye32Light: FluentFontIcon;
export declare const EyeOff32Light: FluentFontIcon;
export declare const Filter32Light: FluentFontIcon;
export declare const Flag32Light: FluentFontIcon;
export declare const FlagOff32Light: FluentFontIcon;
export declare const Flash32Light: FluentFontIcon;
export declare const FolderArrowRight32Light: FluentFontIcon;
export declare const FolderMail32Light: FluentFontIcon;
export declare const HandDraw32Light: FluentFontIcon;
export declare const History32Light: FluentFontIcon;
export declare const ImageAdd32Light: FluentFontIcon;
export declare const ImageAltText32Light: FluentFontIcon;
export declare const ImageCopy32Light: FluentFontIcon;
export declare const ImageReflection32Light: FluentFontIcon;
export declare const ImageShadow32Light: FluentFontIcon;
export declare const ImmersiveReader32Light: FluentFontIcon;
export declare const Important32Light: FluentFontIcon;
export declare const Lasso32Light: FluentFontIcon;
export declare const LayoutColumnTwo32Light: FluentFontIcon;
export declare const LayoutColumnTwoFocusLeft32Light: FluentFontIcon;
export declare const LayoutColumnTwoFocusRight32Light: FluentFontIcon;
export declare const LayoutRowTwo32Light: FluentFontIcon;
export declare const LayoutRowTwoFocusTop32Light: FluentFontIcon;
export declare const LayoutRowTwoFocusTopSettings32Light: FluentFontIcon;
export declare const LayoutRowTwoSettings32Light: FluentFontIcon;
export declare const Lightbulb32Light: FluentFontIcon;
export declare const Link32Light: FluentFontIcon;
export declare const LockClosed32Light: FluentFontIcon;
export declare const LockOpen32Light: FluentFontIcon;
export declare const Mail32Light: FluentFontIcon;
export declare const MailAlert32Light: FluentFontIcon;
export declare const MailArrowClockwise32Light: FluentFontIcon;
export declare const MailArrowDoubleBack32Light: FluentFontIcon;
export declare const MailCopy32Light: FluentFontIcon;
export declare const MailEdit32Light: FluentFontIcon;
export declare const MailList32Light: FluentFontIcon;
export declare const MailMultiple32Light: FluentFontIcon;
export declare const MailRead32Light: FluentFontIcon;
export declare const MailReadMultiple32Light: FluentFontIcon;
export declare const MailRewind32Light: FluentFontIcon;
export declare const MailSettings32Light: FluentFontIcon;
export declare const MailTemplate32Light: FluentFontIcon;
export declare const MailUnread32Light: FluentFontIcon;
export declare const Mic32Light: FluentFontIcon;
export declare const Molecule32Light: FluentFontIcon;
export declare const Note32Light: FluentFontIcon;
export declare const Options32Light: FluentFontIcon;
export declare const PaintBrush32Light: FluentFontIcon;
export declare const PanelLeftDefault32Light: FluentFontIcon;
export declare const PanelLeftFocusRight32Light: FluentFontIcon;
export declare const PenSparkle32Light: FluentFontIcon;
export declare const People32Light: FluentFontIcon;
export declare const PeopleAdd32Light: FluentFontIcon;
export declare const PeopleCheckmark32Light: FluentFontIcon;
export declare const PeopleCommunity32Light: FluentFontIcon;
export declare const PeopleEdit32Light: FluentFontIcon;
export declare const PeopleList32Light: FluentFontIcon;
export declare const PeopleSettings32Light: FluentFontIcon;
export declare const PeopleSync32Light: FluentFontIcon;
export declare const Person32Light: FluentFontIcon;
export declare const PersonAdd32Light: FluentFontIcon;
export declare const PersonAvailable32Light: FluentFontIcon;
export declare const PersonFeedback32Light: FluentFontIcon;
export declare const PersonMail32Light: FluentFontIcon;
export declare const PersonProhibited32Light: FluentFontIcon;
export declare const PersonSuport32Light: FluentFontIcon;
export declare const Phone32Light: FluentFontIcon;
export declare const PictureInPicture32Light: FluentFontIcon;
export declare const Pin32Light: FluentFontIcon;
export declare const PinOff32Light: FluentFontIcon;
export declare const Poll32Light: FluentFontIcon;
export declare const Print32Light: FluentFontIcon;
export declare const Question32Light: FluentFontIcon;
export declare const ReadAloud32Light: FluentFontIcon;
export declare const RectangleLandscape32Light: FluentFontIcon;
export declare const RotateLeft32Light: FluentFontIcon;
export declare const Save32Light: FluentFontIcon;
export declare const SendClock32Light: FluentFontIcon;
export declare const Settings32Light: FluentFontIcon;
export declare const Share32Light: FluentFontIcon;
export declare const ShieldError32Light: FluentFontIcon;
export declare const Signature32Light: FluentFontIcon;
export declare const SpeakerMute32Light: FluentFontIcon;
export declare const SquareArrowForward32Light: FluentFontIcon;
export declare const Stamp32Light: FluentFontIcon;
export declare const StarAdd32Light: FluentFontIcon;
export declare const StarArrowRight32Light: FluentFontIcon;
export declare const Sticker32Light: FluentFontIcon;
export declare const TabAdd32Light: FluentFontIcon;
export declare const Table32Light: FluentFontIcon;
export declare const TableAltText32Light: FluentFontIcon;
export declare const TableCellsMerge32Light: FluentFontIcon;
export declare const TableCellsSplit32Light: FluentFontIcon;
export declare const TableDismiss32Light: FluentFontIcon;
export declare const TableMoveAbove32Light: FluentFontIcon;
export declare const TableMoveBelow32Light: FluentFontIcon;
export declare const TableMoveLeft32Light: FluentFontIcon;
export declare const TableMoveRight32Light: FluentFontIcon;
export declare const TableSettings32Light: FluentFontIcon;
export declare const TableSimple32Light: FluentFontIcon;
export declare const Tag32Light: FluentFontIcon;
export declare const Text32Light: FluentFontIcon;
export declare const TextClearFormatting32Light: FluentFontIcon;
export declare const TextCollapse32Light: FluentFontIcon;
export declare const TextDensity32Light: FluentFontIcon;
export declare const TextEditStyle32Light: FluentFontIcon;
export declare const TextExpand32Light: FluentFontIcon;
export declare const TextboxAlignTopLeft32Light: FluentFontIcon;
export declare const Toolbox32Light: FluentFontIcon;
export declare const Translate32Light: FluentFontIcon;
export declare const Video32Light: FluentFontIcon;
export declare const VideoClip32Light: FluentFontIcon;
export declare const WeatherMoon32Light: FluentFontIcon;
export declare const WeatherSunny32Light: FluentFontIcon;
export declare const Window32Light: FluentFontIcon;
export declare const WrenchScrewdriver32Light: FluentFontIcon;
export declare const ZoomIn32Light: FluentFontIcon;
export declare const ZoomOut32Light: FluentFontIcon;
export declare const AccessTime24Filled: FluentFontIcon;
export declare const Accessibility16Filled: FluentFontIcon;
export declare const Accessibility20Filled: FluentFontIcon;
export declare const Accessibility24Filled: FluentFontIcon;
export declare const Accessibility28Filled: FluentFontIcon;
export declare const Add12Filled: FluentFontIcon;
export declare const Add16Filled: FluentFontIcon;
export declare const Add20Filled: FluentFontIcon;
export declare const Add24Filled: FluentFontIcon;
export declare const Add28Filled: FluentFontIcon;
export declare const AddCircle20Filled: FluentFontIcon;
export declare const AddCircle24Filled: FluentFontIcon;
export declare const AddCircle28Filled: FluentFontIcon;
export declare const Airplane20Filled: FluentFontIcon;
export declare const Airplane24Filled: FluentFontIcon;
export declare const AirplaneTakeOff16Filled: FluentFontIcon;
export declare const AirplaneTakeOff20Filled: FluentFontIcon;
export declare const AirplaneTakeOff24Filled: FluentFontIcon;
export declare const Alert20Filled: FluentFontIcon;
export declare const Alert24Filled: FluentFontIcon;
export declare const Alert28Filled: FluentFontIcon;
export declare const AlertOff16Filled: FluentFontIcon;
export declare const AlertOff20Filled: FluentFontIcon;
export declare const AlertOff24Filled: FluentFontIcon;
export declare const AlertOff28Filled: FluentFontIcon;
export declare const AlertOn24Filled: FluentFontIcon;
export declare const AlertSnooze20Filled: FluentFontIcon;
export declare const AlertSnooze24Filled: FluentFontIcon;
export declare const AlertUrgent20Filled: FluentFontIcon;
export declare const AlertUrgent24Filled: FluentFontIcon;
export declare const AnimalDog20Filled: FluentFontIcon;
export declare const AnimalDog24Filled: FluentFontIcon;
export declare const AppFolder20Filled: FluentFontIcon;
export declare const AppFolder24Filled: FluentFontIcon;
export declare const AppGeneric24Filled: FluentFontIcon;
export declare const AppRecent24Filled: FluentFontIcon;
export declare const AppStore24Filled: FluentFontIcon;
export declare const AppTitle24Filled: FluentFontIcon;
export declare const ApprovalsApp24Filled: FluentFontIcon;
export declare const ApprovalsApp28Filled: FluentFontIcon;
export declare const Apps16Filled: FluentFontIcon;
export declare const Apps20Filled: FluentFontIcon;
export declare const Apps24Filled: FluentFontIcon;
export declare const Apps28Filled: FluentFontIcon;
export declare const AppsAddIn20Filled: FluentFontIcon;
export declare const AppsAddIn24Filled: FluentFontIcon;
export declare const AppsList24Filled: FluentFontIcon;
export declare const Archive20Filled: FluentFontIcon;
export declare const Archive24Filled: FluentFontIcon;
export declare const Archive28Filled: FluentFontIcon;
export declare const Archive48Filled: FluentFontIcon;
export declare const ArrowClockwise20Filled: FluentFontIcon;
export declare const ArrowClockwise24Filled: FluentFontIcon;
export declare const ArrowCounterclockwise20Filled: FluentFontIcon;
export declare const ArrowCounterclockwise24Filled: FluentFontIcon;
export declare const ArrowCurveDownLeft20Filled: FluentFontIcon;
export declare const ArrowCurveDownRight20Filled: FluentFontIcon;
export declare const ArrowCurveUpLeft20Filled: FluentFontIcon;
export declare const ArrowCurveUpRight20Filled: FluentFontIcon;
export declare const ArrowDown16Filled: FluentFontIcon;
export declare const ArrowDown20Filled: FluentFontIcon;
export declare const ArrowDown24Filled: FluentFontIcon;
export declare const ArrowDown28Filled: FluentFontIcon;
export declare const ArrowDownLeft24Filled: FluentFontIcon;
export declare const ArrowDownload16Filled: FluentFontIcon;
export declare const ArrowDownload20Filled: FluentFontIcon;
export declare const ArrowDownload24Filled: FluentFontIcon;
export declare const ArrowDownload48Filled: FluentFontIcon;
export declare const ArrowExpand24Filled: FluentFontIcon;
export declare const ArrowForward16Filled: FluentFontIcon;
export declare const ArrowForward20Filled: FluentFontIcon;
export declare const ArrowForward24Filled: FluentFontIcon;
export declare const ArrowImport20Filled: FluentFontIcon;
export declare const ArrowImport24Filled: FluentFontIcon;
export declare const ArrowLeft20Filled: FluentFontIcon;
export declare const ArrowLeft24Filled: FluentFontIcon;
export declare const ArrowLeft28Filled: FluentFontIcon;
export declare const ArrowMaximize16Filled: FluentFontIcon;
export declare const ArrowMaximize20Filled: FluentFontIcon;
export declare const ArrowMaximize24Filled: FluentFontIcon;
export declare const ArrowMaximize28Filled: FluentFontIcon;
export declare const ArrowMaximizeVertical20Filled: FluentFontIcon;
export declare const ArrowMaximizeVertical24Filled: FluentFontIcon;
export declare const ArrowMinimize16Filled: FluentFontIcon;
export declare const ArrowMinimize20Filled: FluentFontIcon;
export declare const ArrowMinimize24Filled: FluentFontIcon;
export declare const ArrowMinimize28Filled: FluentFontIcon;
export declare const ArrowMinimizeVertical24Filled: FluentFontIcon;
export declare const ArrowMove24Filled: FluentFontIcon;
export declare const ArrowNext20Filled: FluentFontIcon;
export declare const ArrowNext24Filled: FluentFontIcon;
export declare const ArrowPrevious20Filled: FluentFontIcon;
export declare const ArrowPrevious24Filled: FluentFontIcon;
export declare const ArrowRedo20Filled: FluentFontIcon;
export declare const ArrowRedo24Filled: FluentFontIcon;
export declare const ArrowRepeatAll16Filled: FluentFontIcon;
export declare const ArrowRepeatAll20Filled: FluentFontIcon;
export declare const ArrowRepeatAll24Filled: FluentFontIcon;
export declare const ArrowRepeatAllOff16Filled: FluentFontIcon;
export declare const ArrowRepeatAllOff20Filled: FluentFontIcon;
export declare const ArrowRepeatAllOff24Filled: FluentFontIcon;
export declare const ArrowReply16Filled: FluentFontIcon;
export declare const ArrowReply20Filled: FluentFontIcon;
export declare const ArrowReply24Filled: FluentFontIcon;
export declare const ArrowReply48Filled: FluentFontIcon;
export declare const ArrowReplyAll16Filled: FluentFontIcon;
export declare const ArrowReplyAll20Filled: FluentFontIcon;
export declare const ArrowReplyAll24Filled: FluentFontIcon;
export declare const ArrowReplyAll48Filled: FluentFontIcon;
export declare const ArrowReplyDown16Filled: FluentFontIcon;
export declare const ArrowReplyDown20Filled: FluentFontIcon;
export declare const ArrowReplyDown24Filled: FluentFontIcon;
export declare const ArrowRight20Filled: FluentFontIcon;
export declare const ArrowRight24Filled: FluentFontIcon;
export declare const ArrowRight28Filled: FluentFontIcon;
export declare const ArrowRotateClockwise20Filled: FluentFontIcon;
export declare const ArrowRotateClockwise24Filled: FluentFontIcon;
export declare const ArrowRotateCounterclockwise20Filled: FluentFontIcon;
export declare const ArrowRotateCounterclockwise24Filled: FluentFontIcon;
export declare const ArrowSort20Filled: FluentFontIcon;
export declare const ArrowSort24Filled: FluentFontIcon;
export declare const ArrowSort28Filled: FluentFontIcon;
export declare const ArrowSwap20Filled: FluentFontIcon;
export declare const ArrowSwap24Filled: FluentFontIcon;
export declare const ArrowSync12Filled: FluentFontIcon;
export declare const ArrowSync20Filled: FluentFontIcon;
export declare const ArrowSync24Filled: FluentFontIcon;
export declare const ArrowSyncCircle16Filled: FluentFontIcon;
export declare const ArrowSyncCircle20Filled: FluentFontIcon;
export declare const ArrowSyncCircle24Filled: FluentFontIcon;
export declare const ArrowSyncOff12Filled: FluentFontIcon;
export declare const ArrowTrending16Filled: FluentFontIcon;
export declare const ArrowTrending20Filled: FluentFontIcon;
export declare const ArrowTrending24Filled: FluentFontIcon;
export declare const ArrowUndo20Filled: FluentFontIcon;
export declare const ArrowUndo24Filled: FluentFontIcon;
export declare const ArrowUp20Filled: FluentFontIcon;
export declare const ArrowUp24Filled: FluentFontIcon;
export declare const ArrowUp28Filled: FluentFontIcon;
export declare const ArrowUpLeft24Filled: FluentFontIcon;
export declare const ArrowUpRight24Filled: FluentFontIcon;
export declare const ArrowUpload20Filled: FluentFontIcon;
export declare const ArrowUpload24Filled: FluentFontIcon;
export declare const ArrowsBidirectional24Filled: FluentFontIcon;
export declare const Attach16Filled: FluentFontIcon;
export declare const Attach20Filled: FluentFontIcon;
export declare const Attach24Filled: FluentFontIcon;
export declare const Autocorrect24Filled: FluentFontIcon;
export declare const Autosum20Filled: FluentFontIcon;
export declare const Autosum24Filled: FluentFontIcon;
export declare const Backspace20Filled: FluentFontIcon;
export declare const Backspace24Filled: FluentFontIcon;
export declare const Badge24Filled: FluentFontIcon;
export declare const Balloon20Filled: FluentFontIcon;
export declare const Balloon24Filled: FluentFontIcon;
export declare const Battery020Filled: FluentFontIcon;
export declare const Battery024Filled: FluentFontIcon;
export declare const Battery120Filled: FluentFontIcon;
export declare const Battery124Filled: FluentFontIcon;
export declare const Battery220Filled: FluentFontIcon;
export declare const Battery224Filled: FluentFontIcon;
export declare const Battery320Filled: FluentFontIcon;
export declare const Battery324Filled: FluentFontIcon;
export declare const Battery420Filled: FluentFontIcon;
export declare const Battery424Filled: FluentFontIcon;
export declare const Battery520Filled: FluentFontIcon;
export declare const Battery524Filled: FluentFontIcon;
export declare const Battery620Filled: FluentFontIcon;
export declare const Battery624Filled: FluentFontIcon;
export declare const Battery720Filled: FluentFontIcon;
export declare const Battery724Filled: FluentFontIcon;
export declare const Battery820Filled: FluentFontIcon;
export declare const Battery824Filled: FluentFontIcon;
export declare const Battery920Filled: FluentFontIcon;
export declare const Battery924Filled: FluentFontIcon;
export declare const BatteryCharge20Filled: FluentFontIcon;
export declare const BatteryCharge24Filled: FluentFontIcon;
export declare const BatterySaver20Filled: FluentFontIcon;
export declare const BatterySaver24Filled: FluentFontIcon;
export declare const BatteryWarning24Filled: FluentFontIcon;
export declare const Beaker16Filled: FluentFontIcon;
export declare const Beaker20Filled: FluentFontIcon;
export declare const Beaker24Filled: FluentFontIcon;
export declare const Bed20Filled: FluentFontIcon;
export declare const Bed24Filled: FluentFontIcon;
export declare const Bluetooth20Filled: FluentFontIcon;
export declare const Bluetooth24Filled: FluentFontIcon;
export declare const BluetoothConnected24Filled: FluentFontIcon;
export declare const BluetoothDisabled24Filled: FluentFontIcon;
export declare const BluetoothSearching24Filled: FluentFontIcon;
export declare const Board24Filled: FluentFontIcon;
export declare const BookGlobe24Filled: FluentFontIcon;
export declare const BookNumber16Filled: FluentFontIcon;
export declare const BookNumber20Filled: FluentFontIcon;
export declare const BookNumber24Filled: FluentFontIcon;
export declare const Bookmark16Filled: FluentFontIcon;
export declare const Bookmark20Filled: FluentFontIcon;
export declare const Bookmark24Filled: FluentFontIcon;
export declare const Bookmark28Filled: FluentFontIcon;
export declare const BookmarkOff24Filled: FluentFontIcon;
export declare const Bot24Filled: FluentFontIcon;
export declare const BotAdd24Filled: FluentFontIcon;
export declare const Branch24Filled: FluentFontIcon;
export declare const Briefcase20Filled: FluentFontIcon;
export declare const Briefcase24Filled: FluentFontIcon;
export declare const BroadActivityFeed24Filled: FluentFontIcon;
export declare const Broom20Filled: FluentFontIcon;
export declare const Broom24Filled: FluentFontIcon;
export declare const Building24Filled: FluentFontIcon;
export declare const BuildingRetail24Filled: FluentFontIcon;
export declare const Calculator20Filled: FluentFontIcon;
export declare const Calendar3Day20Filled: FluentFontIcon;
export declare const Calendar3Day24Filled: FluentFontIcon;
export declare const Calendar3Day28Filled: FluentFontIcon;
export declare const CalendarAdd20Filled: FluentFontIcon;
export declare const CalendarAdd24Filled: FluentFontIcon;
export declare const CalendarAgenda20Filled: FluentFontIcon;
export declare const CalendarAgenda24Filled: FluentFontIcon;
export declare const CalendarAgenda28Filled: FluentFontIcon;
export declare const CalendarArrowRight20Filled: FluentFontIcon;
export declare const CalendarAssistant20Filled: FluentFontIcon;
export declare const CalendarAssistant24Filled: FluentFontIcon;
export declare const CalendarCancel20Filled: FluentFontIcon;
export declare const CalendarCancel24Filled: FluentFontIcon;
export declare const CalendarCheckmark16Filled: FluentFontIcon;
export declare const CalendarCheckmark20Filled: FluentFontIcon;
export declare const CalendarClock20Filled: FluentFontIcon;
export declare const CalendarClock24Filled: FluentFontIcon;
export declare const CalendarDay20Filled: FluentFontIcon;
export declare const CalendarDay24Filled: FluentFontIcon;
export declare const CalendarDay28Filled: FluentFontIcon;
export declare const CalendarEmpty16Filled: FluentFontIcon;
export declare const CalendarEmpty20Filled: FluentFontIcon;
export declare const CalendarEmpty24Filled: FluentFontIcon;
export declare const CalendarEmpty28Filled: FluentFontIcon;
export declare const CalendarMonth20Filled: FluentFontIcon;
export declare const CalendarMonth24Filled: FluentFontIcon;
export declare const CalendarMonth28Filled: FluentFontIcon;
export declare const CalendarMultiple20Filled: FluentFontIcon;
export declare const CalendarMultiple24Filled: FluentFontIcon;
export declare const CalendarPerson20Filled: FluentFontIcon;
export declare const CalendarReply16Filled: FluentFontIcon;
export declare const CalendarReply20Filled: FluentFontIcon;
export declare const CalendarReply24Filled: FluentFontIcon;
export declare const CalendarReply28Filled: FluentFontIcon;
export declare const CalendarSettings20Filled: FluentFontIcon;
export declare const CalendarStar20Filled: FluentFontIcon;
export declare const CalendarStar24Filled: FluentFontIcon;
export declare const CalendarSync16Filled: FluentFontIcon;
export declare const CalendarSync20Filled: FluentFontIcon;
export declare const CalendarSync24Filled: FluentFontIcon;
export declare const CalendarToday16Filled: FluentFontIcon;
export declare const CalendarToday20Filled: FluentFontIcon;
export declare const CalendarToday24Filled: FluentFontIcon;
export declare const CalendarToday28Filled: FluentFontIcon;
export declare const CalendarWeekNumbers24Filled: FluentFontIcon;
export declare const CalendarWeekStart20Filled: FluentFontIcon;
export declare const CalendarWeekStart24Filled: FluentFontIcon;
export declare const CalendarWeekStart28Filled: FluentFontIcon;
export declare const CalendarWorkWeek16Filled: FluentFontIcon;
export declare const CalendarWorkWeek20Filled: FluentFontIcon;
export declare const CalendarWorkWeek24Filled: FluentFontIcon;
export declare const CallAdd24Filled: FluentFontIcon;
export declare const CallEnd20Filled: FluentFontIcon;
export declare const CallEnd24Filled: FluentFontIcon;
export declare const CallEnd28Filled: FluentFontIcon;
export declare const CallForward24Filled: FluentFontIcon;
export declare const CallInbound16Filled: FluentFontIcon;
export declare const CallInbound24Filled: FluentFontIcon;
export declare const CallMissed16Filled: FluentFontIcon;
export declare const CallMissed24Filled: FluentFontIcon;
export declare const CallOutbound16Filled: FluentFontIcon;
export declare const CallOutbound24Filled: FluentFontIcon;
export declare const CallPark24Filled: FluentFontIcon;
export declare const CalligraphyPen20Filled: FluentFontIcon;
export declare const CalligraphyPen24Filled: FluentFontIcon;
export declare const Camera20Filled: FluentFontIcon;
export declare const Camera24Filled: FluentFontIcon;
export declare const Camera28Filled: FluentFontIcon;
export declare const CameraAdd20Filled: FluentFontIcon;
export declare const CameraAdd24Filled: FluentFontIcon;
export declare const CameraAdd48Filled: FluentFontIcon;
export declare const CameraSwitch24Filled: FluentFontIcon;
export declare const CaretDown12Filled: FluentFontIcon;
export declare const CaretDown16Filled: FluentFontIcon;
export declare const CaretDown20Filled: FluentFontIcon;
export declare const CaretDown24Filled: FluentFontIcon;
export declare const CaretLeft12Filled: FluentFontIcon;
export declare const CaretLeft16Filled: FluentFontIcon;
export declare const CaretLeft20Filled: FluentFontIcon;
export declare const CaretLeft24Filled: FluentFontIcon;
export declare const CaretRight12Filled: FluentFontIcon;
export declare const CaretRight16Filled: FluentFontIcon;
export declare const CaretRight20Filled: FluentFontIcon;
export declare const CaretRight24Filled: FluentFontIcon;
export declare const Cart24Filled: FluentFontIcon;
export declare const Cast20Filled: FluentFontIcon;
export declare const Cast24Filled: FluentFontIcon;
export declare const Cast28Filled: FluentFontIcon;
export declare const Cellular3G24Filled: FluentFontIcon;
export declare const Cellular4G24Filled: FluentFontIcon;
export declare const CellularData120Filled: FluentFontIcon;
export declare const CellularData124Filled: FluentFontIcon;
export declare const CellularData220Filled: FluentFontIcon;
export declare const CellularData224Filled: FluentFontIcon;
export declare const CellularData320Filled: FluentFontIcon;
export declare const CellularData324Filled: FluentFontIcon;
export declare const CellularData420Filled: FluentFontIcon;
export declare const CellularData424Filled: FluentFontIcon;
