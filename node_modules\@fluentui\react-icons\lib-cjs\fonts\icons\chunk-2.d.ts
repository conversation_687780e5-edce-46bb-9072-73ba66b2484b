import type { FluentFontIcon } from "../../utils/fonts/createFluentFontIcon";
export declare const ChartPersonRegular: FluentFontIcon;
export declare const ChatFilled: FluentFontIcon;
export declare const ChatRegular: FluentFontIcon;
export declare const ChatAddFilled: FluentFontIcon;
export declare const ChatAddRegular: FluentFontIcon;
export declare const ChatArrowBackFilled: FluentFontIcon;
export declare const ChatArrowBackRegular: FluentFontIcon;
export declare const ChatArrowBackDownFilled: FluentFontIcon;
export declare const ChatArrowBackDownRegular: FluentFontIcon;
export declare const ChatArrowDoubleBackFilled: FluentFontIcon;
export declare const ChatArrowDoubleBackRegular: FluentFontIcon;
export declare const ChatBubblesQuestionFilled: FluentFontIcon;
export declare const ChatBubblesQuestionRegular: FluentFontIcon;
export declare const ChatCursorFilled: FluentFontIcon;
export declare const ChatCursorRegular: FluentFontIcon;
export declare const ChatDismissFilled: FluentFontIcon;
export declare const ChatDismissRegular: FluentFontIcon;
export declare const ChatEmptyFilled: FluentFontIcon;
export declare const ChatEmptyRegular: FluentFontIcon;
export declare const ChatHelpFilled: FluentFontIcon;
export declare const ChatHelpRegular: FluentFontIcon;
export declare const ChatHistoryFilled: FluentFontIcon;
export declare const ChatHistoryRegular: FluentFontIcon;
export declare const ChatLockFilled: FluentFontIcon;
export declare const ChatLockRegular: FluentFontIcon;
export declare const ChatMailFilled: FluentFontIcon;
export declare const ChatMailRegular: FluentFontIcon;
export declare const ChatMultipleFilled: FluentFontIcon;
export declare const ChatMultipleRegular: FluentFontIcon;
export declare const ChatMultipleCheckmarkFilled: FluentFontIcon;
export declare const ChatMultipleCheckmarkRegular: FluentFontIcon;
export declare const ChatMultipleHeartFilled: FluentFontIcon;
export declare const ChatMultipleHeartRegular: FluentFontIcon;
export declare const ChatMultipleMinusFilled: FluentFontIcon;
export declare const ChatMultipleMinusRegular: FluentFontIcon;
export declare const ChatOffFilled: FluentFontIcon;
export declare const ChatOffRegular: FluentFontIcon;
export declare const ChatSettingsFilled: FluentFontIcon;
export declare const ChatSettingsRegular: FluentFontIcon;
export declare const ChatSparkleFilled: FluentFontIcon;
export declare const ChatSparkleRegular: FluentFontIcon;
export declare const ChatVideoFilled: FluentFontIcon;
export declare const ChatVideoRegular: FluentFontIcon;
export declare const ChatWarningFilled: FluentFontIcon;
export declare const ChatWarningRegular: FluentFontIcon;
export declare const CheckFilled: FluentFontIcon;
export declare const CheckRegular: FluentFontIcon;
export declare const Checkbox1Filled: FluentFontIcon;
export declare const Checkbox1Regular: FluentFontIcon;
export declare const Checkbox2Filled: FluentFontIcon;
export declare const Checkbox2Regular: FluentFontIcon;
export declare const CheckboxArrowRightFilled: FluentFontIcon;
export declare const CheckboxArrowRightRegular: FluentFontIcon;
export declare const CheckboxCheckedFilled: FluentFontIcon;
export declare const CheckboxCheckedRegular: FluentFontIcon;
export declare const CheckboxCheckedSyncFilled: FluentFontIcon;
export declare const CheckboxCheckedSyncRegular: FluentFontIcon;
export declare const CheckboxIndeterminateFilled: FluentFontIcon;
export declare const CheckboxIndeterminateRegular: FluentFontIcon;
export declare const CheckboxPersonFilled: FluentFontIcon;
export declare const CheckboxPersonRegular: FluentFontIcon;
export declare const CheckboxUncheckedFilled: FluentFontIcon;
export declare const CheckboxUncheckedRegular: FluentFontIcon;
export declare const CheckboxWarningFilled: FluentFontIcon;
export declare const CheckboxWarningRegular: FluentFontIcon;
export declare const CheckmarkFilled: FluentFontIcon;
export declare const CheckmarkRegular: FluentFontIcon;
export declare const CheckmarkCircleFilled: FluentFontIcon;
export declare const CheckmarkCircleRegular: FluentFontIcon;
export declare const CheckmarkCircleHintFilled: FluentFontIcon;
export declare const CheckmarkCircleHintRegular: FluentFontIcon;
export declare const CheckmarkCircleSquareFilled: FluentFontIcon;
export declare const CheckmarkCircleSquareRegular: FluentFontIcon;
export declare const CheckmarkCircleWarningFilled: FluentFontIcon;
export declare const CheckmarkCircleWarningRegular: FluentFontIcon;
export declare const CheckmarkLockFilled: FluentFontIcon;
export declare const CheckmarkLockRegular: FluentFontIcon;
export declare const CheckmarkNoteFilled: FluentFontIcon;
export declare const CheckmarkNoteRegular: FluentFontIcon;
export declare const CheckmarkSquareFilled: FluentFontIcon;
export declare const CheckmarkSquareRegular: FluentFontIcon;
export declare const CheckmarkStarburstFilled: FluentFontIcon;
export declare const CheckmarkStarburstRegular: FluentFontIcon;
export declare const CheckmarkUnderlineCircleFilled: FluentFontIcon;
export declare const CheckmarkUnderlineCircleRegular: FluentFontIcon;
export declare const ChessFilled: FluentFontIcon;
export declare const ChessRegular: FluentFontIcon;
export declare const ChevronCircleDownFilled: FluentFontIcon;
export declare const ChevronCircleDownRegular: FluentFontIcon;
export declare const ChevronCircleLeftFilled: FluentFontIcon;
export declare const ChevronCircleLeftRegular: FluentFontIcon;
export declare const ChevronCircleRightFilled: FluentFontIcon;
export declare const ChevronCircleRightRegular: FluentFontIcon;
export declare const ChevronCircleUpFilled: FluentFontIcon;
export declare const ChevronCircleUpRegular: FluentFontIcon;
export declare const ChevronDoubleDownFilled: FluentFontIcon;
export declare const ChevronDoubleDownRegular: FluentFontIcon;
export declare const ChevronDoubleLeftFilled: FluentFontIcon;
export declare const ChevronDoubleLeftRegular: FluentFontIcon;
export declare const ChevronDoubleRightFilled: FluentFontIcon;
export declare const ChevronDoubleRightRegular: FluentFontIcon;
export declare const ChevronDoubleUpFilled: FluentFontIcon;
export declare const ChevronDoubleUpRegular: FluentFontIcon;
export declare const ChevronDownFilled: FluentFontIcon;
export declare const ChevronDownRegular: FluentFontIcon;
export declare const ChevronDownUpFilled: FluentFontIcon;
export declare const ChevronDownUpRegular: FluentFontIcon;
export declare const ChevronLeftFilled: FluentFontIcon;
export declare const ChevronLeftRegular: FluentFontIcon;
export declare const ChevronRightFilled: FluentFontIcon;
export declare const ChevronRightRegular: FluentFontIcon;
export declare const ChevronUpFilled: FluentFontIcon;
export declare const ChevronUpRegular: FluentFontIcon;
export declare const ChevronUpDownFilled: FluentFontIcon;
export declare const ChevronUpDownRegular: FluentFontIcon;
export declare const CircleFilled: FluentFontIcon;
export declare const CircleRegular: FluentFontIcon;
export declare const CircleEditFilled: FluentFontIcon;
export declare const CircleEditRegular: FluentFontIcon;
export declare const CircleEraserFilled: FluentFontIcon;
export declare const CircleEraserRegular: FluentFontIcon;
export declare const CircleHalfFillFilled: FluentFontIcon;
export declare const CircleHalfFillRegular: FluentFontIcon;
export declare const CircleHighlightFilled: FluentFontIcon;
export declare const CircleHighlightRegular: FluentFontIcon;
export declare const CircleHintFilled: FluentFontIcon;
export declare const CircleHintRegular: FluentFontIcon;
export declare const CircleHintCursorFilled: FluentFontIcon;
export declare const CircleHintCursorRegular: FluentFontIcon;
export declare const CircleHintDismissFilled: FluentFontIcon;
export declare const CircleHintDismissRegular: FluentFontIcon;
export declare const CircleHintHalfVerticalFilled: FluentFontIcon;
export declare const CircleHintHalfVerticalRegular: FluentFontIcon;
export declare const CircleImageFilled: FluentFontIcon;
export declare const CircleImageRegular: FluentFontIcon;
export declare const CircleLineFilled: FluentFontIcon;
export declare const CircleLineRegular: FluentFontIcon;
export declare const CircleMultipleConcentricFilled: FluentFontIcon;
export declare const CircleMultipleConcentricRegular: FluentFontIcon;
export declare const CircleMultipleHintCheckmarkFilled: FluentFontIcon;
export declare const CircleMultipleHintCheckmarkRegular: FluentFontIcon;
export declare const CircleMultipleSubtractCheckmarkFilled: FluentFontIcon;
export declare const CircleMultipleSubtractCheckmarkRegular: FluentFontIcon;
export declare const CircleOffFilled: FluentFontIcon;
export declare const CircleOffRegular: FluentFontIcon;
export declare const CircleShadowFilled: FluentFontIcon;
export declare const CircleShadowRegular: FluentFontIcon;
export declare const CircleSmallFilled: FluentFontIcon;
export declare const CircleSmallRegular: FluentFontIcon;
export declare const CircleSparkleFilled: FluentFontIcon;
export declare const CircleSparkleRegular: FluentFontIcon;
export declare const CityFilled: FluentFontIcon;
export declare const CityRegular: FluentFontIcon;
export declare const ClassFilled: FluentFontIcon;
export declare const ClassRegular: FluentFontIcon;
export declare const ClassificationFilled: FluentFontIcon;
export declare const ClassificationRegular: FluentFontIcon;
export declare const ClearFormattingFilled: FluentFontIcon;
export declare const ClearFormattingRegular: FluentFontIcon;
export declare const ClipboardFilled: FluentFontIcon;
export declare const ClipboardRegular: FluentFontIcon;
export declare const Clipboard3DayFilled: FluentFontIcon;
export declare const Clipboard3DayRegular: FluentFontIcon;
export declare const ClipboardArrowRightFilled: FluentFontIcon;
export declare const ClipboardArrowRightRegular: FluentFontIcon;
export declare const ClipboardBrushFilled: FluentFontIcon;
export declare const ClipboardBrushRegular: FluentFontIcon;
export declare const ClipboardBulletListFilled: FluentFontIcon;
export declare const ClipboardBulletListRegular: FluentFontIcon;
export declare const ClipboardBulletListLtrFilled: FluentFontIcon;
export declare const ClipboardBulletListLtrRegular: FluentFontIcon;
export declare const ClipboardBulletListRtlFilled: FluentFontIcon;
export declare const ClipboardBulletListRtlRegular: FluentFontIcon;
export declare const ClipboardCheckmarkFilled: FluentFontIcon;
export declare const ClipboardCheckmarkRegular: FluentFontIcon;
export declare const ClipboardClockFilled: FluentFontIcon;
export declare const ClipboardClockRegular: FluentFontIcon;
export declare const ClipboardCodeFilled: FluentFontIcon;
export declare const ClipboardCodeRegular: FluentFontIcon;
export declare const ClipboardDataBarFilled: FluentFontIcon;
export declare const ClipboardDataBarRegular: FluentFontIcon;
export declare const ClipboardDayFilled: FluentFontIcon;
export declare const ClipboardDayRegular: FluentFontIcon;
export declare const ClipboardEditFilled: FluentFontIcon;
export declare const ClipboardEditRegular: FluentFontIcon;
export declare const ClipboardErrorFilled: FluentFontIcon;
export declare const ClipboardErrorRegular: FluentFontIcon;
export declare const ClipboardHeartFilled: FluentFontIcon;
export declare const ClipboardHeartRegular: FluentFontIcon;
export declare const ClipboardImageFilled: FluentFontIcon;
export declare const ClipboardImageRegular: FluentFontIcon;
export declare const ClipboardLetterFilled: FluentFontIcon;
export declare const ClipboardLetterRegular: FluentFontIcon;
export declare const ClipboardLinkFilled: FluentFontIcon;
export declare const ClipboardLinkRegular: FluentFontIcon;
export declare const ClipboardMathFormulaFilled: FluentFontIcon;
export declare const ClipboardMathFormulaRegular: FluentFontIcon;
export declare const ClipboardMonthFilled: FluentFontIcon;
export declare const ClipboardMonthRegular: FluentFontIcon;
export declare const ClipboardMoreFilled: FluentFontIcon;
export declare const ClipboardMoreRegular: FluentFontIcon;
export declare const ClipboardNoteFilled: FluentFontIcon;
export declare const ClipboardNoteRegular: FluentFontIcon;
export declare const ClipboardNumber123Filled: FluentFontIcon;
export declare const ClipboardNumber123Regular: FluentFontIcon;
export declare const ClipboardPasteFilled: FluentFontIcon;
export declare const ClipboardPasteRegular: FluentFontIcon;
export declare const ClipboardPulseFilled: FluentFontIcon;
export declare const ClipboardPulseRegular: FluentFontIcon;
export declare const ClipboardSearchFilled: FluentFontIcon;
export declare const ClipboardSearchRegular: FluentFontIcon;
export declare const ClipboardSettingsFilled: FluentFontIcon;
export declare const ClipboardSettingsRegular: FluentFontIcon;
export declare const ClipboardTaskFilled: FluentFontIcon;
export declare const ClipboardTaskRegular: FluentFontIcon;
export declare const ClipboardTaskAddFilled: FluentFontIcon;
export declare const ClipboardTaskAddRegular: FluentFontIcon;
export declare const ClipboardTaskListLtrFilled: FluentFontIcon;
export declare const ClipboardTaskListLtrRegular: FluentFontIcon;
export declare const ClipboardTaskListRtlFilled: FluentFontIcon;
export declare const ClipboardTaskListRtlRegular: FluentFontIcon;
export declare const ClipboardTextEditFilled: FluentFontIcon;
export declare const ClipboardTextEditRegular: FluentFontIcon;
export declare const ClipboardTextLtrFilled: FluentFontIcon;
export declare const ClipboardTextLtrRegular: FluentFontIcon;
export declare const ClipboardTextRtlFilled: FluentFontIcon;
export declare const ClipboardTextRtlRegular: FluentFontIcon;
export declare const ClockFilled: FluentFontIcon;
export declare const ClockRegular: FluentFontIcon;
export declare const ClockAlarmFilled: FluentFontIcon;
export declare const ClockAlarmRegular: FluentFontIcon;
export declare const ClockArrowDownloadFilled: FluentFontIcon;
export declare const ClockArrowDownloadRegular: FluentFontIcon;
export declare const ClockBillFilled: FluentFontIcon;
export declare const ClockBillRegular: FluentFontIcon;
export declare const ClockDismissFilled: FluentFontIcon;
export declare const ClockDismissRegular: FluentFontIcon;
export declare const ClockLockFilled: FluentFontIcon;
export declare const ClockLockRegular: FluentFontIcon;
export declare const ClockPauseFilled: FluentFontIcon;
export declare const ClockPauseRegular: FluentFontIcon;
export declare const ClockSparkleFilled: FluentFontIcon;
export declare const ClockSparkleRegular: FluentFontIcon;
export declare const ClockToolboxFilled: FluentFontIcon;
export declare const ClockToolboxRegular: FluentFontIcon;
export declare const ClosedCaptionFilled: FluentFontIcon;
export declare const ClosedCaptionRegular: FluentFontIcon;
export declare const ClosedCaptionOffFilled: FluentFontIcon;
export declare const ClosedCaptionOffRegular: FluentFontIcon;
export declare const ClothesHangerFilled: FluentFontIcon;
export declare const ClothesHangerRegular: FluentFontIcon;
export declare const CloudFilled: FluentFontIcon;
export declare const CloudRegular: FluentFontIcon;
export declare const CloudAddFilled: FluentFontIcon;
export declare const CloudAddRegular: FluentFontIcon;
export declare const CloudArchiveFilled: FluentFontIcon;
export declare const CloudArchiveRegular: FluentFontIcon;
export declare const CloudArrowDownFilled: FluentFontIcon;
export declare const CloudArrowDownRegular: FluentFontIcon;
export declare const CloudArrowRightFilled: FluentFontIcon;
export declare const CloudArrowRightRegular: FluentFontIcon;
export declare const CloudArrowUpFilled: FluentFontIcon;
export declare const CloudArrowUpRegular: FluentFontIcon;
export declare const CloudBeakerFilled: FluentFontIcon;
export declare const CloudBeakerRegular: FluentFontIcon;
export declare const CloudBidirectionalFilled: FluentFontIcon;
export declare const CloudBidirectionalRegular: FluentFontIcon;
export declare const CloudCheckmarkFilled: FluentFontIcon;
export declare const CloudCheckmarkRegular: FluentFontIcon;
export declare const CloudCubeFilled: FluentFontIcon;
export declare const CloudCubeRegular: FluentFontIcon;
export declare const CloudDatabaseFilled: FluentFontIcon;
export declare const CloudDatabaseRegular: FluentFontIcon;
export declare const CloudDesktopFilled: FluentFontIcon;
export declare const CloudDesktopRegular: FluentFontIcon;
export declare const CloudDismissFilled: FluentFontIcon;
export declare const CloudDismissRegular: FluentFontIcon;
export declare const CloudEditFilled: FluentFontIcon;
export declare const CloudEditRegular: FluentFontIcon;
export declare const CloudErrorFilled: FluentFontIcon;
export declare const CloudErrorRegular: FluentFontIcon;
export declare const CloudFlowFilled: FluentFontIcon;
export declare const CloudFlowRegular: FluentFontIcon;
export declare const CloudLinkFilled: FluentFontIcon;
export declare const CloudLinkRegular: FluentFontIcon;
export declare const CloudOffFilled: FluentFontIcon;
export declare const CloudOffRegular: FluentFontIcon;
export declare const CloudSwapFilled: FluentFontIcon;
export declare const CloudSwapRegular: FluentFontIcon;
export declare const CloudSyncFilled: FluentFontIcon;
export declare const CloudSyncRegular: FluentFontIcon;
export declare const CloudWordsFilled: FluentFontIcon;
export declare const CloudWordsRegular: FluentFontIcon;
export declare const CloverFilled: FluentFontIcon;
export declare const CloverRegular: FluentFontIcon;
export declare const CodeFilled: FluentFontIcon;
export declare const CodeRegular: FluentFontIcon;
export declare const CodeBlockFilled: FluentFontIcon;
export declare const CodeBlockRegular: FluentFontIcon;
export declare const CodeBlockEditFilled: FluentFontIcon;
export declare const CodeBlockEditRegular: FluentFontIcon;
export declare const CodeCircleFilled: FluentFontIcon;
export declare const CodeCircleRegular: FluentFontIcon;
export declare const CodeTextFilled: FluentFontIcon;
export declare const CodeTextRegular: FluentFontIcon;
export declare const CodeTextEditFilled: FluentFontIcon;
export declare const CodeTextEditRegular: FluentFontIcon;
export declare const CoinMultipleFilled: FluentFontIcon;
export declare const CoinMultipleRegular: FluentFontIcon;
export declare const CoinStackFilled: FluentFontIcon;
export declare const CoinStackRegular: FluentFontIcon;
export declare const CollectionsFilled: FluentFontIcon;
export declare const CollectionsRegular: FluentFontIcon;
export declare const CollectionsAddFilled: FluentFontIcon;
export declare const CollectionsAddRegular: FluentFontIcon;
export declare const CollectionsEmptyFilled: FluentFontIcon;
export declare const CollectionsEmptyRegular: FluentFontIcon;
export declare const ColorFilled: FluentFontIcon;
export declare const ColorRegular: FluentFontIcon;
export declare const ColorBackgroundFilled: FluentFontIcon;
export declare const ColorBackgroundRegular: FluentFontIcon;
export declare const ColorBackgroundAccentRegular: FluentFontIcon;
export declare const ColorFillFilled: FluentFontIcon;
export declare const ColorFillRegular: FluentFontIcon;
export declare const ColorFillAccentRegular: FluentFontIcon;
export declare const ColorLineFilled: FluentFontIcon;
export declare const ColorLineRegular: FluentFontIcon;
export declare const ColorLineAccentRegular: FluentFontIcon;
export declare const ColumnFilled: FluentFontIcon;
export declare const ColumnRegular: FluentFontIcon;
export declare const ColumnArrowRightFilled: FluentFontIcon;
export declare const ColumnArrowRightRegular: FluentFontIcon;
export declare const ColumnDoubleCompareFilled: FluentFontIcon;
export declare const ColumnDoubleCompareRegular: FluentFontIcon;
export declare const ColumnEditFilled: FluentFontIcon;
export declare const ColumnEditRegular: FluentFontIcon;
export declare const ColumnSingleCompareFilled: FluentFontIcon;
export declare const ColumnSingleCompareRegular: FluentFontIcon;
export declare const ColumnTripleFilled: FluentFontIcon;
export declare const ColumnTripleRegular: FluentFontIcon;
export declare const ColumnTripleEditFilled: FluentFontIcon;
export declare const ColumnTripleEditRegular: FluentFontIcon;
export declare const CommaFilled: FluentFontIcon;
export declare const CommaRegular: FluentFontIcon;
export declare const CommentFilled: FluentFontIcon;
export declare const CommentRegular: FluentFontIcon;
export declare const CommentAddFilled: FluentFontIcon;
export declare const CommentAddRegular: FluentFontIcon;
export declare const CommentArrowLeftFilled: FluentFontIcon;
export declare const CommentArrowLeftRegular: FluentFontIcon;
export declare const CommentArrowRightFilled: FluentFontIcon;
export declare const CommentArrowRightRegular: FluentFontIcon;
export declare const CommentBadgeFilled: FluentFontIcon;
export declare const CommentBadgeRegular: FluentFontIcon;
export declare const CommentCheckmarkFilled: FluentFontIcon;
export declare const CommentCheckmarkRegular: FluentFontIcon;
export declare const CommentDismissFilled: FluentFontIcon;
export declare const CommentDismissRegular: FluentFontIcon;
export declare const CommentEditFilled: FluentFontIcon;
export declare const CommentEditRegular: FluentFontIcon;
export declare const CommentErrorFilled: FluentFontIcon;
export declare const CommentErrorRegular: FluentFontIcon;
export declare const CommentLightningFilled: FluentFontIcon;
export declare const CommentLightningRegular: FluentFontIcon;
export declare const CommentLinkFilled: FluentFontIcon;
export declare const CommentLinkRegular: FluentFontIcon;
export declare const CommentMentionFilled: FluentFontIcon;
export declare const CommentMentionRegular: FluentFontIcon;
export declare const CommentMultipleFilled: FluentFontIcon;
export declare const CommentMultipleRegular: FluentFontIcon;
export declare const CommentMultipleCheckmarkFilled: FluentFontIcon;
export declare const CommentMultipleCheckmarkRegular: FluentFontIcon;
export declare const CommentMultipleLinkFilled: FluentFontIcon;
export declare const CommentMultipleLinkRegular: FluentFontIcon;
export declare const CommentMultipleMentionFilled: FluentFontIcon;
export declare const CommentMultipleMentionRegular: FluentFontIcon;
export declare const CommentNoteFilled: FluentFontIcon;
export declare const CommentNoteRegular: FluentFontIcon;
export declare const CommentOffFilled: FluentFontIcon;
export declare const CommentOffRegular: FluentFontIcon;
export declare const CommentQuoteFilled: FluentFontIcon;
export declare const CommentQuoteRegular: FluentFontIcon;
export declare const CommentTextFilled: FluentFontIcon;
export declare const CommentTextRegular: FluentFontIcon;
export declare const CommunicationFilled: FluentFontIcon;
export declare const CommunicationRegular: FluentFontIcon;
export declare const CommunicationPersonFilled: FluentFontIcon;
export declare const CommunicationPersonRegular: FluentFontIcon;
export declare const CommunicationShieldFilled: FluentFontIcon;
export declare const CommunicationShieldRegular: FluentFontIcon;
export declare const CompassNorthwestFilled: FluentFontIcon;
export declare const CompassNorthwestRegular: FluentFontIcon;
export declare const CompassTrueNorthFilled: FluentFontIcon;
export declare const CompassTrueNorthRegular: FluentFontIcon;
export declare const ComposeFilled: FluentFontIcon;
export declare const ComposeRegular: FluentFontIcon;
export declare const ConferenceRoomFilled: FluentFontIcon;
export declare const ConferenceRoomRegular: FluentFontIcon;
export declare const ConnectedFilled: FluentFontIcon;
export declare const ConnectedRegular: FluentFontIcon;
export declare const ConnectorFilled: FluentFontIcon;
export declare const ConnectorRegular: FluentFontIcon;
export declare const ContactCardFilled: FluentFontIcon;
export declare const ContactCardRegular: FluentFontIcon;
export declare const ContactCardGroupFilled: FluentFontIcon;
export declare const ContactCardGroupRegular: FluentFontIcon;
export declare const ContactCardLinkFilled: FluentFontIcon;
export declare const ContactCardLinkRegular: FluentFontIcon;
export declare const ContactCardRibbonFilled: FluentFontIcon;
export declare const ContactCardRibbonRegular: FluentFontIcon;
export declare const ContentSettingsFilled: FluentFontIcon;
export declare const ContentSettingsRegular: FluentFontIcon;
export declare const ContentViewFilled: FluentFontIcon;
export declare const ContentViewRegular: FluentFontIcon;
export declare const ContentViewGalleryFilled: FluentFontIcon;
export declare const ContentViewGalleryRegular: FluentFontIcon;
export declare const ContentViewGalleryLightningFilled: FluentFontIcon;
export declare const ContentViewGalleryLightningRegular: FluentFontIcon;
export declare const ContractDownLeftFilled: FluentFontIcon;
export declare const ContractDownLeftRegular: FluentFontIcon;
export declare const ContractUpRightFilled: FluentFontIcon;
export declare const ContractUpRightRegular: FluentFontIcon;
export declare const ControlButtonFilled: FluentFontIcon;
export declare const ControlButtonRegular: FluentFontIcon;
export declare const ConvertRangeFilled: FluentFontIcon;
export declare const ConvertRangeRegular: FluentFontIcon;
export declare const CookiesFilled: FluentFontIcon;
export declare const CookiesRegular: FluentFontIcon;
export declare const CopyFilled: FluentFontIcon;
export declare const CopyRegular: FluentFontIcon;
export declare const CopyAddFilled: FluentFontIcon;
export declare const CopyAddRegular: FluentFontIcon;
export declare const CopyArrowRightFilled: FluentFontIcon;
export declare const CopyArrowRightRegular: FluentFontIcon;
export declare const CopySelectFilled: FluentFontIcon;
export declare const CopySelectRegular: FluentFontIcon;
export declare const CouchFilled: FluentFontIcon;
export declare const CouchRegular: FluentFontIcon;
export declare const CreditCardClockFilled: FluentFontIcon;
export declare const CreditCardClockRegular: FluentFontIcon;
export declare const CreditCardPersonFilled: FluentFontIcon;
export declare const CreditCardPersonRegular: FluentFontIcon;
export declare const CreditCardToolboxFilled: FluentFontIcon;
export declare const CreditCardToolboxRegular: FluentFontIcon;
export declare const CropFilled: FluentFontIcon;
export declare const CropRegular: FluentFontIcon;
export declare const CropArrowRotateFilled: FluentFontIcon;
export declare const CropArrowRotateRegular: FluentFontIcon;
export declare const CropInterimFilled: FluentFontIcon;
export declare const CropInterimRegular: FluentFontIcon;
export declare const CropInterimOffFilled: FluentFontIcon;
export declare const CropInterimOffRegular: FluentFontIcon;
export declare const CrownFilled: FluentFontIcon;
export declare const CrownRegular: FluentFontIcon;
export declare const CrownSubtractFilled: FluentFontIcon;
export declare const CrownSubtractRegular: FluentFontIcon;
export declare const CubeFilled: FluentFontIcon;
export declare const CubeRegular: FluentFontIcon;
export declare const CubeAddFilled: FluentFontIcon;
export declare const CubeAddRegular: FluentFontIcon;
export declare const CubeArrowCurveDownFilled: FluentFontIcon;
export declare const CubeArrowCurveDownRegular: FluentFontIcon;
export declare const CubeCheckmarkFilled: FluentFontIcon;
export declare const CubeCheckmarkRegular: FluentFontIcon;
export declare const CubeLinkFilled: FluentFontIcon;
export declare const CubeLinkRegular: FluentFontIcon;
export declare const CubeMultipleFilled: FluentFontIcon;
export declare const CubeMultipleRegular: FluentFontIcon;
export declare const CubeQuickFilled: FluentFontIcon;
export declare const CubeQuickRegular: FluentFontIcon;
export declare const CubeRotateFilled: FluentFontIcon;
export declare const CubeRotateRegular: FluentFontIcon;
export declare const CubeSyncFilled: FluentFontIcon;
export declare const CubeSyncRegular: FluentFontIcon;
export declare const CubeTreeFilled: FluentFontIcon;
export declare const CubeTreeRegular: FluentFontIcon;
export declare const CurrencyDollarEuroFilled: FluentFontIcon;
export declare const CurrencyDollarEuroRegular: FluentFontIcon;
export declare const CurrencyDollarRupeeFilled: FluentFontIcon;
export declare const CurrencyDollarRupeeRegular: FluentFontIcon;
export declare const CursorFilled: FluentFontIcon;
export declare const CursorRegular: FluentFontIcon;
export declare const CursorClickFilled: FluentFontIcon;
export declare const CursorClickRegular: FluentFontIcon;
export declare const CursorHoverFilled: FluentFontIcon;
export declare const CursorHoverRegular: FluentFontIcon;
export declare const CursorHoverOffFilled: FluentFontIcon;
export declare const CursorHoverOffRegular: FluentFontIcon;
export declare const CursorProhibitedFilled: FluentFontIcon;
export declare const CursorProhibitedRegular: FluentFontIcon;
export declare const CutFilled: FluentFontIcon;
export declare const CutRegular: FluentFontIcon;
export declare const DarkThemeFilled: FluentFontIcon;
export declare const DarkThemeRegular: FluentFontIcon;
export declare const DataAreaFilled: FluentFontIcon;
export declare const DataAreaRegular: FluentFontIcon;
export declare const DataBarHorizontalFilled: FluentFontIcon;
export declare const DataBarHorizontalRegular: FluentFontIcon;
export declare const DataBarVerticalFilled: FluentFontIcon;
export declare const DataBarVerticalRegular: FluentFontIcon;
