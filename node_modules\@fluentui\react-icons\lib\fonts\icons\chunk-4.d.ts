import type { FluentFontIcon } from "../../utils/fonts/createFluentFontIcon";
export declare const DustFilled: FluentFontIcon;
export declare const DustRegular: FluentFontIcon;
export declare const EarthFilled: FluentFontIcon;
export declare const EarthRegular: FluentFontIcon;
export declare const EarthLeafFilled: FluentFontIcon;
export declare const EarthLeafRegular: FluentFontIcon;
export declare const EditFilled: FluentFontIcon;
export declare const EditRegular: FluentFontIcon;
export declare const EditArrowBackFilled: FluentFontIcon;
export declare const EditArrowBackRegular: FluentFontIcon;
export declare const EditLineHorizontal3Filled: FluentFontIcon;
export declare const EditLineHorizontal3Regular: FluentFontIcon;
export declare const EditLockFilled: FluentFontIcon;
export declare const EditLockRegular: FluentFontIcon;
export declare const EditOffFilled: FluentFontIcon;
export declare const EditOffRegular: FluentFontIcon;
export declare const EditPersonFilled: FluentFontIcon;
export declare const EditPersonRegular: FluentFontIcon;
export declare const EditProhibitedFilled: FluentFontIcon;
export declare const EditProhibitedRegular: FluentFontIcon;
export declare const EditSettingsFilled: FluentFontIcon;
export declare const EditSettingsRegular: FluentFontIcon;
export declare const ElevatorFilled: FluentFontIcon;
export declare const ElevatorRegular: FluentFontIcon;
export declare const EmojiFilled: FluentFontIcon;
export declare const EmojiRegular: FluentFontIcon;
export declare const EmojiAddFilled: FluentFontIcon;
export declare const EmojiAddRegular: FluentFontIcon;
export declare const EmojiAngryFilled: FluentFontIcon;
export declare const EmojiAngryRegular: FluentFontIcon;
export declare const EmojiEditFilled: FluentFontIcon;
export declare const EmojiEditRegular: FluentFontIcon;
export declare const EmojiHandFilled: FluentFontIcon;
export declare const EmojiHandRegular: FluentFontIcon;
export declare const EmojiHintFilled: FluentFontIcon;
export declare const EmojiHintRegular: FluentFontIcon;
export declare const EmojiLaughFilled: FluentFontIcon;
export declare const EmojiLaughRegular: FluentFontIcon;
export declare const EmojiMehFilled: FluentFontIcon;
export declare const EmojiMehRegular: FluentFontIcon;
export declare const EmojiMemeFilled: FluentFontIcon;
export declare const EmojiMemeRegular: FluentFontIcon;
export declare const EmojiMultipleFilled: FluentFontIcon;
export declare const EmojiMultipleRegular: FluentFontIcon;
export declare const EmojiSadFilled: FluentFontIcon;
export declare const EmojiSadRegular: FluentFontIcon;
export declare const EmojiSadSlightFilled: FluentFontIcon;
export declare const EmojiSadSlightRegular: FluentFontIcon;
export declare const EmojiSmileSlightFilled: FluentFontIcon;
export declare const EmojiSmileSlightRegular: FluentFontIcon;
export declare const EmojiSparkleFilled: FluentFontIcon;
export declare const EmojiSparkleRegular: FluentFontIcon;
export declare const EmojiSurpriseFilled: FluentFontIcon;
export declare const EmojiSurpriseRegular: FluentFontIcon;
export declare const EngineFilled: FluentFontIcon;
export declare const EngineRegular: FluentFontIcon;
export declare const EqualCircleFilled: FluentFontIcon;
export declare const EqualCircleRegular: FluentFontIcon;
export declare const EqualOffFilled: FluentFontIcon;
export declare const EqualOffRegular: FluentFontIcon;
export declare const EraserFilled: FluentFontIcon;
export declare const EraserRegular: FluentFontIcon;
export declare const EraserMediumFilled: FluentFontIcon;
export declare const EraserMediumRegular: FluentFontIcon;
export declare const EraserSegmentFilled: FluentFontIcon;
export declare const EraserSegmentRegular: FluentFontIcon;
export declare const EraserSmallFilled: FluentFontIcon;
export declare const EraserSmallRegular: FluentFontIcon;
export declare const EraserToolFilled: FluentFontIcon;
export declare const EraserToolRegular: FluentFontIcon;
export declare const ErrorCircleFilled: FluentFontIcon;
export declare const ErrorCircleRegular: FluentFontIcon;
export declare const ErrorCircleSettingsFilled: FluentFontIcon;
export declare const ErrorCircleSettingsRegular: FluentFontIcon;
export declare const ExpandUpLeftFilled: FluentFontIcon;
export declare const ExpandUpLeftRegular: FluentFontIcon;
export declare const ExpandUpRightFilled: FluentFontIcon;
export declare const ExpandUpRightRegular: FluentFontIcon;
export declare const ExtendedDockFilled: FluentFontIcon;
export declare const ExtendedDockRegular: FluentFontIcon;
export declare const EyeFilled: FluentFontIcon;
export declare const EyeRegular: FluentFontIcon;
export declare const EyeLinesFilled: FluentFontIcon;
export declare const EyeLinesRegular: FluentFontIcon;
export declare const EyeOffFilled: FluentFontIcon;
export declare const EyeOffRegular: FluentFontIcon;
export declare const EyeTrackingFilled: FluentFontIcon;
export declare const EyeTrackingRegular: FluentFontIcon;
export declare const EyeTrackingOffFilled: FluentFontIcon;
export declare const EyeTrackingOffRegular: FluentFontIcon;
export declare const EyedropperFilled: FluentFontIcon;
export declare const EyedropperRegular: FluentFontIcon;
export declare const EyedropperOffFilled: FluentFontIcon;
export declare const EyedropperOffRegular: FluentFontIcon;
export declare const FStopFilled: FluentFontIcon;
export declare const FStopRegular: FluentFontIcon;
export declare const FastAccelerationFilled: FluentFontIcon;
export declare const FastAccelerationRegular: FluentFontIcon;
export declare const FastForwardFilled: FluentFontIcon;
export declare const FastForwardRegular: FluentFontIcon;
export declare const FaxFilled: FluentFontIcon;
export declare const FaxRegular: FluentFontIcon;
export declare const FeedFilled: FluentFontIcon;
export declare const FeedRegular: FluentFontIcon;
export declare const FilmstripFilled: FluentFontIcon;
export declare const FilmstripRegular: FluentFontIcon;
export declare const FilmstripImageFilled: FluentFontIcon;
export declare const FilmstripImageRegular: FluentFontIcon;
export declare const FilmstripPlayFilled: FluentFontIcon;
export declare const FilmstripPlayRegular: FluentFontIcon;
export declare const FilmstripSplitFilled: FluentFontIcon;
export declare const FilmstripSplitRegular: FluentFontIcon;
export declare const FilterFilled: FluentFontIcon;
export declare const FilterRegular: FluentFontIcon;
export declare const FilterAddFilled: FluentFontIcon;
export declare const FilterAddRegular: FluentFontIcon;
export declare const FilterDismissFilled: FluentFontIcon;
export declare const FilterDismissRegular: FluentFontIcon;
export declare const FilterSyncFilled: FluentFontIcon;
export declare const FilterSyncRegular: FluentFontIcon;
export declare const FingerprintFilled: FluentFontIcon;
export declare const FingerprintRegular: FluentFontIcon;
export declare const FireFilled: FluentFontIcon;
export declare const FireRegular: FluentFontIcon;
export declare const FireplaceFilled: FluentFontIcon;
export declare const FireplaceRegular: FluentFontIcon;
export declare const FixedWidthFilled: FluentFontIcon;
export declare const FixedWidthRegular: FluentFontIcon;
export declare const FlagFilled: FluentFontIcon;
export declare const FlagRegular: FluentFontIcon;
export declare const FlagCheckeredFilled: FluentFontIcon;
export declare const FlagCheckeredRegular: FluentFontIcon;
export declare const FlagClockFilled: FluentFontIcon;
export declare const FlagClockRegular: FluentFontIcon;
export declare const FlagOffFilled: FluentFontIcon;
export declare const FlagOffRegular: FluentFontIcon;
export declare const FlagPrideFilled: FluentFontIcon;
export declare const FlagPrideIntersexInclusiveProgressFilled: FluentFontIcon;
export declare const FlagPridePhiladelphiaFilled: FluentFontIcon;
export declare const FlagPrideProgressFilled: FluentFontIcon;
export declare const FlashFilled: FluentFontIcon;
export declare const FlashRegular: FluentFontIcon;
export declare const FlashAddFilled: FluentFontIcon;
export declare const FlashAddRegular: FluentFontIcon;
export declare const FlashAutoFilled: FluentFontIcon;
export declare const FlashAutoRegular: FluentFontIcon;
export declare const FlashCheckmarkFilled: FluentFontIcon;
export declare const FlashCheckmarkRegular: FluentFontIcon;
export declare const FlashFlowFilled: FluentFontIcon;
export declare const FlashFlowRegular: FluentFontIcon;
export declare const FlashOffFilled: FluentFontIcon;
export declare const FlashOffRegular: FluentFontIcon;
export declare const FlashPlayFilled: FluentFontIcon;
export declare const FlashPlayRegular: FluentFontIcon;
export declare const FlashSettingsFilled: FluentFontIcon;
export declare const FlashSettingsRegular: FluentFontIcon;
export declare const FlashSparkleFilled: FluentFontIcon;
export declare const FlashSparkleRegular: FluentFontIcon;
export declare const FlashlightFilled: FluentFontIcon;
export declare const FlashlightRegular: FluentFontIcon;
export declare const FlashlightOffFilled: FluentFontIcon;
export declare const FlashlightOffRegular: FluentFontIcon;
export declare const FlipHorizontalFilled: FluentFontIcon;
export declare const FlipHorizontalRegular: FluentFontIcon;
export declare const FlipVerticalFilled: FluentFontIcon;
export declare const FlipVerticalRegular: FluentFontIcon;
export declare const FlowFilled: FluentFontIcon;
export declare const FlowRegular: FluentFontIcon;
export declare const FlowDotFilled: FluentFontIcon;
export declare const FlowDotRegular: FluentFontIcon;
export declare const FlowSparkleFilled: FluentFontIcon;
export declare const FlowSparkleRegular: FluentFontIcon;
export declare const FlowchartFilled: FluentFontIcon;
export declare const FlowchartRegular: FluentFontIcon;
export declare const FlowchartCircleFilled: FluentFontIcon;
export declare const FlowchartCircleRegular: FluentFontIcon;
export declare const FluentFilled: FluentFontIcon;
export declare const FluentRegular: FluentFontIcon;
export declare const FluidFilled: FluentFontIcon;
export declare const FluidRegular: FluentFontIcon;
export declare const FolderFilled: FluentFontIcon;
export declare const FolderRegular: FluentFontIcon;
export declare const FolderAddFilled: FluentFontIcon;
export declare const FolderAddRegular: FluentFontIcon;
export declare const FolderArrowLeftFilled: FluentFontIcon;
export declare const FolderArrowLeftRegular: FluentFontIcon;
export declare const FolderArrowRightFilled: FluentFontIcon;
export declare const FolderArrowRightRegular: FluentFontIcon;
export declare const FolderArrowUpFilled: FluentFontIcon;
export declare const FolderArrowUpRegular: FluentFontIcon;
export declare const FolderBriefcaseFilled: FluentFontIcon;
export declare const FolderBriefcaseRegular: FluentFontIcon;
export declare const FolderDocumentFilled: FluentFontIcon;
export declare const FolderDocumentRegular: FluentFontIcon;
export declare const FolderGlobeFilled: FluentFontIcon;
export declare const FolderGlobeRegular: FluentFontIcon;
export declare const FolderLightningFilled: FluentFontIcon;
export declare const FolderLightningRegular: FluentFontIcon;
export declare const FolderLinkFilled: FluentFontIcon;
export declare const FolderLinkRegular: FluentFontIcon;
export declare const FolderListFilled: FluentFontIcon;
export declare const FolderListRegular: FluentFontIcon;
export declare const FolderMailFilled: FluentFontIcon;
export declare const FolderMailRegular: FluentFontIcon;
export declare const FolderOpenFilled: FluentFontIcon;
export declare const FolderOpenRegular: FluentFontIcon;
export declare const FolderOpenDownFilled: FluentFontIcon;
export declare const FolderOpenDownRegular: FluentFontIcon;
export declare const FolderOpenVerticalFilled: FluentFontIcon;
export declare const FolderOpenVerticalRegular: FluentFontIcon;
export declare const FolderPeopleFilled: FluentFontIcon;
export declare const FolderPeopleRegular: FluentFontIcon;
export declare const FolderPersonFilled: FluentFontIcon;
export declare const FolderPersonRegular: FluentFontIcon;
export declare const FolderProhibitedFilled: FluentFontIcon;
export declare const FolderProhibitedRegular: FluentFontIcon;
export declare const FolderSearchFilled: FluentFontIcon;
export declare const FolderSearchRegular: FluentFontIcon;
export declare const FolderSwapFilled: FluentFontIcon;
export declare const FolderSwapRegular: FluentFontIcon;
export declare const FolderSyncFilled: FluentFontIcon;
export declare const FolderSyncRegular: FluentFontIcon;
export declare const FolderZipFilled: FluentFontIcon;
export declare const FolderZipRegular: FluentFontIcon;
export declare const FontDecreaseFilled: FluentFontIcon;
export declare const FontDecreaseRegular: FluentFontIcon;
export declare const FontIncreaseFilled: FluentFontIcon;
export declare const FontIncreaseRegular: FluentFontIcon;
export declare const FontSpaceTrackingInFilled: FluentFontIcon;
export declare const FontSpaceTrackingInRegular: FluentFontIcon;
export declare const FontSpaceTrackingOutFilled: FluentFontIcon;
export declare const FontSpaceTrackingOutRegular: FluentFontIcon;
export declare const FoodFilled: FluentFontIcon;
export declare const FoodRegular: FluentFontIcon;
export declare const FoodAppleFilled: FluentFontIcon;
export declare const FoodAppleRegular: FluentFontIcon;
export declare const FoodCakeFilled: FluentFontIcon;
export declare const FoodCakeRegular: FluentFontIcon;
export declare const FoodCarrotFilled: FluentFontIcon;
export declare const FoodCarrotRegular: FluentFontIcon;
export declare const FoodChickenLegFilled: FluentFontIcon;
export declare const FoodChickenLegRegular: FluentFontIcon;
export declare const FoodEggFilled: FluentFontIcon;
export declare const FoodEggRegular: FluentFontIcon;
export declare const FoodFishFilled: FluentFontIcon;
export declare const FoodFishRegular: FluentFontIcon;
export declare const FoodGrainsFilled: FluentFontIcon;
export declare const FoodGrainsRegular: FluentFontIcon;
export declare const FoodPizzaFilled: FluentFontIcon;
export declare const FoodPizzaRegular: FluentFontIcon;
export declare const FoodToastFilled: FluentFontIcon;
export declare const FoodToastRegular: FluentFontIcon;
export declare const FormFilled: FluentFontIcon;
export declare const FormRegular: FluentFontIcon;
export declare const FormMultipleFilled: FluentFontIcon;
export declare const FormMultipleRegular: FluentFontIcon;
export declare const FormMultipleCollectionFilled: FluentFontIcon;
export declare const FormMultipleCollectionRegular: FluentFontIcon;
export declare const FormNewFilled: FluentFontIcon;
export declare const FormNewRegular: FluentFontIcon;
export declare const FormSparkleFilled: FluentFontIcon;
export declare const FormSparkleRegular: FluentFontIcon;
export declare const Fps120Filled: FluentFontIcon;
export declare const Fps120Regular: FluentFontIcon;
export declare const Fps240Filled: FluentFontIcon;
export declare const Fps240Regular: FluentFontIcon;
export declare const Fps30Filled: FluentFontIcon;
export declare const Fps30Regular: FluentFontIcon;
export declare const Fps60Filled: FluentFontIcon;
export declare const Fps60Regular: FluentFontIcon;
export declare const Fps960Filled: FluentFontIcon;
export declare const Fps960Regular: FluentFontIcon;
export declare const FrameFilled: FluentFontIcon;
export declare const FrameRegular: FluentFontIcon;
export declare const FullScreenMaximizeFilled: FluentFontIcon;
export declare const FullScreenMaximizeRegular: FluentFontIcon;
export declare const FullScreenMinimizeFilled: FluentFontIcon;
export declare const FullScreenMinimizeRegular: FluentFontIcon;
export declare const GameChatFilled: FluentFontIcon;
export declare const GameChatRegular: FluentFontIcon;
export declare const GamesFilled: FluentFontIcon;
export declare const GamesRegular: FluentFontIcon;
export declare const GanttChartFilled: FluentFontIcon;
export declare const GanttChartRegular: FluentFontIcon;
export declare const GasFilled: FluentFontIcon;
export declare const GasRegular: FluentFontIcon;
export declare const GasPumpFilled: FluentFontIcon;
export declare const GasPumpRegular: FluentFontIcon;
export declare const GatherFilled: FluentFontIcon;
export declare const GatherRegular: FluentFontIcon;
export declare const GaugeFilled: FluentFontIcon;
export declare const GaugeRegular: FluentFontIcon;
export declare const GaugeAddFilled: FluentFontIcon;
export declare const GaugeAddRegular: FluentFontIcon;
export declare const GavelFilled: FluentFontIcon;
export declare const GavelRegular: FluentFontIcon;
export declare const GavelProhibitedFilled: FluentFontIcon;
export declare const GavelProhibitedRegular: FluentFontIcon;
export declare const GestureFilled: FluentFontIcon;
export declare const GestureRegular: FluentFontIcon;
export declare const GifFilled: FluentFontIcon;
export declare const GifRegular: FluentFontIcon;
export declare const GiftFilled: FluentFontIcon;
export declare const GiftRegular: FluentFontIcon;
export declare const GiftCardFilled: FluentFontIcon;
export declare const GiftCardRegular: FluentFontIcon;
export declare const GiftCardAddFilled: FluentFontIcon;
export declare const GiftCardAddRegular: FluentFontIcon;
export declare const GiftCardArrowRightFilled: FluentFontIcon;
export declare const GiftCardArrowRightRegular: FluentFontIcon;
export declare const GiftCardMoneyFilled: FluentFontIcon;
export declare const GiftCardMoneyRegular: FluentFontIcon;
export declare const GiftCardMultipleFilled: FluentFontIcon;
export declare const GiftCardMultipleRegular: FluentFontIcon;
export declare const GiftOpenFilled: FluentFontIcon;
export declare const GiftOpenRegular: FluentFontIcon;
export declare const GlanceFilled: FluentFontIcon;
export declare const GlanceRegular: FluentFontIcon;
export declare const GlanceHorizontalFilled: FluentFontIcon;
export declare const GlanceHorizontalRegular: FluentFontIcon;
export declare const GlanceHorizontalSparklesFilled: FluentFontIcon;
export declare const GlanceHorizontalSparklesRegular: FluentFontIcon;
export declare const GlassesFilled: FluentFontIcon;
export declare const GlassesRegular: FluentFontIcon;
export declare const GlassesOffFilled: FluentFontIcon;
export declare const GlassesOffRegular: FluentFontIcon;
export declare const GlobeFilled: FluentFontIcon;
export declare const GlobeRegular: FluentFontIcon;
export declare const GlobeAddFilled: FluentFontIcon;
export declare const GlobeAddRegular: FluentFontIcon;
export declare const GlobeArrowForwardFilled: FluentFontIcon;
export declare const GlobeArrowForwardRegular: FluentFontIcon;
export declare const GlobeArrowUpFilled: FluentFontIcon;
export declare const GlobeArrowUpRegular: FluentFontIcon;
export declare const GlobeClockFilled: FluentFontIcon;
export declare const GlobeClockRegular: FluentFontIcon;
export declare const GlobeDesktopFilled: FluentFontIcon;
export declare const GlobeDesktopRegular: FluentFontIcon;
export declare const GlobeErrorFilled: FluentFontIcon;
export declare const GlobeErrorRegular: FluentFontIcon;
export declare const GlobeLocationFilled: FluentFontIcon;
export declare const GlobeLocationRegular: FluentFontIcon;
export declare const GlobeOffFilled: FluentFontIcon;
export declare const GlobeOffRegular: FluentFontIcon;
export declare const GlobePersonFilled: FluentFontIcon;
export declare const GlobePersonRegular: FluentFontIcon;
export declare const GlobeProhibitedFilled: FluentFontIcon;
export declare const GlobeProhibitedRegular: FluentFontIcon;
export declare const GlobeSearchFilled: FluentFontIcon;
export declare const GlobeSearchRegular: FluentFontIcon;
export declare const GlobeShieldFilled: FluentFontIcon;
export declare const GlobeShieldRegular: FluentFontIcon;
export declare const GlobeStarFilled: FluentFontIcon;
export declare const GlobeStarRegular: FluentFontIcon;
export declare const GlobeSurfaceFilled: FluentFontIcon;
export declare const GlobeSurfaceRegular: FluentFontIcon;
export declare const GlobeSyncFilled: FluentFontIcon;
export declare const GlobeSyncRegular: FluentFontIcon;
export declare const GlobeVideoFilled: FluentFontIcon;
export declare const GlobeVideoRegular: FluentFontIcon;
export declare const GlobeWarningFilled: FluentFontIcon;
export declare const GlobeWarningRegular: FluentFontIcon;
export declare const GridFilled: FluentFontIcon;
export declare const GridRegular: FluentFontIcon;
export declare const GridDotsFilled: FluentFontIcon;
export declare const GridDotsRegular: FluentFontIcon;
export declare const GridKanbanFilled: FluentFontIcon;
export declare const GridKanbanRegular: FluentFontIcon;
export declare const GroupFilled: FluentFontIcon;
export declare const GroupRegular: FluentFontIcon;
export declare const GroupDismissFilled: FluentFontIcon;
export declare const GroupDismissRegular: FluentFontIcon;
export declare const GroupListFilled: FluentFontIcon;
export declare const GroupListRegular: FluentFontIcon;
export declare const GroupReturnFilled: FluentFontIcon;
export declare const GroupReturnRegular: FluentFontIcon;
export declare const GuardianFilled: FluentFontIcon;
export declare const GuardianRegular: FluentFontIcon;
export declare const GuestFilled: FluentFontIcon;
export declare const GuestRegular: FluentFontIcon;
export declare const GuestAddFilled: FluentFontIcon;
export declare const GuestAddRegular: FluentFontIcon;
export declare const GuitarFilled: FluentFontIcon;
export declare const GuitarRegular: FluentFontIcon;
export declare const HandDrawFilled: FluentFontIcon;
export declare const HandDrawRegular: FluentFontIcon;
export declare const HandLeftFilled: FluentFontIcon;
export declare const HandLeftRegular: FluentFontIcon;
export declare const HandLeftChatFilled: FluentFontIcon;
export declare const HandLeftChatRegular: FluentFontIcon;
export declare const HandMultipleFilled: FluentFontIcon;
export declare const HandMultipleRegular: FluentFontIcon;
export declare const HandOpenHeartFilled: FluentFontIcon;
export declare const HandOpenHeartRegular: FluentFontIcon;
export declare const HandPointFilled: FluentFontIcon;
export declare const HandPointRegular: FluentFontIcon;
export declare const HandRightFilled: FluentFontIcon;
export declare const HandRightRegular: FluentFontIcon;
export declare const HandRightOffFilled: FluentFontIcon;
export declare const HandRightOffRegular: FluentFontIcon;
export declare const HandWaveFilled: FluentFontIcon;
export declare const HandWaveRegular: FluentFontIcon;
export declare const HandshakeFilled: FluentFontIcon;
export declare const HandshakeRegular: FluentFontIcon;
export declare const HapticStrongFilled: FluentFontIcon;
export declare const HapticStrongRegular: FluentFontIcon;
export declare const HapticWeakFilled: FluentFontIcon;
export declare const HapticWeakRegular: FluentFontIcon;
export declare const HardDriveFilled: FluentFontIcon;
export declare const HardDriveRegular: FluentFontIcon;
export declare const HatGraduationFilled: FluentFontIcon;
export declare const HatGraduationRegular: FluentFontIcon;
export declare const HatGraduationAddFilled: FluentFontIcon;
export declare const HatGraduationAddRegular: FluentFontIcon;
export declare const HatGraduationSparkleFilled: FluentFontIcon;
export declare const HatGraduationSparkleRegular: FluentFontIcon;
export declare const HdFilled: FluentFontIcon;
export declare const HdRegular: FluentFontIcon;
export declare const HdOffFilled: FluentFontIcon;
export declare const HdOffRegular: FluentFontIcon;
export declare const HdrFilled: FluentFontIcon;
export declare const HdrRegular: FluentFontIcon;
export declare const HdrOffFilled: FluentFontIcon;
export declare const HdrOffRegular: FluentFontIcon;
export declare const HeadphonesFilled: FluentFontIcon;
export declare const HeadphonesRegular: FluentFontIcon;
export declare const HeadphonesSoundWaveFilled: FluentFontIcon;
export declare const HeadphonesSoundWaveRegular: FluentFontIcon;
export declare const HeadsetFilled: FluentFontIcon;
export declare const HeadsetRegular: FluentFontIcon;
export declare const HeadsetAddFilled: FluentFontIcon;
export declare const HeadsetAddRegular: FluentFontIcon;
export declare const HeadsetVrFilled: FluentFontIcon;
export declare const HeadsetVrRegular: FluentFontIcon;
export declare const HeartFilled: FluentFontIcon;
export declare const HeartRegular: FluentFontIcon;
export declare const HeartBrokenFilled: FluentFontIcon;
export declare const HeartBrokenRegular: FluentFontIcon;
export declare const HeartCircleFilled: FluentFontIcon;
export declare const HeartCircleRegular: FluentFontIcon;
export declare const HeartCircleHintFilled: FluentFontIcon;
export declare const HeartCircleHintRegular: FluentFontIcon;
export declare const HeartOffFilled: FluentFontIcon;
export declare const HeartOffRegular: FluentFontIcon;
export declare const HeartPulseFilled: FluentFontIcon;
export declare const HeartPulseRegular: FluentFontIcon;
export declare const HeartPulseCheckmarkFilled: FluentFontIcon;
export declare const HeartPulseCheckmarkRegular: FluentFontIcon;
export declare const HeartPulseErrorFilled: FluentFontIcon;
export declare const HeartPulseErrorRegular: FluentFontIcon;
export declare const HeartPulseWarningFilled: FluentFontIcon;
export declare const HeartPulseWarningRegular: FluentFontIcon;
export declare const HexagonFilled: FluentFontIcon;
export declare const HexagonRegular: FluentFontIcon;
export declare const HexagonSparkleFilled: FluentFontIcon;
export declare const HexagonSparkleRegular: FluentFontIcon;
export declare const HexagonThreeFilled: FluentFontIcon;
export declare const HexagonThreeRegular: FluentFontIcon;
export declare const HighlightFilled: FluentFontIcon;
export declare const HighlightRegular: FluentFontIcon;
export declare const HighlightAccentFilled: FluentFontIcon;
export declare const HighlightLinkFilled: FluentFontIcon;
export declare const HighlightLinkRegular: FluentFontIcon;
export declare const HighwayFilled: FluentFontIcon;
export declare const HighwayRegular: FluentFontIcon;
export declare const HistoryFilled: FluentFontIcon;
export declare const HistoryRegular: FluentFontIcon;
export declare const HistoryDismissFilled: FluentFontIcon;
export declare const HistoryDismissRegular: FluentFontIcon;
export declare const HomeFilled: FluentFontIcon;
export declare const HomeRegular: FluentFontIcon;
export declare const HomeAddFilled: FluentFontIcon;
export declare const HomeAddRegular: FluentFontIcon;
export declare const HomeCheckmarkFilled: FluentFontIcon;
export declare const HomeCheckmarkRegular: FluentFontIcon;
export declare const HomeDatabaseFilled: FluentFontIcon;
export declare const HomeDatabaseRegular: FluentFontIcon;
export declare const HomeEmptyFilled: FluentFontIcon;
export declare const HomeEmptyRegular: FluentFontIcon;
export declare const HomeGarageFilled: FluentFontIcon;
export declare const HomeGarageRegular: FluentFontIcon;
export declare const HomeHeartFilled: FluentFontIcon;
export declare const HomeHeartRegular: FluentFontIcon;
export declare const HomeMoreFilled: FluentFontIcon;
export declare const HomeMoreRegular: FluentFontIcon;
export declare const HomePersonFilled: FluentFontIcon;
export declare const HomePersonRegular: FluentFontIcon;
export declare const HomeSplitFilled: FluentFontIcon;
export declare const HomeSplitRegular: FluentFontIcon;
export declare const HourglassFilled: FluentFontIcon;
export declare const HourglassRegular: FluentFontIcon;
export declare const HourglassHalfFilled: FluentFontIcon;
export declare const HourglassHalfRegular: FluentFontIcon;
export declare const HourglassOneQuarterFilled: FluentFontIcon;
export declare const HourglassOneQuarterRegular: FluentFontIcon;
export declare const HourglassThreeQuarterFilled: FluentFontIcon;
export declare const HourglassThreeQuarterRegular: FluentFontIcon;
export declare const IconsFilled: FluentFontIcon;
export declare const IconsRegular: FluentFontIcon;
export declare const ImageFilled: FluentFontIcon;
