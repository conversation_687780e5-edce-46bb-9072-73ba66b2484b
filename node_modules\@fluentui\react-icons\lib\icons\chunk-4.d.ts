import type { FluentIcon } from "../utils/createFluentIcon";
export declare const ScanThumbUpFilled: FluentIcon;
export declare const ScanThumbUpRegular: FluentIcon;
export declare const ScanThumbUpOffFilled: FluentIcon;
export declare const ScanThumbUpOffRegular: FluentIcon;
export declare const ScanTypeColor: FluentIcon;
export declare const ScanTypeFilled: FluentIcon;
export declare const ScanTypeRegular: FluentIcon;
export declare const ScanTypeCheckmarkFilled: FluentIcon;
export declare const ScanTypeCheckmarkRegular: FluentIcon;
export declare const ScanTypeOffFilled: FluentIcon;
export declare const ScanTypeOffRegular: FluentIcon;
export declare const ScratchpadFilled: FluentIcon;
export declare const ScratchpadRegular: FluentIcon;
export declare const ScreenCutFilled: FluentIcon;
export declare const ScreenCutRegular: FluentIcon;
export declare const ScreenPersonFilled: FluentIcon;
export declare const ScreenPersonRegular: FluentIcon;
export declare const ScreenSearchFilled: FluentIcon;
export declare const ScreenSearchRegular: FluentIcon;
export declare const ScreenshotFilled: FluentIcon;
export declare const ScreenshotRegular: FluentIcon;
export declare const ScreenshotRecordFilled: FluentIcon;
export declare const ScreenshotRecordRegular: FluentIcon;
export declare const ScriptFilled: FluentIcon;
export declare const ScriptRegular: FluentIcon;
export declare const SearchFilled: FluentIcon;
export declare const SearchRegular: FluentIcon;
export declare const SearchInfoFilled: FluentIcon;
export declare const SearchInfoRegular: FluentIcon;
export declare const SearchSettingsFilled: FluentIcon;
export declare const SearchSettingsRegular: FluentIcon;
export declare const SearchShieldFilled: FluentIcon;
export declare const SearchShieldRegular: FluentIcon;
export declare const SearchSparkleColor: FluentIcon;
export declare const SearchSparkleFilled: FluentIcon;
export declare const SearchSparkleRegular: FluentIcon;
export declare const SearchSquareFilled: FluentIcon;
export declare const SearchSquareRegular: FluentIcon;
export declare const SearchVisualColor: FluentIcon;
export declare const SearchVisualFilled: FluentIcon;
export declare const SearchVisualRegular: FluentIcon;
export declare const SeatFilled: FluentIcon;
export declare const SeatRegular: FluentIcon;
export declare const SeatAddFilled: FluentIcon;
export declare const SeatAddRegular: FluentIcon;
export declare const SelectAllOffFilled: FluentIcon;
export declare const SelectAllOffRegular: FluentIcon;
export declare const SelectAllOnFilled: FluentIcon;
export declare const SelectAllOnRegular: FluentIcon;
export declare const SelectObjectFilled: FluentIcon;
export declare const SelectObjectRegular: FluentIcon;
export declare const SelectObjectSkewFilled: FluentIcon;
export declare const SelectObjectSkewRegular: FluentIcon;
export declare const SelectObjectSkewDismissFilled: FluentIcon;
export declare const SelectObjectSkewDismissRegular: FluentIcon;
export declare const SelectObjectSkewEditFilled: FluentIcon;
export declare const SelectObjectSkewEditRegular: FluentIcon;
export declare const SendColor: FluentIcon;
export declare const SendFilled: FluentIcon;
export declare const SendRegular: FluentIcon;
export declare const SendBeakerFilled: FluentIcon;
export declare const SendBeakerRegular: FluentIcon;
export declare const SendClockColor: FluentIcon;
export declare const SendClockFilled: FluentIcon;
export declare const SendClockRegular: FluentIcon;
export declare const SendCopyFilled: FluentIcon;
export declare const SendCopyRegular: FluentIcon;
export declare const SendPersonFilled: FluentIcon;
export declare const SendPersonRegular: FluentIcon;
export declare const SerialPortFilled: FluentIcon;
export declare const SerialPortRegular: FluentIcon;
export declare const ServerFilled: FluentIcon;
export declare const ServerRegular: FluentIcon;
export declare const ServerLinkFilled: FluentIcon;
export declare const ServerLinkRegular: FluentIcon;
export declare const ServerMultipleFilled: FluentIcon;
export declare const ServerMultipleRegular: FluentIcon;
export declare const ServerPlayFilled: FluentIcon;
export declare const ServerPlayRegular: FluentIcon;
export declare const ServiceBellFilled: FluentIcon;
export declare const ServiceBellRegular: FluentIcon;
export declare const SettingsColor: FluentIcon;
export declare const SettingsFilled: FluentIcon;
export declare const SettingsRegular: FluentIcon;
export declare const SettingsChatFilled: FluentIcon;
export declare const SettingsChatRegular: FluentIcon;
export declare const SettingsCogMultipleFilled: FluentIcon;
export declare const SettingsCogMultipleRegular: FluentIcon;
export declare const ShapeExcludeFilled: FluentIcon;
export declare const ShapeExcludeRegular: FluentIcon;
export declare const ShapeIntersectFilled: FluentIcon;
export declare const ShapeIntersectRegular: FluentIcon;
export declare const ShapeOrganicFilled: FluentIcon;
export declare const ShapeOrganicRegular: FluentIcon;
export declare const ShapeSubtractFilled: FluentIcon;
export declare const ShapeSubtractRegular: FluentIcon;
export declare const ShapeUnionFilled: FluentIcon;
export declare const ShapeUnionRegular: FluentIcon;
export declare const ShapesFilled: FluentIcon;
export declare const ShapesRegular: FluentIcon;
export declare const ShareFilled: FluentIcon;
export declare const ShareRegular: FluentIcon;
export declare const ShareAndroidColor: FluentIcon;
export declare const ShareAndroidFilled: FluentIcon;
export declare const ShareAndroidRegular: FluentIcon;
export declare const ShareCloseTrayFilled: FluentIcon;
export declare const ShareCloseTrayRegular: FluentIcon;
export declare const ShareIosColor: FluentIcon;
export declare const ShareIosFilled: FluentIcon;
export declare const ShareIosRegular: FluentIcon;
export declare const ShareMultipleFilled: FluentIcon;
export declare const ShareMultipleRegular: FluentIcon;
export declare const ShareScreenPersonFilled: FluentIcon;
export declare const ShareScreenPersonRegular: FluentIcon;
export declare const ShareScreenPersonOverlayFilled: FluentIcon;
export declare const ShareScreenPersonOverlayRegular: FluentIcon;
export declare const ShareScreenPersonOverlayInsideFilled: FluentIcon;
export declare const ShareScreenPersonOverlayInsideRegular: FluentIcon;
export declare const ShareScreenPersonPFilled: FluentIcon;
export declare const ShareScreenPersonPRegular: FluentIcon;
export declare const ShareScreenStartFilled: FluentIcon;
export declare const ShareScreenStartRegular: FluentIcon;
export declare const ShareScreenStopFilled: FluentIcon;
export declare const ShareScreenStopRegular: FluentIcon;
export declare const ShieldColor: FluentIcon;
export declare const ShieldFilled: FluentIcon;
export declare const ShieldRegular: FluentIcon;
export declare const ShieldAddFilled: FluentIcon;
export declare const ShieldAddRegular: FluentIcon;
export declare const ShieldArrowRightFilled: FluentIcon;
export declare const ShieldArrowRightRegular: FluentIcon;
export declare const ShieldBadgeFilled: FluentIcon;
export declare const ShieldBadgeRegular: FluentIcon;
export declare const ShieldCheckmarkColor: FluentIcon;
export declare const ShieldCheckmarkFilled: FluentIcon;
export declare const ShieldCheckmarkRegular: FluentIcon;
export declare const ShieldDismissFilled: FluentIcon;
export declare const ShieldDismissRegular: FluentIcon;
export declare const ShieldDismissShieldFilled: FluentIcon;
export declare const ShieldDismissShieldRegular: FluentIcon;
export declare const ShieldErrorFilled: FluentIcon;
export declare const ShieldErrorRegular: FluentIcon;
export declare const ShieldGlobeFilled: FluentIcon;
export declare const ShieldGlobeRegular: FluentIcon;
export declare const ShieldKeyholeFilled: FluentIcon;
export declare const ShieldKeyholeRegular: FluentIcon;
export declare const ShieldLockFilled: FluentIcon;
export declare const ShieldLockRegular: FluentIcon;
export declare const ShieldPersonFilled: FluentIcon;
export declare const ShieldPersonRegular: FluentIcon;
export declare const ShieldPersonAddFilled: FluentIcon;
export declare const ShieldPersonAddRegular: FluentIcon;
export declare const ShieldProhibitedFilled: FluentIcon;
export declare const ShieldProhibitedRegular: FluentIcon;
export declare const ShieldQuestionFilled: FluentIcon;
export declare const ShieldQuestionRegular: FluentIcon;
export declare const ShieldSettingsFilled: FluentIcon;
export declare const ShieldSettingsRegular: FluentIcon;
export declare const ShieldTaskFilled: FluentIcon;
export declare const ShieldTaskRegular: FluentIcon;
export declare const ShiftsColor: FluentIcon;
export declare const ShiftsFilled: FluentIcon;
export declare const ShiftsRegular: FluentIcon;
export declare const Shifts30MinutesFilled: FluentIcon;
export declare const Shifts30MinutesRegular: FluentIcon;
export declare const ShiftsActivityFilled: FluentIcon;
export declare const ShiftsActivityRegular: FluentIcon;
export declare const ShiftsAddFilled: FluentIcon;
export declare const ShiftsAddRegular: FluentIcon;
export declare const ShiftsAvailabilityFilled: FluentIcon;
export declare const ShiftsAvailabilityRegular: FluentIcon;
export declare const ShiftsCheckmarkFilled: FluentIcon;
export declare const ShiftsCheckmarkRegular: FluentIcon;
export declare const ShiftsDayFilled: FluentIcon;
export declare const ShiftsDayRegular: FluentIcon;
export declare const ShiftsOpenFilled: FluentIcon;
export declare const ShiftsOpenRegular: FluentIcon;
export declare const ShiftsProhibitedFilled: FluentIcon;
export declare const ShiftsProhibitedRegular: FluentIcon;
export declare const ShiftsQuestionMarkFilled: FluentIcon;
export declare const ShiftsQuestionMarkRegular: FluentIcon;
export declare const ShiftsTeamFilled: FluentIcon;
export declare const ShiftsTeamRegular: FluentIcon;
export declare const ShoppingBagFilled: FluentIcon;
export declare const ShoppingBagRegular: FluentIcon;
export declare const ShoppingBagAddFilled: FluentIcon;
export declare const ShoppingBagAddRegular: FluentIcon;
export declare const ShoppingBagArrowLeftFilled: FluentIcon;
export declare const ShoppingBagArrowLeftRegular: FluentIcon;
export declare const ShoppingBagCheckmarkFilled: FluentIcon;
export declare const ShoppingBagCheckmarkRegular: FluentIcon;
export declare const ShoppingBagDismissFilled: FluentIcon;
export declare const ShoppingBagDismissRegular: FluentIcon;
export declare const ShoppingBagPauseFilled: FluentIcon;
export declare const ShoppingBagPauseRegular: FluentIcon;
export declare const ShoppingBagPercentFilled: FluentIcon;
export declare const ShoppingBagPercentRegular: FluentIcon;
export declare const ShoppingBagPlayFilled: FluentIcon;
export declare const ShoppingBagPlayRegular: FluentIcon;
export declare const ShoppingBagTagFilled: FluentIcon;
export declare const ShoppingBagTagRegular: FluentIcon;
export declare const ShortpickFilled: FluentIcon;
export declare const ShortpickRegular: FluentIcon;
export declare const ShowerheadFilled: FluentIcon;
export declare const ShowerheadRegular: FluentIcon;
export declare const SidebarSearchLtrFilled: FluentIcon;
export declare const SidebarSearchLtrRegular: FluentIcon;
export declare const SidebarSearchRtlFilled: FluentIcon;
export declare const SidebarSearchRtlRegular: FluentIcon;
export declare const SignOutFilled: FluentIcon;
export declare const SignOutRegular: FluentIcon;
export declare const SignatureFilled: FluentIcon;
export declare const SignatureRegular: FluentIcon;
export declare const SimFilled: FluentIcon;
export declare const SimRegular: FluentIcon;
export declare const SkipBack10Filled: FluentIcon;
export declare const SkipBack10Regular: FluentIcon;
export declare const SkipBack15Filled: FluentIcon;
export declare const SkipBack15Regular: FluentIcon;
export declare const SkipForward10Filled: FluentIcon;
export declare const SkipForward10Regular: FluentIcon;
export declare const SkipForward15Filled: FluentIcon;
export declare const SkipForward15Regular: FluentIcon;
export declare const SkipForward30Filled: FluentIcon;
export declare const SkipForward30Regular: FluentIcon;
export declare const SkipForwardTabFilled: FluentIcon;
export declare const SkipForwardTabRegular: FluentIcon;
export declare const SlashForwardFilled: FluentIcon;
export declare const SlashForwardRegular: FluentIcon;
export declare const SleepFilled: FluentIcon;
export declare const SleepRegular: FluentIcon;
export declare const SlideAddFilled: FluentIcon;
export declare const SlideAddRegular: FluentIcon;
export declare const SlideArrowRightFilled: FluentIcon;
export declare const SlideArrowRightRegular: FluentIcon;
export declare const SlideEraserFilled: FluentIcon;
export declare const SlideEraserRegular: FluentIcon;
export declare const SlideGridFilled: FluentIcon;
export declare const SlideGridRegular: FluentIcon;
export declare const SlideHideFilled: FluentIcon;
export declare const SlideHideRegular: FluentIcon;
export declare const SlideLayoutFilled: FluentIcon;
export declare const SlideLayoutRegular: FluentIcon;
export declare const SlideLinkFilled: FluentIcon;
export declare const SlideLinkRegular: FluentIcon;
export declare const SlideMicrophoneFilled: FluentIcon;
export declare const SlideMicrophoneRegular: FluentIcon;
export declare const SlideMultipleFilled: FluentIcon;
export declare const SlideMultipleRegular: FluentIcon;
export declare const SlideMultipleArrowRightFilled: FluentIcon;
export declare const SlideMultipleArrowRightRegular: FluentIcon;
export declare const SlideMultipleSearchFilled: FluentIcon;
export declare const SlideMultipleSearchRegular: FluentIcon;
export declare const SlidePlayFilled: FluentIcon;
export declare const SlidePlayRegular: FluentIcon;
export declare const SlideRecordFilled: FluentIcon;
export declare const SlideRecordRegular: FluentIcon;
export declare const SlideSearchFilled: FluentIcon;
export declare const SlideSearchRegular: FluentIcon;
export declare const SlideSettingsFilled: FluentIcon;
export declare const SlideSettingsRegular: FluentIcon;
export declare const SlideSizeFilled: FluentIcon;
export declare const SlideSizeRegular: FluentIcon;
export declare const SlideTextFilled: FluentIcon;
export declare const SlideTextRegular: FluentIcon;
export declare const SlideTextCallFilled: FluentIcon;
export declare const SlideTextCallRegular: FluentIcon;
export declare const SlideTextCursorFilled: FluentIcon;
export declare const SlideTextCursorRegular: FluentIcon;
export declare const SlideTextEditFilled: FluentIcon;
export declare const SlideTextEditRegular: FluentIcon;
export declare const SlideTextMultipleFilled: FluentIcon;
export declare const SlideTextMultipleRegular: FluentIcon;
export declare const SlideTextPersonFilled: FluentIcon;
export declare const SlideTextPersonRegular: FluentIcon;
export declare const SlideTextSparkleColor: FluentIcon;
export declare const SlideTextSparkleFilled: FluentIcon;
export declare const SlideTextSparkleRegular: FluentIcon;
export declare const SlideTextTitleFilled: FluentIcon;
export declare const SlideTextTitleRegular: FluentIcon;
export declare const SlideTextTitleAddFilled: FluentIcon;
export declare const SlideTextTitleAddRegular: FluentIcon;
export declare const SlideTextTitleCheckmarkFilled: FluentIcon;
export declare const SlideTextTitleCheckmarkRegular: FluentIcon;
export declare const SlideTextTitleEditFilled: FluentIcon;
export declare const SlideTextTitleEditRegular: FluentIcon;
export declare const SlideTopicAddFilled: FluentIcon;
export declare const SlideTopicAddRegular: FluentIcon;
export declare const SlideTransitionFilled: FluentIcon;
export declare const SlideTransitionRegular: FluentIcon;
export declare const SmartwatchFilled: FluentIcon;
export declare const SmartwatchRegular: FluentIcon;
export declare const SmartwatchDotFilled: FluentIcon;
export declare const SmartwatchDotRegular: FluentIcon;
export declare const SnoozeFilled: FluentIcon;
export declare const SnoozeRegular: FluentIcon;
export declare const SoundSourceFilled: FluentIcon;
export declare const SoundSourceRegular: FluentIcon;
export declare const SoundWaveCircleFilled: FluentIcon;
export declare const SoundWaveCircleRegular: FluentIcon;
export declare const SoundWaveCircleSparkleFilled: FluentIcon;
export declare const SoundWaveCircleSparkleRegular: FluentIcon;
export declare const Space3DFilled: FluentIcon;
export declare const Space3DRegular: FluentIcon;
export declare const SpacebarFilled: FluentIcon;
export declare const SpacebarRegular: FluentIcon;
export declare const SparkleFilled: FluentIcon;
export declare const SparkleRegular: FluentIcon;
export declare const SparkleActionFilled: FluentIcon;
export declare const SparkleActionRegular: FluentIcon;
export declare const SparkleCircleFilled: FluentIcon;
export declare const SparkleCircleRegular: FluentIcon;
export declare const SparkleInfoFilled: FluentIcon;
export declare const SparkleInfoRegular: FluentIcon;
export declare const SpatulaSpoonFilled: FluentIcon;
export declare const SpatulaSpoonRegular: FluentIcon;
export declare const Speaker0Filled: FluentIcon;
export declare const Speaker0Regular: FluentIcon;
export declare const Speaker1Filled: FluentIcon;
export declare const Speaker1Regular: FluentIcon;
export declare const Speaker2Filled: FluentIcon;
export declare const Speaker2Regular: FluentIcon;
export declare const SpeakerBluetoothFilled: FluentIcon;
export declare const SpeakerBluetoothRegular: FluentIcon;
export declare const SpeakerBoxFilled: FluentIcon;
export declare const SpeakerBoxRegular: FluentIcon;
export declare const SpeakerEditFilled: FluentIcon;
export declare const SpeakerEditRegular: FluentIcon;
export declare const SpeakerMuteFilled: FluentIcon;
export declare const SpeakerMuteRegular: FluentIcon;
export declare const SpeakerOffFilled: FluentIcon;
export declare const SpeakerOffRegular: FluentIcon;
export declare const SpeakerSettingsFilled: FluentIcon;
export declare const SpeakerSettingsRegular: FluentIcon;
export declare const SpeakerUsbFilled: FluentIcon;
export declare const SpeakerUsbRegular: FluentIcon;
export declare const SpinnerIosFilled: FluentIcon;
export declare const SpinnerIosRegular: FluentIcon;
export declare const SplitHintFilled: FluentIcon;
export declare const SplitHintRegular: FluentIcon;
export declare const SplitHorizontalFilled: FluentIcon;
export declare const SplitHorizontalRegular: FluentIcon;
export declare const SplitVerticalFilled: FluentIcon;
export declare const SplitVerticalRegular: FluentIcon;
export declare const SportColor: FluentIcon;
export declare const SportFilled: FluentIcon;
export declare const SportRegular: FluentIcon;
export declare const SportAmericanFootballFilled: FluentIcon;
export declare const SportAmericanFootballRegular: FluentIcon;
export declare const SportBaseballFilled: FluentIcon;
export declare const SportBaseballRegular: FluentIcon;
export declare const SportBasketballFilled: FluentIcon;
export declare const SportBasketballRegular: FluentIcon;
export declare const SportHockeyFilled: FluentIcon;
export declare const SportHockeyRegular: FluentIcon;
export declare const SportSoccerFilled: FluentIcon;
export declare const SportSoccerRegular: FluentIcon;
export declare const SquareFilled: FluentIcon;
export declare const SquareRegular: FluentIcon;
export declare const SquareAddFilled: FluentIcon;
export declare const SquareAddRegular: FluentIcon;
export declare const SquareArrowForwardFilled: FluentIcon;
export declare const SquareArrowForwardRegular: FluentIcon;
export declare const SquareDismissFilled: FluentIcon;
export declare const SquareDismissRegular: FluentIcon;
export declare const SquareDovetailJointFilled: FluentIcon;
export declare const SquareDovetailJointRegular: FluentIcon;
export declare const SquareEraserFilled: FluentIcon;
export declare const SquareEraserRegular: FluentIcon;
export declare const SquareHintFilled: FluentIcon;
export declare const SquareHintRegular: FluentIcon;
export declare const SquareHintAppsFilled: FluentIcon;
export declare const SquareHintAppsRegular: FluentIcon;
export declare const SquareHintArrowBackFilled: FluentIcon;
export declare const SquareHintArrowBackRegular: FluentIcon;
export declare const SquareHintHexagonFilled: FluentIcon;
export declare const SquareHintHexagonRegular: FluentIcon;
export declare const SquareHintSparklesFilled: FluentIcon;
export declare const SquareHintSparklesRegular: FluentIcon;
export declare const SquareMultipleFilled: FluentIcon;
export declare const SquareMultipleRegular: FluentIcon;
export declare const SquareShadowFilled: FluentIcon;
export declare const SquareShadowRegular: FluentIcon;
export declare const SquareTextArrowRepeatAllFilled: FluentIcon;
export declare const SquareTextArrowRepeatAllRegular: FluentIcon;
export declare const SquaresNestedFilled: FluentIcon;
export declare const SquaresNestedRegular: FluentIcon;
export declare const StackFilled: FluentIcon;
export declare const StackRegular: FluentIcon;
export declare const StackAddFilled: FluentIcon;
export declare const StackAddRegular: FluentIcon;
export declare const StackArrowForwardFilled: FluentIcon;
export declare const StackArrowForwardRegular: FluentIcon;
export declare const StackOffFilled: FluentIcon;
export declare const StackOffRegular: FluentIcon;
export declare const StackStarFilled: FluentIcon;
export declare const StackStarRegular: FluentIcon;
export declare const StackVerticalFilled: FluentIcon;
export declare const StackVerticalRegular: FluentIcon;
export declare const StarColor: FluentIcon;
export declare const StarFilled: FluentIcon;
export declare const StarRegular: FluentIcon;
export declare const StarAddFilled: FluentIcon;
export declare const StarAddRegular: FluentIcon;
export declare const StarArrowBackFilled: FluentIcon;
export declare const StarArrowBackRegular: FluentIcon;
export declare const StarArrowRightEndFilled: FluentIcon;
export declare const StarArrowRightEndRegular: FluentIcon;
export declare const StarArrowRightStartFilled: FluentIcon;
export declare const StarArrowRightStartRegular: FluentIcon;
export declare const StarCheckmarkFilled: FluentIcon;
export declare const StarCheckmarkRegular: FluentIcon;
export declare const StarDismissFilled: FluentIcon;
export declare const StarDismissRegular: FluentIcon;
export declare const StarEditFilled: FluentIcon;
export declare const StarEditRegular: FluentIcon;
export declare const StarEmphasisFilled: FluentIcon;
export declare const StarEmphasisRegular: FluentIcon;
export declare const StarHalfFilled: FluentIcon;
export declare const StarHalfRegular: FluentIcon;
export declare const StarLineHorizontal3Filled: FluentIcon;
export declare const StarLineHorizontal3Regular: FluentIcon;
export declare const StarOffFilled: FluentIcon;
export declare const StarOffRegular: FluentIcon;
export declare const StarOneQuarterFilled: FluentIcon;
export declare const StarOneQuarterRegular: FluentIcon;
export declare const StarProhibitedFilled: FluentIcon;
export declare const StarProhibitedRegular: FluentIcon;
export declare const StarSettingsColor: FluentIcon;
export declare const StarSettingsFilled: FluentIcon;
export declare const StarSettingsRegular: FluentIcon;
export declare const StarThreeQuarterFilled: FluentIcon;
export declare const StarThreeQuarterRegular: FluentIcon;
export declare const StatusFilled: FluentIcon;
export declare const StatusRegular: FluentIcon;
export declare const StepFilled: FluentIcon;
export declare const StepRegular: FluentIcon;
export declare const StepsFilled: FluentIcon;
export declare const StepsRegular: FluentIcon;
export declare const StethoscopeFilled: FluentIcon;
export declare const StethoscopeRegular: FluentIcon;
export declare const StickerFilled: FluentIcon;
export declare const StickerRegular: FluentIcon;
export declare const StickerAddFilled: FluentIcon;
export declare const StickerAddRegular: FluentIcon;
export declare const StopFilled: FluentIcon;
export declare const StopRegular: FluentIcon;
export declare const StorageFilled: FluentIcon;
export declare const StorageRegular: FluentIcon;
export declare const StoreMicrosoftFilled: FluentIcon;
export declare const StoreMicrosoftRegular: FluentIcon;
export declare const StreamFilled: FluentIcon;
export declare const StreamRegular: FluentIcon;
export declare const StreamInputFilled: FluentIcon;
export declare const StreamInputRegular: FluentIcon;
export declare const StreamInputOutputFilled: FluentIcon;
export declare const StreamInputOutputRegular: FluentIcon;
export declare const StreamOutputFilled: FluentIcon;
export declare const StreamOutputRegular: FluentIcon;
export declare const StreetSignFilled: FluentIcon;
export declare const StreetSignRegular: FluentIcon;
export declare const StyleGuideFilled: FluentIcon;
export declare const StyleGuideRegular: FluentIcon;
export declare const SubGridFilled: FluentIcon;
export declare const SubGridRegular: FluentIcon;
export declare const SubtitlesFilled: FluentIcon;
export declare const SubtitlesRegular: FluentIcon;
export declare const SubtractFilled: FluentIcon;
export declare const SubtractRegular: FluentIcon;
export declare const SubtractCircleFilled: FluentIcon;
export declare const SubtractCircleRegular: FluentIcon;
export declare const SubtractCircleArrowBackFilled: FluentIcon;
export declare const SubtractCircleArrowBackRegular: FluentIcon;
export declare const SubtractCircleArrowForwardFilled: FluentIcon;
export declare const SubtractCircleArrowForwardRegular: FluentIcon;
export declare const SubtractParenthesesFilled: FluentIcon;
export declare const SubtractParenthesesRegular: FluentIcon;
export declare const SubtractSquareFilled: FluentIcon;
export declare const SubtractSquareRegular: FluentIcon;
export declare const SubtractSquareMultipleFilled: FluentIcon;
export declare const SubtractSquareMultipleRegular: FluentIcon;
export declare const SurfaceEarbudsFilled: FluentIcon;
export declare const SurfaceEarbudsRegular: FluentIcon;
export declare const SurfaceHubFilled: FluentIcon;
export declare const SurfaceHubRegular: FluentIcon;
export declare const SwimmingPoolFilled: FluentIcon;
export declare const SwimmingPoolRegular: FluentIcon;
export declare const SwipeDownFilled: FluentIcon;
export declare const SwipeDownRegular: FluentIcon;
export declare const SwipeRightFilled: FluentIcon;
export declare const SwipeRightRegular: FluentIcon;
export declare const SwipeUpFilled: FluentIcon;
export declare const SwipeUpRegular: FluentIcon;
export declare const SymbolsFilled: FluentIcon;
export declare const SymbolsRegular: FluentIcon;
export declare const SyncOffFilled: FluentIcon;
export declare const SyncOffRegular: FluentIcon;
export declare const SyringeFilled: FluentIcon;
export declare const SyringeRegular: FluentIcon;
export declare const SystemFilled: FluentIcon;
export declare const SystemRegular: FluentIcon;
export declare const TabFilled: FluentIcon;
export declare const TabRegular: FluentIcon;
export declare const TabAddFilled: FluentIcon;
export declare const TabAddRegular: FluentIcon;
export declare const TabArrowLeftFilled: FluentIcon;
export declare const TabArrowLeftRegular: FluentIcon;
export declare const TabDesktopFilled: FluentIcon;
export declare const TabDesktopRegular: FluentIcon;
export declare const TabDesktopArrowClockwiseFilled: FluentIcon;
export declare const TabDesktopArrowClockwiseRegular: FluentIcon;
export declare const TabDesktopArrowLeftFilled: FluentIcon;
export declare const TabDesktopArrowLeftRegular: FluentIcon;
export declare const TabDesktopBottomFilled: FluentIcon;
export declare const TabDesktopBottomRegular: FluentIcon;
export declare const TabDesktopClockFilled: FluentIcon;
export declare const TabDesktopClockRegular: FluentIcon;
export declare const TabDesktopCopyFilled: FluentIcon;
export declare const TabDesktopCopyRegular: FluentIcon;
export declare const TabDesktopImageFilled: FluentIcon;
export declare const TabDesktopImageRegular: FluentIcon;
export declare const TabDesktopLinkFilled: FluentIcon;
export declare const TabDesktopLinkRegular: FluentIcon;
export declare const TabDesktopMultipleFilled: FluentIcon;
export declare const TabDesktopMultipleRegular: FluentIcon;
export declare const TabDesktopMultipleAddFilled: FluentIcon;
export declare const TabDesktopMultipleAddRegular: FluentIcon;
export declare const TabDesktopMultipleBottomFilled: FluentIcon;
export declare const TabDesktopMultipleBottomRegular: FluentIcon;
export declare const TabDesktopMultipleSparkleFilled: FluentIcon;
export declare const TabDesktopMultipleSparkleRegular: FluentIcon;
export declare const TabDesktopNewPageFilled: FluentIcon;
export declare const TabDesktopNewPageRegular: FluentIcon;
export declare const TabDesktopSearchFilled: FluentIcon;
export declare const TabDesktopSearchRegular: FluentIcon;
export declare const TabGroupFilled: FluentIcon;
export declare const TabGroupRegular: FluentIcon;
export declare const TabInPrivateFilled: FluentIcon;
export declare const TabInPrivateRegular: FluentIcon;
export declare const TabInprivateAccountFilled: FluentIcon;
export declare const TabInprivateAccountRegular: FluentIcon;
export declare const TabProhibitedFilled: FluentIcon;
export declare const TabProhibitedRegular: FluentIcon;
export declare const TabShieldDismissFilled: FluentIcon;
export declare const TabShieldDismissRegular: FluentIcon;
export declare const TableColor: FluentIcon;
export declare const TableFilled: FluentIcon;
export declare const TableRegular: FluentIcon;
export declare const TableAddFilled: FluentIcon;
export declare const TableAddRegular: FluentIcon;
export declare const TableAltTextFilled: FluentIcon;
export declare const TableAltTextRegular: FluentIcon;
export declare const TableArrowRepeatAllFilled: FluentIcon;
export declare const TableArrowRepeatAllRegular: FluentIcon;
export declare const TableArrowUpFilled: FluentIcon;
export declare const TableArrowUpRegular: FluentIcon;
export declare const TableBottomRowFilled: FluentIcon;
export declare const TableBottomRowRegular: FluentIcon;
export declare const TableCalculatorFilled: FluentIcon;
export declare const TableCalculatorRegular: FluentIcon;
export declare const TableCellAddFilled: FluentIcon;
export declare const TableCellAddRegular: FluentIcon;
export declare const TableCellCenterFilled: FluentIcon;
export declare const TableCellCenterRegular: FluentIcon;
export declare const TableCellCenterArrowRepeatAllFilled: FluentIcon;
export declare const TableCellCenterArrowRepeatAllRegular: FluentIcon;
export declare const TableCellCenterEditFilled: FluentIcon;
export declare const TableCellCenterEditRegular: FluentIcon;
export declare const TableCellCenterLinkFilled: FluentIcon;
export declare const TableCellCenterLinkRegular: FluentIcon;
export declare const TableCellCenterSearchFilled: FluentIcon;
export declare const TableCellCenterSearchRegular: FluentIcon;
export declare const TableCellEditFilled: FluentIcon;
export declare const TableCellEditRegular: FluentIcon;
export declare const TableCellsMergeFilled: FluentIcon;
export declare const TableCellsMergeRegular: FluentIcon;
export declare const TableCellsSplitFilled: FluentIcon;
export declare const TableCellsSplitRegular: FluentIcon;
export declare const TableCheckerFilled: FluentIcon;
export declare const TableCheckerRegular: FluentIcon;
export declare const TableColumnTopBottomFilled: FluentIcon;
export declare const TableColumnTopBottomRegular: FluentIcon;
export declare const TableColumnTopBottomArrowRepeatAllFilled: FluentIcon;
export declare const TableColumnTopBottomArrowRepeatAllRegular: FluentIcon;
export declare const TableColumnTopBottomEditFilled: FluentIcon;
export declare const TableColumnTopBottomEditRegular: FluentIcon;
export declare const TableColumnTopBottomLinkFilled: FluentIcon;
export declare const TableColumnTopBottomLinkRegular: FluentIcon;
export declare const TableColumnTopBottomSearchFilled: FluentIcon;
export declare const TableColumnTopBottomSearchRegular: FluentIcon;
export declare const TableCopyFilled: FluentIcon;
export declare const TableCopyRegular: FluentIcon;
export declare const TableCursorFilled: FluentIcon;
export declare const TableCursorRegular: FluentIcon;
export declare const TableDeleteColumnFilled: FluentIcon;
export declare const TableDeleteColumnRegular: FluentIcon;
export declare const TableDeleteRowFilled: FluentIcon;
export declare const TableDeleteRowRegular: FluentIcon;
export declare const TableDismissFilled: FluentIcon;
export declare const TableDismissRegular: FluentIcon;
export declare const TableEditFilled: FluentIcon;
export declare const TableEditRegular: FluentIcon;
export declare const TableFreezeColumnFilled: FluentIcon;
export declare const TableFreezeColumnRegular: FluentIcon;
export declare const TableFreezeColumnAndRowFilled: FluentIcon;
export declare const TableFreezeColumnAndRowRegular: FluentIcon;
export declare const TableFreezeColumnAndRowDismissFilled: FluentIcon;
export declare const TableFreezeColumnAndRowDismissRegular: FluentIcon;
export declare const TableFreezeColumnDismissFilled: FluentIcon;
export declare const TableFreezeColumnDismissRegular: FluentIcon;
export declare const TableFreezeRowFilled: FluentIcon;
export declare const TableFreezeRowRegular: FluentIcon;
export declare const TableFreezeRowDismissFilled: FluentIcon;
export declare const TableFreezeRowDismissRegular: FluentIcon;
export declare const TableImageFilled: FluentIcon;
export declare const TableImageRegular: FluentIcon;
export declare const TableInsertColumnFilled: FluentIcon;
export declare const TableInsertColumnRegular: FluentIcon;
export declare const TableInsertRowFilled: FluentIcon;
export declare const TableInsertRowRegular: FluentIcon;
export declare const TableLightningFilled: FluentIcon;
export declare const TableLightningRegular: FluentIcon;
export declare const TableLinkFilled: FluentIcon;
export declare const TableLinkRegular: FluentIcon;
export declare const TableLockFilled: FluentIcon;
export declare const TableLockRegular: FluentIcon;
export declare const TableMoveAboveFilled: FluentIcon;
export declare const TableMoveAboveRegular: FluentIcon;
export declare const TableMoveBelowFilled: FluentIcon;
export declare const TableMoveBelowRegular: FluentIcon;
export declare const TableMoveLeftFilled: FluentIcon;
export declare const TableMoveLeftRegular: FluentIcon;
export declare const TableMoveRightFilled: FluentIcon;
export declare const TableMoveRightRegular: FluentIcon;
export declare const TableMultipleFilled: FluentIcon;
export declare const TableMultipleRegular: FluentIcon;
export declare const TableOffsetFilled: FluentIcon;
export declare const TableOffsetRegular: FluentIcon;
export declare const TableOffsetAddFilled: FluentIcon;
export declare const TableOffsetAddRegular: FluentIcon;
export declare const TableOffsetLessThanOrEqualToFilled: FluentIcon;
export declare const TableOffsetLessThanOrEqualToRegular: FluentIcon;
export declare const TableOffsetSettingsFilled: FluentIcon;
export declare const TableOffsetSettingsRegular: FluentIcon;
export declare const TableResizeColumnFilled: FluentIcon;
export declare const TableResizeColumnRegular: FluentIcon;
export declare const TableResizeRowFilled: FluentIcon;
export declare const TableResizeRowRegular: FluentIcon;
export declare const TableSearchFilled: FluentIcon;
export declare const TableSearchRegular: FluentIcon;
export declare const TableSettingsFilled: FluentIcon;
export declare const TableSettingsRegular: FluentIcon;
export declare const TableSimpleFilled: FluentIcon;
export declare const TableSimpleRegular: FluentIcon;
export declare const TableSimpleCheckmarkFilled: FluentIcon;
export declare const TableSimpleCheckmarkRegular: FluentIcon;
export declare const TableSimpleExcludeFilled: FluentIcon;
export declare const TableSimpleExcludeRegular: FluentIcon;
export declare const TableSimpleIncludeFilled: FluentIcon;
export declare const TableSimpleIncludeRegular: FluentIcon;
export declare const TableSimpleMultipleFilled: FluentIcon;
export declare const TableSimpleMultipleRegular: FluentIcon;
export declare const TableSparkleFilled: FluentIcon;
export declare const TableSparkleRegular: FluentIcon;
export declare const TableSplitFilled: FluentIcon;
export declare const TableSplitRegular: FluentIcon;
export declare const TableStackAboveFilled: FluentIcon;
export declare const TableStackAboveRegular: FluentIcon;
export declare const TableStackBelowFilled: FluentIcon;
export declare const TableStackBelowRegular: FluentIcon;
export declare const TableStackLeftFilled: FluentIcon;
export declare const TableStackLeftRegular: FluentIcon;
export declare const TableStackRightFilled: FluentIcon;
export declare const TableStackRightRegular: FluentIcon;
export declare const TableSwitchFilled: FluentIcon;
export declare const TableSwitchRegular: FluentIcon;
export declare const TabletFilled: FluentIcon;
export declare const TabletRegular: FluentIcon;
export declare const TabletLaptopFilled: FluentIcon;
export declare const TabletLaptopRegular: FluentIcon;
export declare const TabletSpeakerFilled: FluentIcon;
export declare const TabletSpeakerRegular: FluentIcon;
export declare const TabsFilled: FluentIcon;
export declare const TabsRegular: FluentIcon;
export declare const TagFilled: FluentIcon;
export declare const TagRegular: FluentIcon;
export declare const TagAddFilled: FluentIcon;
export declare const TagAddRegular: FluentIcon;
export declare const TagCircleFilled: FluentIcon;
export declare const TagCircleRegular: FluentIcon;
export declare const TagDismissFilled: FluentIcon;
export declare const TagDismissRegular: FluentIcon;
export declare const TagEditFilled: FluentIcon;
export declare const TagEditRegular: FluentIcon;
export declare const TagErrorFilled: FluentIcon;
export declare const TagErrorRegular: FluentIcon;
export declare const TagLockFilled: FluentIcon;
export declare const TagLockRegular: FluentIcon;
export declare const TagLockAccentFilled: FluentIcon;
export declare const TagMultipleFilled: FluentIcon;
export declare const TagMultipleRegular: FluentIcon;
export declare const TagOffFilled: FluentIcon;
export declare const TagOffRegular: FluentIcon;
export declare const TagPercentFilled: FluentIcon;
export declare const TagPercentRegular: FluentIcon;
export declare const TagQuestionMarkFilled: FluentIcon;
export declare const TagQuestionMarkRegular: FluentIcon;
export declare const TagResetFilled: FluentIcon;
export declare const TagResetRegular: FluentIcon;
export declare const TagSearchFilled: FluentIcon;
export declare const TagSearchRegular: FluentIcon;
export declare const TapDoubleFilled: FluentIcon;
export declare const TapDoubleRegular: FluentIcon;
export declare const TapSingleFilled: FluentIcon;
export declare const TapSingleRegular: FluentIcon;
export declare const TargetFilled: FluentIcon;
export declare const TargetRegular: FluentIcon;
export declare const TargetAddFilled: FluentIcon;
export declare const TargetAddRegular: FluentIcon;
export declare const TargetArrowFilled: FluentIcon;
export declare const TargetArrowRegular: FluentIcon;
export declare const TargetDismissFilled: FluentIcon;
export declare const TargetDismissRegular: FluentIcon;
export declare const TargetEditFilled: FluentIcon;
export declare const TargetEditRegular: FluentIcon;
export declare const TargetSparkleFilled: FluentIcon;
export declare const TargetSparkleRegular: FluentIcon;
export declare const TaskListAddFilled: FluentIcon;
export declare const TaskListAddRegular: FluentIcon;
export declare const TaskListLtrFilled: FluentIcon;
export declare const TaskListLtrRegular: FluentIcon;
export declare const TaskListRtlFilled: FluentIcon;
export declare const TaskListRtlRegular: FluentIcon;
export declare const TaskListSquareAddFilled: FluentIcon;
export declare const TaskListSquareAddRegular: FluentIcon;
export declare const TaskListSquareDatabaseFilled: FluentIcon;
export declare const TaskListSquareDatabaseRegular: FluentIcon;
export declare const TaskListSquareLtrFilled: FluentIcon;
export declare const TaskListSquareLtrRegular: FluentIcon;
export declare const TaskListSquarePersonFilled: FluentIcon;
export declare const TaskListSquarePersonRegular: FluentIcon;
export declare const TaskListSquareRtlFilled: FluentIcon;
export declare const TaskListSquareRtlRegular: FluentIcon;
export declare const TaskListSquareSettingsFilled: FluentIcon;
export declare const TaskListSquareSettingsRegular: FluentIcon;
export declare const TaskListSquareSparkleFilled: FluentIcon;
export declare const TaskListSquareSparkleRegular: FluentIcon;
export declare const TasksAppFilled: FluentIcon;
export declare const TasksAppRegular: FluentIcon;
export declare const TeachingFilled: FluentIcon;
export declare const TeachingRegular: FluentIcon;
export declare const TeardropBottomRightFilled: FluentIcon;
export declare const TeardropBottomRightRegular: FluentIcon;
export declare const TeddyFilled: FluentIcon;
export declare const TeddyRegular: FluentIcon;
export declare const TemperatureFilled: FluentIcon;
export declare const TemperatureRegular: FluentIcon;
export declare const TemperatureDegreeCelsiusFilled: FluentIcon;
export declare const TemperatureDegreeCelsiusRegular: FluentIcon;
export declare const TemperatureDegreeFahrenheitFilled: FluentIcon;
export declare const TemperatureDegreeFahrenheitRegular: FluentIcon;
export declare const TentFilled: FluentIcon;
export declare const TentRegular: FluentIcon;
export declare const TetrisAppFilled: FluentIcon;
export declare const TetrisAppRegular: FluentIcon;
export declare const TextAddFilled: FluentIcon;
export declare const TextAddRegular: FluentIcon;
export declare const TextAddSpaceAfterFilled: FluentIcon;
export declare const TextAddSpaceAfterRegular: FluentIcon;
export declare const TextAddSpaceBeforeFilled: FluentIcon;
export declare const TextAddSpaceBeforeRegular: FluentIcon;
export declare const TextAddTFilled: FluentIcon;
export declare const TextAddTRegular: FluentIcon;
export declare const TextAlignCenterFilled: FluentIcon;
export declare const TextAlignCenterRegular: FluentIcon;
export declare const TextAlignCenterRotate270Filled: FluentIcon;
export declare const TextAlignCenterRotate270Regular: FluentIcon;
export declare const TextAlignCenterRotate90Filled: FluentIcon;
export declare const TextAlignCenterRotate90Regular: FluentIcon;
export declare const TextAlignDistributedFilled: FluentIcon;
export declare const TextAlignDistributedRegular: FluentIcon;
export declare const TextAlignDistributedEvenlyFilled: FluentIcon;
export declare const TextAlignDistributedEvenlyRegular: FluentIcon;
export declare const TextAlignDistributedVerticalFilled: FluentIcon;
export declare const TextAlignDistributedVerticalRegular: FluentIcon;
export declare const TextAlignJustifyFilled: FluentIcon;
export declare const TextAlignJustifyRegular: FluentIcon;
export declare const TextAlignJustifyLowFilled: FluentIcon;
export declare const TextAlignJustifyLowRegular: FluentIcon;
export declare const TextAlignJustifyLow90Filled: FluentIcon;
export declare const TextAlignJustifyLow90Regular: FluentIcon;
export declare const TextAlignJustifyLowRotate270Filled: FluentIcon;
export declare const TextAlignJustifyLowRotate270Regular: FluentIcon;
export declare const TextAlignJustifyLowRotate90Filled: FluentIcon;
export declare const TextAlignJustifyLowRotate90Regular: FluentIcon;
export declare const TextAlignJustifyRotate270Filled: FluentIcon;
export declare const TextAlignJustifyRotate270Regular: FluentIcon;
export declare const TextAlignJustifyRotate90Filled: FluentIcon;
export declare const TextAlignJustifyRotate90Regular: FluentIcon;
export declare const TextAlignLeftFilled: FluentIcon;
export declare const TextAlignLeftRegular: FluentIcon;
export declare const TextAlignLeftRotate270Filled: FluentIcon;
export declare const TextAlignLeftRotate270Regular: FluentIcon;
export declare const TextAlignLeftRotate90Filled: FluentIcon;
export declare const TextAlignLeftRotate90Regular: FluentIcon;
export declare const TextAlignRightFilled: FluentIcon;
export declare const TextAlignRightRegular: FluentIcon;
export declare const TextAlignRightRotate270Filled: FluentIcon;
export declare const TextAlignRightRotate270Regular: FluentIcon;
export declare const TextAlignRightRotate90Filled: FluentIcon;
export declare const TextAlignRightRotate90Regular: FluentIcon;
export declare const TextArrowDownRightColumnFilled: FluentIcon;
export declare const TextArrowDownRightColumnRegular: FluentIcon;
export declare const TextAsteriskFilled: FluentIcon;
export declare const TextAsteriskRegular: FluentIcon;
export declare const TextBaselineFilled: FluentIcon;
export declare const TextBaselineRegular: FluentIcon;
export declare const TextBoldFilled: FluentIcon;
export declare const TextBoldRegular: FluentIcon;
export declare const TextBoxSettingsFilled: FluentIcon;
export declare const TextBoxSettingsRegular: FluentIcon;
export declare const TextBulletListFilled: FluentIcon;
export declare const TextBulletListRegular: FluentIcon;
export declare const TextBulletList90Filled: FluentIcon;
export declare const TextBulletList90Regular: FluentIcon;
export declare const TextBulletListAddFilled: FluentIcon;
export declare const TextBulletListAddRegular: FluentIcon;
export declare const TextBulletListCheckmarkFilled: FluentIcon;
export declare const TextBulletListCheckmarkRegular: FluentIcon;
export declare const TextBulletListDismissFilled: FluentIcon;
export declare const TextBulletListDismissRegular: FluentIcon;
export declare const TextBulletListLtrFilled: FluentIcon;
export declare const TextBulletListLtrRegular: FluentIcon;
export declare const TextBulletListLtr90Filled: FluentIcon;
export declare const TextBulletListLtr90Regular: FluentIcon;
export declare const TextBulletListRtlFilled: FluentIcon;
export declare const TextBulletListRtlRegular: FluentIcon;
export declare const TextBulletListRtl90Filled: FluentIcon;
export declare const TextBulletListRtl90Regular: FluentIcon;
export declare const TextBulletListSquareColor: FluentIcon;
export declare const TextBulletListSquareFilled: FluentIcon;
export declare const TextBulletListSquareRegular: FluentIcon;
export declare const TextBulletListSquareClockFilled: FluentIcon;
export declare const TextBulletListSquareClockRegular: FluentIcon;
export declare const TextBulletListSquareEditFilled: FluentIcon;
export declare const TextBulletListSquareEditRegular: FluentIcon;
export declare const TextBulletListSquarePersonFilled: FluentIcon;
export declare const TextBulletListSquarePersonRegular: FluentIcon;
export declare const TextBulletListSquareSearchFilled: FluentIcon;
export declare const TextBulletListSquareSearchRegular: FluentIcon;
export declare const TextBulletListSquareSettingsFilled: FluentIcon;
export declare const TextBulletListSquareSettingsRegular: FluentIcon;
export declare const TextBulletListSquareShieldFilled: FluentIcon;
export declare const TextBulletListSquareShieldRegular: FluentIcon;
export declare const TextBulletListSquareSparkleColor: FluentIcon;
export declare const TextBulletListSquareSparkleFilled: FluentIcon;
export declare const TextBulletListSquareSparkleRegular: FluentIcon;
export declare const TextBulletListSquareToolboxFilled: FluentIcon;
export declare const TextBulletListSquareToolboxRegular: FluentIcon;
export declare const TextBulletListSquareWarningFilled: FluentIcon;
export declare const TextBulletListSquareWarningRegular: FluentIcon;
export declare const TextBulletListTreeFilled: FluentIcon;
export declare const TextBulletListTreeRegular: FluentIcon;
export declare const TextCaseLowercaseFilled: FluentIcon;
export declare const TextCaseLowercaseRegular: FluentIcon;
export declare const TextCaseTitleFilled: FluentIcon;
export declare const TextCaseTitleRegular: FluentIcon;
export declare const TextCaseUppercaseFilled: FluentIcon;
export declare const TextCaseUppercaseRegular: FluentIcon;
export declare const TextChangeCaseFilled: FluentIcon;
export declare const TextChangeCaseRegular: FluentIcon;
export declare const TextClearFormattingFilled: FluentIcon;
export declare const TextClearFormattingRegular: FluentIcon;
export declare const TextCollapseFilled: FluentIcon;
export declare const TextCollapseRegular: FluentIcon;
export declare const TextColorFilled: FluentIcon;
export declare const TextColorRegular: FluentIcon;
export declare const TextColorAccentFilled: FluentIcon;
export declare const TextColumnOneFilled: FluentIcon;
export declare const TextColumnOneRegular: FluentIcon;
export declare const TextColumnOneNarrowFilled: FluentIcon;
export declare const TextColumnOneNarrowRegular: FluentIcon;
export declare const TextColumnOneSemiNarrowFilled: FluentIcon;
export declare const TextColumnOneSemiNarrowRegular: FluentIcon;
export declare const TextColumnOneWideFilled: FluentIcon;
export declare const TextColumnOneWideRegular: FluentIcon;
export declare const TextColumnOneWideLightningFilled: FluentIcon;
export declare const TextColumnOneWideLightningRegular: FluentIcon;
export declare const TextColumnThreeFilled: FluentIcon;
export declare const TextColumnThreeRegular: FluentIcon;
export declare const TextColumnTwoFilled: FluentIcon;
export declare const TextColumnTwoRegular: FluentIcon;
export declare const TextColumnTwoLeftFilled: FluentIcon;
export declare const TextColumnTwoLeftRegular: FluentIcon;
export declare const TextColumnTwoRightFilled: FluentIcon;
export declare const TextColumnTwoRightRegular: FluentIcon;
export declare const TextColumnWideFilled: FluentIcon;
export declare const TextColumnWideRegular: FluentIcon;
export declare const TextContinuousFilled: FluentIcon;
export declare const TextContinuousRegular: FluentIcon;
export declare const TextDensityFilled: FluentIcon;
export declare const TextDensityRegular: FluentIcon;
export declare const TextDescriptionFilled: FluentIcon;
export declare const TextDescriptionRegular: FluentIcon;
export declare const TextDescriptionLtrFilled: FluentIcon;
export declare const TextDescriptionLtrRegular: FluentIcon;
export declare const TextDescriptionRtlFilled: FluentIcon;
export declare const TextDescriptionRtlRegular: FluentIcon;
export declare const TextDirectionHorizontalLeftFilled: FluentIcon;
export declare const TextDirectionHorizontalLeftRegular: FluentIcon;
export declare const TextDirectionHorizontalLtrFilled: FluentIcon;
export declare const TextDirectionHorizontalLtrRegular: FluentIcon;
export declare const TextDirectionHorizontalRightFilled: FluentIcon;
export declare const TextDirectionHorizontalRightRegular: FluentIcon;
export declare const TextDirectionHorizontalRtlFilled: FluentIcon;
export declare const TextDirectionHorizontalRtlRegular: FluentIcon;
export declare const TextDirectionRotate270RightFilled: FluentIcon;
export declare const TextDirectionRotate270RightRegular: FluentIcon;
export declare const TextDirectionRotate315RightFilled: FluentIcon;
export declare const TextDirectionRotate315RightRegular: FluentIcon;
export declare const TextDirectionRotate45RightFilled: FluentIcon;
export declare const TextDirectionRotate45RightRegular: FluentIcon;
export declare const TextDirectionRotate90LeftFilled: FluentIcon;
export declare const TextDirectionRotate90LeftRegular: FluentIcon;
export declare const TextDirectionRotate90LtrFilled: FluentIcon;
export declare const TextDirectionRotate90LtrRegular: FluentIcon;
export declare const TextDirectionRotate90RightFilled: FluentIcon;
export declare const TextDirectionRotate90RightRegular: FluentIcon;
export declare const TextDirectionRotate90RtlFilled: FluentIcon;
export declare const TextDirectionRotate90RtlRegular: FluentIcon;
export declare const TextDirectionVerticalFilled: FluentIcon;
export declare const TextDirectionVerticalRegular: FluentIcon;
export declare const TextEditStyleColor: FluentIcon;
export declare const TextEditStyleFilled: FluentIcon;
export declare const TextEditStyleRegular: FluentIcon;
export declare const TextEffectsFilled: FluentIcon;
export declare const TextEffectsRegular: FluentIcon;
export declare const TextEffectsSparkleFilled: FluentIcon;
export declare const TextEffectsSparkleRegular: FluentIcon;
export declare const TextExpandFilled: FluentIcon;
export declare const TextExpandRegular: FluentIcon;
export declare const TextFieldFilled: FluentIcon;
export declare const TextFieldRegular: FluentIcon;
export declare const TextFirstLineFilled: FluentIcon;
export declare const TextFirstLineRegular: FluentIcon;
export declare const TextFontFilled: FluentIcon;
export declare const TextFontRegular: FluentIcon;
export declare const TextFontInfoFilled: FluentIcon;
export declare const TextFontInfoRegular: FluentIcon;
export declare const TextFontSizeFilled: FluentIcon;
export declare const TextFontSizeRegular: FluentIcon;
export declare const TextFootnoteFilled: FluentIcon;
export declare const TextFootnoteRegular: FluentIcon;
export declare const TextGrammarArrowLeftFilled: FluentIcon;
export declare const TextGrammarArrowLeftRegular: FluentIcon;
export declare const TextGrammarArrowRightFilled: FluentIcon;
export declare const TextGrammarArrowRightRegular: FluentIcon;
export declare const TextGrammarCheckmarkFilled: FluentIcon;
export declare const TextGrammarCheckmarkRegular: FluentIcon;
export declare const TextGrammarDismissFilled: FluentIcon;
export declare const TextGrammarDismissRegular: FluentIcon;
export declare const TextGrammarErrorFilled: FluentIcon;
export declare const TextGrammarErrorRegular: FluentIcon;
export declare const TextGrammarLightningFilled: FluentIcon;
export declare const TextGrammarLightningRegular: FluentIcon;
export declare const TextGrammarSettingsFilled: FluentIcon;
export declare const TextGrammarSettingsRegular: FluentIcon;
export declare const TextGrammarWandFilled: FluentIcon;
export declare const TextGrammarWandRegular: FluentIcon;
export declare const TextHangingFilled: FluentIcon;
export declare const TextHangingRegular: FluentIcon;
export declare const TextHeader1Filled: FluentIcon;
export declare const TextHeader1Regular: FluentIcon;
export declare const TextHeader1LinesFilled: FluentIcon;
export declare const TextHeader1LinesRegular: FluentIcon;
export declare const TextHeader1LinesCaretFilled: FluentIcon;
export declare const TextHeader1LinesCaretRegular: FluentIcon;
export declare const TextHeader2Filled: FluentIcon;
export declare const TextHeader2Regular: FluentIcon;
export declare const TextHeader2LinesFilled: FluentIcon;
export declare const TextHeader2LinesRegular: FluentIcon;
export declare const TextHeader2LinesCaretFilled: FluentIcon;
export declare const TextHeader2LinesCaretRegular: FluentIcon;
export declare const TextHeader3Filled: FluentIcon;
export declare const TextHeader3Regular: FluentIcon;
export declare const TextHeader3LinesFilled: FluentIcon;
export declare const TextHeader3LinesRegular: FluentIcon;
export declare const TextHeader3LinesCaretFilled: FluentIcon;
export declare const TextHeader3LinesCaretRegular: FluentIcon;
export declare const TextHeader4Filled: FluentIcon;
export declare const TextHeader4Regular: FluentIcon;
export declare const TextHeader4LinesCaretFilled: FluentIcon;
export declare const TextHeader4LinesCaretRegular: FluentIcon;
export declare const TextHeader5Filled: FluentIcon;
export declare const TextHeader5Regular: FluentIcon;
export declare const TextHeader6Filled: FluentIcon;
export declare const TextHeader6Regular: FluentIcon;
export declare const TextIndentDecreaseFilled: FluentIcon;
export declare const TextIndentDecreaseRegular: FluentIcon;
export declare const TextIndentDecreaseLtrFilled: FluentIcon;
