import type { FluentIcon } from "../utils/createFluentIcon";
export declare const CatchUpFilled: FluentIcon;
export declare const CatchUpRegular: FluentIcon;
export declare const Cellular3GFilled: FluentIcon;
export declare const Cellular3GRegular: FluentIcon;
export declare const Cellular4GFilled: FluentIcon;
export declare const Cellular4GRegular: FluentIcon;
export declare const Cellular5GFilled: FluentIcon;
export declare const Cellular5GRegular: FluentIcon;
export declare const CellularData1Filled: FluentIcon;
export declare const CellularData1Regular: FluentIcon;
export declare const CellularData2Filled: FluentIcon;
export declare const CellularData2Regular: FluentIcon;
export declare const CellularData3Filled: FluentIcon;
export declare const CellularData3Regular: FluentIcon;
export declare const CellularData4Filled: FluentIcon;
export declare const CellularData4Regular: FluentIcon;
export declare const CellularData5Filled: FluentIcon;
export declare const CellularData5Regular: FluentIcon;
export declare const CellularOffFilled: FluentIcon;
export declare const CellularOffRegular: FluentIcon;
export declare const CellularWarningFilled: FluentIcon;
export declare const CellularWarningRegular: FluentIcon;
export declare const CenterHorizontalFilled: FluentIcon;
export declare const CenterHorizontalRegular: FluentIcon;
export declare const CenterVerticalFilled: FluentIcon;
export declare const CenterVerticalRegular: FluentIcon;
export declare const CertificateColor: FluentIcon;
export declare const CertificateFilled: FluentIcon;
export declare const CertificateRegular: FluentIcon;
export declare const ChannelFilled: FluentIcon;
export declare const ChannelRegular: FluentIcon;
export declare const ChannelAddFilled: FluentIcon;
export declare const ChannelAddRegular: FluentIcon;
export declare const ChannelAlertFilled: FluentIcon;
export declare const ChannelAlertRegular: FluentIcon;
export declare const ChannelArrowLeftFilled: FluentIcon;
export declare const ChannelArrowLeftRegular: FluentIcon;
export declare const ChannelDismissFilled: FluentIcon;
export declare const ChannelDismissRegular: FluentIcon;
export declare const ChannelShareFilled: FluentIcon;
export declare const ChannelShareRegular: FluentIcon;
export declare const ChannelSubtractFilled: FluentIcon;
export declare const ChannelSubtractRegular: FluentIcon;
export declare const ChartMultipleColor: FluentIcon;
export declare const ChartMultipleFilled: FluentIcon;
export declare const ChartMultipleRegular: FluentIcon;
export declare const ChartPersonFilled: FluentIcon;
export declare const ChartPersonRegular: FluentIcon;
export declare const ChatColor: FluentIcon;
export declare const ChatFilled: FluentIcon;
export declare const ChatRegular: FluentIcon;
export declare const ChatAddColor: FluentIcon;
export declare const ChatAddFilled: FluentIcon;
export declare const ChatAddRegular: FluentIcon;
export declare const ChatArrowBackFilled: FluentIcon;
export declare const ChatArrowBackRegular: FluentIcon;
export declare const ChatArrowBackDownFilled: FluentIcon;
export declare const ChatArrowBackDownRegular: FluentIcon;
export declare const ChatArrowDoubleBackFilled: FluentIcon;
export declare const ChatArrowDoubleBackRegular: FluentIcon;
export declare const ChatBubblesQuestionColor: FluentIcon;
export declare const ChatBubblesQuestionFilled: FluentIcon;
export declare const ChatBubblesQuestionRegular: FluentIcon;
export declare const ChatCursorFilled: FluentIcon;
export declare const ChatCursorRegular: FluentIcon;
export declare const ChatDismissFilled: FluentIcon;
export declare const ChatDismissRegular: FluentIcon;
export declare const ChatEmptyFilled: FluentIcon;
export declare const ChatEmptyRegular: FluentIcon;
export declare const ChatHelpFilled: FluentIcon;
export declare const ChatHelpRegular: FluentIcon;
export declare const ChatHistoryFilled: FluentIcon;
export declare const ChatHistoryRegular: FluentIcon;
export declare const ChatLockFilled: FluentIcon;
export declare const ChatLockRegular: FluentIcon;
export declare const ChatMailFilled: FluentIcon;
export declare const ChatMailRegular: FluentIcon;
export declare const ChatMoreColor: FluentIcon;
export declare const ChatMultipleColor: FluentIcon;
export declare const ChatMultipleFilled: FluentIcon;
export declare const ChatMultipleRegular: FluentIcon;
export declare const ChatMultipleCheckmarkFilled: FluentIcon;
export declare const ChatMultipleCheckmarkRegular: FluentIcon;
export declare const ChatMultipleHeartFilled: FluentIcon;
export declare const ChatMultipleHeartRegular: FluentIcon;
export declare const ChatMultipleMinusFilled: FluentIcon;
export declare const ChatMultipleMinusRegular: FluentIcon;
export declare const ChatOffFilled: FluentIcon;
export declare const ChatOffRegular: FluentIcon;
export declare const ChatSettingsFilled: FluentIcon;
export declare const ChatSettingsRegular: FluentIcon;
export declare const ChatSparkleFilled: FluentIcon;
export declare const ChatSparkleRegular: FluentIcon;
export declare const ChatVideoFilled: FluentIcon;
export declare const ChatVideoRegular: FluentIcon;
export declare const ChatWarningFilled: FluentIcon;
export declare const ChatWarningRegular: FluentIcon;
export declare const CheckFilled: FluentIcon;
export declare const CheckRegular: FluentIcon;
export declare const Checkbox1Filled: FluentIcon;
export declare const Checkbox1Regular: FluentIcon;
export declare const CheckboxColor: FluentIcon;
export declare const Checkbox2Filled: FluentIcon;
export declare const Checkbox2Regular: FluentIcon;
export declare const CheckboxArrowRightFilled: FluentIcon;
export declare const CheckboxArrowRightRegular: FluentIcon;
export declare const CheckboxCheckedFilled: FluentIcon;
export declare const CheckboxCheckedRegular: FluentIcon;
export declare const CheckboxCheckedSyncFilled: FluentIcon;
export declare const CheckboxCheckedSyncRegular: FluentIcon;
export declare const CheckboxIndeterminateFilled: FluentIcon;
export declare const CheckboxIndeterminateRegular: FluentIcon;
export declare const CheckboxPersonColor: FluentIcon;
export declare const CheckboxPersonFilled: FluentIcon;
export declare const CheckboxPersonRegular: FluentIcon;
export declare const CheckboxUncheckedFilled: FluentIcon;
export declare const CheckboxUncheckedRegular: FluentIcon;
export declare const CheckboxWarningFilled: FluentIcon;
export declare const CheckboxWarningRegular: FluentIcon;
export declare const CheckmarkFilled: FluentIcon;
export declare const CheckmarkRegular: FluentIcon;
export declare const CheckmarkCircleColor: FluentIcon;
export declare const CheckmarkCircleFilled: FluentIcon;
export declare const CheckmarkCircleRegular: FluentIcon;
export declare const CheckmarkCircleHintFilled: FluentIcon;
export declare const CheckmarkCircleHintRegular: FluentIcon;
export declare const CheckmarkCircleSquareFilled: FluentIcon;
export declare const CheckmarkCircleSquareRegular: FluentIcon;
export declare const CheckmarkCircleWarningFilled: FluentIcon;
export declare const CheckmarkCircleWarningRegular: FluentIcon;
export declare const CheckmarkLockFilled: FluentIcon;
export declare const CheckmarkLockRegular: FluentIcon;
export declare const CheckmarkNoteFilled: FluentIcon;
export declare const CheckmarkNoteRegular: FluentIcon;
export declare const CheckmarkSquareFilled: FluentIcon;
export declare const CheckmarkSquareRegular: FluentIcon;
export declare const CheckmarkStarburstFilled: FluentIcon;
export declare const CheckmarkStarburstRegular: FluentIcon;
export declare const CheckmarkUnderlineCircleFilled: FluentIcon;
export declare const CheckmarkUnderlineCircleRegular: FluentIcon;
export declare const ChessFilled: FluentIcon;
export declare const ChessRegular: FluentIcon;
export declare const ChevronCircleDownFilled: FluentIcon;
export declare const ChevronCircleDownRegular: FluentIcon;
export declare const ChevronCircleLeftFilled: FluentIcon;
export declare const ChevronCircleLeftRegular: FluentIcon;
export declare const ChevronCircleRightFilled: FluentIcon;
export declare const ChevronCircleRightRegular: FluentIcon;
export declare const ChevronCircleUpFilled: FluentIcon;
export declare const ChevronCircleUpRegular: FluentIcon;
export declare const ChevronDoubleDownFilled: FluentIcon;
export declare const ChevronDoubleDownRegular: FluentIcon;
export declare const ChevronDoubleLeftFilled: FluentIcon;
export declare const ChevronDoubleLeftRegular: FluentIcon;
export declare const ChevronDoubleRightFilled: FluentIcon;
export declare const ChevronDoubleRightRegular: FluentIcon;
export declare const ChevronDoubleUpFilled: FluentIcon;
export declare const ChevronDoubleUpRegular: FluentIcon;
export declare const ChevronDownFilled: FluentIcon;
export declare const ChevronDownRegular: FluentIcon;
export declare const ChevronDownUpFilled: FluentIcon;
export declare const ChevronDownUpRegular: FluentIcon;
export declare const ChevronLeftFilled: FluentIcon;
export declare const ChevronLeftRegular: FluentIcon;
export declare const ChevronRightFilled: FluentIcon;
export declare const ChevronRightRegular: FluentIcon;
export declare const ChevronUpFilled: FluentIcon;
export declare const ChevronUpRegular: FluentIcon;
export declare const ChevronUpDownFilled: FluentIcon;
export declare const ChevronUpDownRegular: FluentIcon;
export declare const CircleFilled: FluentIcon;
export declare const CircleRegular: FluentIcon;
export declare const CircleEditFilled: FluentIcon;
export declare const CircleEditRegular: FluentIcon;
export declare const CircleEraserFilled: FluentIcon;
export declare const CircleEraserRegular: FluentIcon;
export declare const CircleHalfFillFilled: FluentIcon;
export declare const CircleHalfFillRegular: FluentIcon;
export declare const CircleHighlightFilled: FluentIcon;
export declare const CircleHighlightRegular: FluentIcon;
export declare const CircleHintFilled: FluentIcon;
export declare const CircleHintRegular: FluentIcon;
export declare const CircleHintCursorFilled: FluentIcon;
export declare const CircleHintCursorRegular: FluentIcon;
export declare const CircleHintDismissFilled: FluentIcon;
export declare const CircleHintDismissRegular: FluentIcon;
export declare const CircleHintHalfVerticalFilled: FluentIcon;
export declare const CircleHintHalfVerticalRegular: FluentIcon;
export declare const CircleImageFilled: FluentIcon;
export declare const CircleImageRegular: FluentIcon;
export declare const CircleLineFilled: FluentIcon;
export declare const CircleLineRegular: FluentIcon;
export declare const CircleMultipleConcentricFilled: FluentIcon;
export declare const CircleMultipleConcentricRegular: FluentIcon;
export declare const CircleMultipleHintCheckmarkFilled: FluentIcon;
export declare const CircleMultipleHintCheckmarkRegular: FluentIcon;
export declare const CircleMultipleSubtractCheckmarkFilled: FluentIcon;
export declare const CircleMultipleSubtractCheckmarkRegular: FluentIcon;
export declare const CircleOffFilled: FluentIcon;
export declare const CircleOffRegular: FluentIcon;
export declare const CircleShadowFilled: FluentIcon;
export declare const CircleShadowRegular: FluentIcon;
export declare const CircleSmallFilled: FluentIcon;
export declare const CircleSmallRegular: FluentIcon;
export declare const CircleSparkleFilled: FluentIcon;
export declare const CircleSparkleRegular: FluentIcon;
export declare const CityFilled: FluentIcon;
export declare const CityRegular: FluentIcon;
export declare const ClassFilled: FluentIcon;
export declare const ClassRegular: FluentIcon;
export declare const ClassificationFilled: FluentIcon;
export declare const ClassificationRegular: FluentIcon;
export declare const ClearFormattingFilled: FluentIcon;
export declare const ClearFormattingRegular: FluentIcon;
export declare const ClipboardColor: FluentIcon;
export declare const ClipboardFilled: FluentIcon;
export declare const ClipboardRegular: FluentIcon;
export declare const Clipboard3DayFilled: FluentIcon;
export declare const Clipboard3DayRegular: FluentIcon;
export declare const ClipboardArrowRightFilled: FluentIcon;
export declare const ClipboardArrowRightRegular: FluentIcon;
export declare const ClipboardBrushFilled: FluentIcon;
export declare const ClipboardBrushRegular: FluentIcon;
export declare const ClipboardBulletListFilled: FluentIcon;
export declare const ClipboardBulletListRegular: FluentIcon;
export declare const ClipboardBulletListLtrFilled: FluentIcon;
export declare const ClipboardBulletListLtrRegular: FluentIcon;
export declare const ClipboardBulletListRtlFilled: FluentIcon;
export declare const ClipboardBulletListRtlRegular: FluentIcon;
export declare const ClipboardCheckmarkFilled: FluentIcon;
export declare const ClipboardCheckmarkRegular: FluentIcon;
export declare const ClipboardClockFilled: FluentIcon;
export declare const ClipboardClockRegular: FluentIcon;
export declare const ClipboardCodeFilled: FluentIcon;
export declare const ClipboardCodeRegular: FluentIcon;
export declare const ClipboardDataBarFilled: FluentIcon;
export declare const ClipboardDataBarRegular: FluentIcon;
export declare const ClipboardDayFilled: FluentIcon;
export declare const ClipboardDayRegular: FluentIcon;
export declare const ClipboardEditFilled: FluentIcon;
export declare const ClipboardEditRegular: FluentIcon;
export declare const ClipboardErrorFilled: FluentIcon;
export declare const ClipboardErrorRegular: FluentIcon;
export declare const ClipboardHeartFilled: FluentIcon;
export declare const ClipboardHeartRegular: FluentIcon;
export declare const ClipboardImageFilled: FluentIcon;
export declare const ClipboardImageRegular: FluentIcon;
export declare const ClipboardLetterFilled: FluentIcon;
export declare const ClipboardLetterRegular: FluentIcon;
export declare const ClipboardLinkFilled: FluentIcon;
export declare const ClipboardLinkRegular: FluentIcon;
export declare const ClipboardMathFormulaFilled: FluentIcon;
export declare const ClipboardMathFormulaRegular: FluentIcon;
export declare const ClipboardMonthFilled: FluentIcon;
export declare const ClipboardMonthRegular: FluentIcon;
export declare const ClipboardMoreFilled: FluentIcon;
export declare const ClipboardMoreRegular: FluentIcon;
export declare const ClipboardNoteFilled: FluentIcon;
export declare const ClipboardNoteRegular: FluentIcon;
export declare const ClipboardNumber123Filled: FluentIcon;
export declare const ClipboardNumber123Regular: FluentIcon;
export declare const ClipboardPasteFilled: FluentIcon;
export declare const ClipboardPasteRegular: FluentIcon;
export declare const ClipboardPulseFilled: FluentIcon;
export declare const ClipboardPulseRegular: FluentIcon;
export declare const ClipboardSearchFilled: FluentIcon;
export declare const ClipboardSearchRegular: FluentIcon;
export declare const ClipboardSettingsFilled: FluentIcon;
export declare const ClipboardSettingsRegular: FluentIcon;
export declare const ClipboardTaskColor: FluentIcon;
export declare const ClipboardTaskFilled: FluentIcon;
export declare const ClipboardTaskRegular: FluentIcon;
export declare const ClipboardTaskAddFilled: FluentIcon;
export declare const ClipboardTaskAddRegular: FluentIcon;
export declare const ClipboardTaskListLtrFilled: FluentIcon;
export declare const ClipboardTaskListLtrRegular: FluentIcon;
export declare const ClipboardTaskListRtlFilled: FluentIcon;
export declare const ClipboardTaskListRtlRegular: FluentIcon;
export declare const ClipboardTextEditColor: FluentIcon;
export declare const ClipboardTextEditFilled: FluentIcon;
export declare const ClipboardTextEditRegular: FluentIcon;
export declare const ClipboardTextLtrFilled: FluentIcon;
export declare const ClipboardTextLtrRegular: FluentIcon;
export declare const ClipboardTextRtlFilled: FluentIcon;
export declare const ClipboardTextRtlRegular: FluentIcon;
export declare const ClockColor: FluentIcon;
export declare const ClockFilled: FluentIcon;
export declare const ClockRegular: FluentIcon;
export declare const ClockAlarmColor: FluentIcon;
export declare const ClockAlarmFilled: FluentIcon;
export declare const ClockAlarmRegular: FluentIcon;
export declare const ClockArrowDownloadFilled: FluentIcon;
export declare const ClockArrowDownloadRegular: FluentIcon;
export declare const ClockBillFilled: FluentIcon;
export declare const ClockBillRegular: FluentIcon;
export declare const ClockDismissFilled: FluentIcon;
export declare const ClockDismissRegular: FluentIcon;
export declare const ClockLockFilled: FluentIcon;
export declare const ClockLockRegular: FluentIcon;
export declare const ClockPauseFilled: FluentIcon;
export declare const ClockPauseRegular: FluentIcon;
export declare const ClockSparkleFilled: FluentIcon;
export declare const ClockSparkleRegular: FluentIcon;
export declare const ClockToolboxFilled: FluentIcon;
export declare const ClockToolboxRegular: FluentIcon;
export declare const ClosedCaptionFilled: FluentIcon;
export declare const ClosedCaptionRegular: FluentIcon;
export declare const ClosedCaptionOffFilled: FluentIcon;
export declare const ClosedCaptionOffRegular: FluentIcon;
export declare const ClothesHangerFilled: FluentIcon;
export declare const ClothesHangerRegular: FluentIcon;
export declare const CloudColor: FluentIcon;
export declare const CloudFilled: FluentIcon;
export declare const CloudRegular: FluentIcon;
export declare const CloudAddFilled: FluentIcon;
export declare const CloudAddRegular: FluentIcon;
export declare const CloudArchiveFilled: FluentIcon;
export declare const CloudArchiveRegular: FluentIcon;
export declare const CloudArrowDownFilled: FluentIcon;
export declare const CloudArrowDownRegular: FluentIcon;
export declare const CloudArrowRightFilled: FluentIcon;
export declare const CloudArrowRightRegular: FluentIcon;
export declare const CloudArrowUpFilled: FluentIcon;
export declare const CloudArrowUpRegular: FluentIcon;
export declare const CloudBeakerFilled: FluentIcon;
export declare const CloudBeakerRegular: FluentIcon;
export declare const CloudBidirectionalFilled: FluentIcon;
export declare const CloudBidirectionalRegular: FluentIcon;
export declare const CloudCheckmarkFilled: FluentIcon;
export declare const CloudCheckmarkRegular: FluentIcon;
export declare const CloudCubeFilled: FluentIcon;
export declare const CloudCubeRegular: FluentIcon;
export declare const CloudDatabaseFilled: FluentIcon;
export declare const CloudDatabaseRegular: FluentIcon;
export declare const CloudDesktopFilled: FluentIcon;
export declare const CloudDesktopRegular: FluentIcon;
export declare const CloudDismissColor: FluentIcon;
export declare const CloudDismissFilled: FluentIcon;
export declare const CloudDismissRegular: FluentIcon;
export declare const CloudEditFilled: FluentIcon;
export declare const CloudEditRegular: FluentIcon;
export declare const CloudErrorFilled: FluentIcon;
export declare const CloudErrorRegular: FluentIcon;
export declare const CloudFlowFilled: FluentIcon;
export declare const CloudFlowRegular: FluentIcon;
export declare const CloudLinkFilled: FluentIcon;
export declare const CloudLinkRegular: FluentIcon;
export declare const CloudOffFilled: FluentIcon;
export declare const CloudOffRegular: FluentIcon;
export declare const CloudSwapFilled: FluentIcon;
export declare const CloudSwapRegular: FluentIcon;
export declare const CloudSyncFilled: FluentIcon;
export declare const CloudSyncRegular: FluentIcon;
export declare const CloudWordsColor: FluentIcon;
export declare const CloudWordsFilled: FluentIcon;
export declare const CloudWordsRegular: FluentIcon;
export declare const CloverFilled: FluentIcon;
export declare const CloverRegular: FluentIcon;
export declare const CodeColor: FluentIcon;
export declare const CodeFilled: FluentIcon;
export declare const CodeRegular: FluentIcon;
export declare const CodeBlockColor: FluentIcon;
export declare const CodeBlockFilled: FluentIcon;
export declare const CodeBlockRegular: FluentIcon;
export declare const CodeBlockEditFilled: FluentIcon;
export declare const CodeBlockEditRegular: FluentIcon;
export declare const CodeCircleFilled: FluentIcon;
export declare const CodeCircleRegular: FluentIcon;
export declare const CodeTextFilled: FluentIcon;
export declare const CodeTextRegular: FluentIcon;
export declare const CodeTextEditFilled: FluentIcon;
export declare const CodeTextEditRegular: FluentIcon;
export declare const CoinMultipleColor: FluentIcon;
export declare const CoinMultipleFilled: FluentIcon;
export declare const CoinMultipleRegular: FluentIcon;
export declare const CoinStackFilled: FluentIcon;
export declare const CoinStackRegular: FluentIcon;
export declare const CollectionsFilled: FluentIcon;
export declare const CollectionsRegular: FluentIcon;
export declare const CollectionsAddFilled: FluentIcon;
export declare const CollectionsAddRegular: FluentIcon;
export declare const CollectionsEmptyFilled: FluentIcon;
export declare const CollectionsEmptyRegular: FluentIcon;
export declare const ColorFilled: FluentIcon;
export declare const ColorRegular: FluentIcon;
export declare const ColorBackgroundFilled: FluentIcon;
export declare const ColorBackgroundRegular: FluentIcon;
export declare const ColorBackgroundAccentRegular: FluentIcon;
export declare const ColorFillFilled: FluentIcon;
export declare const ColorFillRegular: FluentIcon;
export declare const ColorFillAccentRegular: FluentIcon;
export declare const ColorLineFilled: FluentIcon;
export declare const ColorLineRegular: FluentIcon;
export declare const ColorLineAccentRegular: FluentIcon;
export declare const ColumnFilled: FluentIcon;
export declare const ColumnRegular: FluentIcon;
export declare const ColumnArrowRightFilled: FluentIcon;
export declare const ColumnArrowRightRegular: FluentIcon;
export declare const ColumnDoubleCompareFilled: FluentIcon;
export declare const ColumnDoubleCompareRegular: FluentIcon;
export declare const ColumnEditFilled: FluentIcon;
export declare const ColumnEditRegular: FluentIcon;
export declare const ColumnSingleCompareFilled: FluentIcon;
export declare const ColumnSingleCompareRegular: FluentIcon;
export declare const ColumnTripleFilled: FluentIcon;
export declare const ColumnTripleRegular: FluentIcon;
export declare const ColumnTripleEditFilled: FluentIcon;
export declare const ColumnTripleEditRegular: FluentIcon;
export declare const CommaFilled: FluentIcon;
export declare const CommaRegular: FluentIcon;
export declare const CommentColor: FluentIcon;
export declare const CommentFilled: FluentIcon;
export declare const CommentRegular: FluentIcon;
export declare const CommentAddFilled: FluentIcon;
export declare const CommentAddRegular: FluentIcon;
export declare const CommentArrowLeftFilled: FluentIcon;
export declare const CommentArrowLeftRegular: FluentIcon;
export declare const CommentArrowRightFilled: FluentIcon;
export declare const CommentArrowRightRegular: FluentIcon;
export declare const CommentBadgeFilled: FluentIcon;
export declare const CommentBadgeRegular: FluentIcon;
export declare const CommentCheckmarkFilled: FluentIcon;
export declare const CommentCheckmarkRegular: FluentIcon;
export declare const CommentDismissFilled: FluentIcon;
export declare const CommentDismissRegular: FluentIcon;
export declare const CommentEditFilled: FluentIcon;
export declare const CommentEditRegular: FluentIcon;
export declare const CommentErrorFilled: FluentIcon;
export declare const CommentErrorRegular: FluentIcon;
export declare const CommentLightningFilled: FluentIcon;
export declare const CommentLightningRegular: FluentIcon;
export declare const CommentLinkFilled: FluentIcon;
export declare const CommentLinkRegular: FluentIcon;
export declare const CommentMentionFilled: FluentIcon;
export declare const CommentMentionRegular: FluentIcon;
export declare const CommentMultipleColor: FluentIcon;
export declare const CommentMultipleFilled: FluentIcon;
export declare const CommentMultipleRegular: FluentIcon;
export declare const CommentMultipleCheckmarkFilled: FluentIcon;
export declare const CommentMultipleCheckmarkRegular: FluentIcon;
export declare const CommentMultipleLinkFilled: FluentIcon;
export declare const CommentMultipleLinkRegular: FluentIcon;
export declare const CommentMultipleMentionFilled: FluentIcon;
export declare const CommentMultipleMentionRegular: FluentIcon;
export declare const CommentNoteFilled: FluentIcon;
export declare const CommentNoteRegular: FluentIcon;
export declare const CommentOffFilled: FluentIcon;
export declare const CommentOffRegular: FluentIcon;
export declare const CommentQuoteFilled: FluentIcon;
export declare const CommentQuoteRegular: FluentIcon;
export declare const CommentTextFilled: FluentIcon;
export declare const CommentTextRegular: FluentIcon;
export declare const CommunicationFilled: FluentIcon;
export declare const CommunicationRegular: FluentIcon;
export declare const CommunicationPersonFilled: FluentIcon;
export declare const CommunicationPersonRegular: FluentIcon;
export declare const CommunicationShieldFilled: FluentIcon;
export declare const CommunicationShieldRegular: FluentIcon;
export declare const CompassNorthwestFilled: FluentIcon;
export declare const CompassNorthwestRegular: FluentIcon;
export declare const CompassTrueNorthFilled: FluentIcon;
export declare const CompassTrueNorthRegular: FluentIcon;
export declare const ComposeFilled: FluentIcon;
export declare const ComposeRegular: FluentIcon;
export declare const ConferenceRoomFilled: FluentIcon;
export declare const ConferenceRoomRegular: FluentIcon;
export declare const ConnectedFilled: FluentIcon;
export declare const ConnectedRegular: FluentIcon;
export declare const ConnectorFilled: FluentIcon;
export declare const ConnectorRegular: FluentIcon;
export declare const ContactCardColor: FluentIcon;
export declare const ContactCardFilled: FluentIcon;
export declare const ContactCardRegular: FluentIcon;
export declare const ContactCardGroupFilled: FluentIcon;
export declare const ContactCardGroupRegular: FluentIcon;
export declare const ContactCardLinkFilled: FluentIcon;
export declare const ContactCardLinkRegular: FluentIcon;
export declare const ContactCardRibbonFilled: FluentIcon;
export declare const ContactCardRibbonRegular: FluentIcon;
export declare const ContentSettingsFilled: FluentIcon;
export declare const ContentSettingsRegular: FluentIcon;
export declare const ContentViewColor: FluentIcon;
export declare const ContentViewFilled: FluentIcon;
export declare const ContentViewRegular: FluentIcon;
export declare const ContentViewGalleryFilled: FluentIcon;
export declare const ContentViewGalleryRegular: FluentIcon;
export declare const ContentViewGalleryLightningFilled: FluentIcon;
export declare const ContentViewGalleryLightningRegular: FluentIcon;
export declare const ContractDownLeftFilled: FluentIcon;
export declare const ContractDownLeftRegular: FluentIcon;
export declare const ContractUpRightFilled: FluentIcon;
export declare const ContractUpRightRegular: FluentIcon;
export declare const ControlButtonFilled: FluentIcon;
export declare const ControlButtonRegular: FluentIcon;
export declare const ConvertRangeFilled: FluentIcon;
export declare const ConvertRangeRegular: FluentIcon;
export declare const CookiesFilled: FluentIcon;
export declare const CookiesRegular: FluentIcon;
export declare const CopyFilled: FluentIcon;
export declare const CopyRegular: FluentIcon;
export declare const CopyAddFilled: FluentIcon;
export declare const CopyAddRegular: FluentIcon;
export declare const CopyArrowRightFilled: FluentIcon;
export declare const CopyArrowRightRegular: FluentIcon;
export declare const CopySelectFilled: FluentIcon;
export declare const CopySelectRegular: FluentIcon;
export declare const CouchFilled: FluentIcon;
export declare const CouchRegular: FluentIcon;
export declare const CreditCardClockFilled: FluentIcon;
export declare const CreditCardClockRegular: FluentIcon;
export declare const CreditCardPersonFilled: FluentIcon;
export declare const CreditCardPersonRegular: FluentIcon;
export declare const CreditCardToolboxFilled: FluentIcon;
export declare const CreditCardToolboxRegular: FluentIcon;
export declare const CropFilled: FluentIcon;
export declare const CropRegular: FluentIcon;
export declare const CropArrowRotateFilled: FluentIcon;
export declare const CropArrowRotateRegular: FluentIcon;
export declare const CropInterimFilled: FluentIcon;
export declare const CropInterimRegular: FluentIcon;
export declare const CropInterimOffFilled: FluentIcon;
export declare const CropInterimOffRegular: FluentIcon;
export declare const CrownFilled: FluentIcon;
export declare const CrownRegular: FluentIcon;
export declare const CrownSubtractFilled: FluentIcon;
export declare const CrownSubtractRegular: FluentIcon;
export declare const CubeFilled: FluentIcon;
export declare const CubeRegular: FluentIcon;
export declare const CubeAddFilled: FluentIcon;
export declare const CubeAddRegular: FluentIcon;
export declare const CubeArrowCurveDownFilled: FluentIcon;
export declare const CubeArrowCurveDownRegular: FluentIcon;
export declare const CubeCheckmarkFilled: FluentIcon;
export declare const CubeCheckmarkRegular: FluentIcon;
export declare const CubeLinkFilled: FluentIcon;
export declare const CubeLinkRegular: FluentIcon;
export declare const CubeMultipleFilled: FluentIcon;
export declare const CubeMultipleRegular: FluentIcon;
export declare const CubeQuickFilled: FluentIcon;
export declare const CubeQuickRegular: FluentIcon;
export declare const CubeRotateFilled: FluentIcon;
export declare const CubeRotateRegular: FluentIcon;
export declare const CubeSyncFilled: FluentIcon;
export declare const CubeSyncRegular: FluentIcon;
export declare const CubeTreeFilled: FluentIcon;
export declare const CubeTreeRegular: FluentIcon;
export declare const CurrencyDollarEuroFilled: FluentIcon;
export declare const CurrencyDollarEuroRegular: FluentIcon;
export declare const CurrencyDollarRupeeFilled: FluentIcon;
export declare const CurrencyDollarRupeeRegular: FluentIcon;
export declare const CursorFilled: FluentIcon;
export declare const CursorRegular: FluentIcon;
export declare const CursorClickFilled: FluentIcon;
export declare const CursorClickRegular: FluentIcon;
export declare const CursorHoverFilled: FluentIcon;
export declare const CursorHoverRegular: FluentIcon;
export declare const CursorHoverOffFilled: FluentIcon;
export declare const CursorHoverOffRegular: FluentIcon;
export declare const CursorProhibitedFilled: FluentIcon;
export declare const CursorProhibitedRegular: FluentIcon;
export declare const CutFilled: FluentIcon;
export declare const CutRegular: FluentIcon;
export declare const DarkThemeFilled: FluentIcon;
export declare const DarkThemeRegular: FluentIcon;
export declare const DataAreaColor: FluentIcon;
export declare const DataAreaFilled: FluentIcon;
export declare const DataAreaRegular: FluentIcon;
export declare const DataBarHorizontalFilled: FluentIcon;
export declare const DataBarHorizontalRegular: FluentIcon;
export declare const DataBarVerticalFilled: FluentIcon;
export declare const DataBarVerticalRegular: FluentIcon;
export declare const DataBarVerticalAddFilled: FluentIcon;
export declare const DataBarVerticalAddRegular: FluentIcon;
export declare const DataBarVerticalArrowDownFilled: FluentIcon;
export declare const DataBarVerticalArrowDownRegular: FluentIcon;
export declare const DataBarVerticalAscendingColor: FluentIcon;
export declare const DataBarVerticalAscendingFilled: FluentIcon;
export declare const DataBarVerticalAscendingRegular: FluentIcon;
export declare const DataBarVerticalEditFilled: FluentIcon;
export declare const DataBarVerticalEditRegular: FluentIcon;
export declare const DataBarVerticalStarFilled: FluentIcon;
export declare const DataBarVerticalStarRegular: FluentIcon;
export declare const DataFunnelFilled: FluentIcon;
export declare const DataFunnelRegular: FluentIcon;
export declare const DataHistogramFilled: FluentIcon;
export declare const DataHistogramRegular: FluentIcon;
export declare const DataLineColor: FluentIcon;
export declare const DataLineFilled: FluentIcon;
export declare const DataLineRegular: FluentIcon;
export declare const DataPieColor: FluentIcon;
export declare const DataPieFilled: FluentIcon;
export declare const DataPieRegular: FluentIcon;
export declare const DataScatterColor: FluentIcon;
export declare const DataScatterFilled: FluentIcon;
export declare const DataScatterRegular: FluentIcon;
export declare const DataSunburstFilled: FluentIcon;
export declare const DataSunburstRegular: FluentIcon;
export declare const DataTreemapFilled: FluentIcon;
export declare const DataTreemapRegular: FluentIcon;
export declare const DataTrendingColor: FluentIcon;
export declare const DataTrendingFilled: FluentIcon;
export declare const DataTrendingRegular: FluentIcon;
export declare const DataUsageFilled: FluentIcon;
export declare const DataUsageRegular: FluentIcon;
export declare const DataUsageCheckmarkFilled: FluentIcon;
export declare const DataUsageCheckmarkRegular: FluentIcon;
export declare const DataUsageEditFilled: FluentIcon;
export declare const DataUsageEditRegular: FluentIcon;
export declare const DataUsageSettingsFilled: FluentIcon;
export declare const DataUsageSettingsRegular: FluentIcon;
export declare const DataUsageSparkleFilled: FluentIcon;
export declare const DataUsageSparkleRegular: FluentIcon;
export declare const DataUsageToolboxFilled: FluentIcon;
export declare const DataUsageToolboxRegular: FluentIcon;
export declare const DataWaterfallFilled: FluentIcon;
export declare const DataWaterfallRegular: FluentIcon;
export declare const DataWhiskerFilled: FluentIcon;
export declare const DataWhiskerRegular: FluentIcon;
export declare const DatabaseColor: FluentIcon;
export declare const DatabaseFilled: FluentIcon;
export declare const DatabaseRegular: FluentIcon;
export declare const DatabaseArrowDownFilled: FluentIcon;
export declare const DatabaseArrowDownRegular: FluentIcon;
export declare const DatabaseArrowRightFilled: FluentIcon;
export declare const DatabaseArrowRightRegular: FluentIcon;
export declare const DatabaseArrowUpFilled: FluentIcon;
export declare const DatabaseArrowUpRegular: FluentIcon;
export declare const DatabaseCheckmarkFilled: FluentIcon;
export declare const DatabaseCheckmarkRegular: FluentIcon;
export declare const DatabaseLightningFilled: FluentIcon;
export declare const DatabaseLightningRegular: FluentIcon;
export declare const DatabaseLinkFilled: FluentIcon;
export declare const DatabaseLinkRegular: FluentIcon;
export declare const DatabaseMultipleFilled: FluentIcon;
export declare const DatabaseMultipleRegular: FluentIcon;
export declare const DatabasePersonFilled: FluentIcon;
export declare const DatabasePersonRegular: FluentIcon;
export declare const DatabasePlugConnectedFilled: FluentIcon;
export declare const DatabasePlugConnectedRegular: FluentIcon;
export declare const DatabaseSearchFilled: FluentIcon;
export declare const DatabaseSearchRegular: FluentIcon;
export declare const DatabaseSwitchFilled: FluentIcon;
export declare const DatabaseSwitchRegular: FluentIcon;
export declare const DatabaseWarningFilled: FluentIcon;
export declare const DatabaseWarningRegular: FluentIcon;
export declare const DatabaseWindowFilled: FluentIcon;
export declare const DatabaseWindowRegular: FluentIcon;
export declare const DecimalArrowLeftFilled: FluentIcon;
export declare const DecimalArrowLeftRegular: FluentIcon;
export declare const DecimalArrowRightFilled: FluentIcon;
export declare const DecimalArrowRightRegular: FluentIcon;
export declare const DeleteFilled: FluentIcon;
export declare const DeleteRegular: FluentIcon;
export declare const DeleteArrowBackFilled: FluentIcon;
export declare const DeleteArrowBackRegular: FluentIcon;
export declare const DeleteDismissFilled: FluentIcon;
export declare const DeleteDismissRegular: FluentIcon;
export declare const DeleteLinesFilled: FluentIcon;
export declare const DeleteLinesRegular: FluentIcon;
export declare const DeleteOffFilled: FluentIcon;
export declare const DeleteOffRegular: FluentIcon;
export declare const DentistFilled: FluentIcon;
export declare const DentistRegular: FluentIcon;
export declare const DesignIdeasColor: FluentIcon;
export declare const DesignIdeasFilled: FluentIcon;
export declare const DesignIdeasRegular: FluentIcon;
export declare const DeskFilled: FluentIcon;
export declare const DeskRegular: FluentIcon;
export declare const DeskMultipleFilled: FluentIcon;
export declare const DeskMultipleRegular: FluentIcon;
export declare const DeskSparkleFilled: FluentIcon;
export declare const DeskSparkleRegular: FluentIcon;
export declare const DesktopFilled: FluentIcon;
export declare const DesktopRegular: FluentIcon;
export declare const DesktopArrowDownFilled: FluentIcon;
export declare const DesktopArrowDownRegular: FluentIcon;
export declare const DesktopArrowDownOffFilled: FluentIcon;
export declare const DesktopArrowDownOffRegular: FluentIcon;
export declare const DesktopArrowRightFilled: FluentIcon;
export declare const DesktopArrowRightRegular: FluentIcon;
export declare const DesktopCheckmarkFilled: FluentIcon;
export declare const DesktopCheckmarkRegular: FluentIcon;
export declare const DesktopCursorFilled: FluentIcon;
export declare const DesktopCursorRegular: FluentIcon;
export declare const DesktopEditFilled: FluentIcon;
export declare const DesktopEditRegular: FluentIcon;
export declare const DesktopFlowFilled: FluentIcon;
export declare const DesktopFlowRegular: FluentIcon;
export declare const DesktopKeyboardFilled: FluentIcon;
export declare const DesktopKeyboardRegular: FluentIcon;
export declare const DesktopMacFilled: FluentIcon;
export declare const DesktopMacRegular: FluentIcon;
export declare const DesktopOffFilled: FluentIcon;
export declare const DesktopOffRegular: FluentIcon;
export declare const DesktopPulseFilled: FluentIcon;
export declare const DesktopPulseRegular: FluentIcon;
export declare const DesktopSignalFilled: FluentIcon;
export declare const DesktopSignalRegular: FluentIcon;
export declare const DesktopSpeakerFilled: FluentIcon;
export declare const DesktopSpeakerRegular: FluentIcon;
export declare const DesktopSpeakerOffFilled: FluentIcon;
export declare const DesktopSpeakerOffRegular: FluentIcon;
export declare const DesktopSyncFilled: FluentIcon;
export declare const DesktopSyncRegular: FluentIcon;
export declare const DesktopToolboxFilled: FluentIcon;
export declare const DesktopToolboxRegular: FluentIcon;
export declare const DesktopTowerFilled: FluentIcon;
export declare const DesktopTowerRegular: FluentIcon;
export declare const DeveloperBoardFilled: FluentIcon;
export declare const DeveloperBoardRegular: FluentIcon;
export declare const DeveloperBoardLightningFilled: FluentIcon;
export declare const DeveloperBoardLightningRegular: FluentIcon;
export declare const DeveloperBoardLightningToolboxFilled: FluentIcon;
export declare const DeveloperBoardLightningToolboxRegular: FluentIcon;
export declare const DeveloperBoardSearchFilled: FluentIcon;
export declare const DeveloperBoardSearchRegular: FluentIcon;
export declare const DeviceEqFilled: FluentIcon;
export declare const DeviceEqRegular: FluentIcon;
export declare const DeviceMeetingRoomFilled: FluentIcon;
export declare const DeviceMeetingRoomRegular: FluentIcon;
export declare const DeviceMeetingRoomRemoteFilled: FluentIcon;
export declare const DeviceMeetingRoomRemoteRegular: FluentIcon;
export declare const DiagramFilled: FluentIcon;
export declare const DiagramRegular: FluentIcon;
export declare const DialpadFilled: FluentIcon;
export declare const DialpadRegular: FluentIcon;
export declare const DialpadOffFilled: FluentIcon;
export declare const DialpadOffRegular: FluentIcon;
export declare const DialpadQuestionMarkFilled: FluentIcon;
export declare const DialpadQuestionMarkRegular: FluentIcon;
export declare const DiamondFilled: FluentIcon;
export declare const DiamondRegular: FluentIcon;
export declare const DiamondDismissFilled: FluentIcon;
export declare const DiamondDismissRegular: FluentIcon;
export declare const DirectionsFilled: FluentIcon;
export declare const DirectionsRegular: FluentIcon;
export declare const DishwasherFilled: FluentIcon;
export declare const DishwasherRegular: FluentIcon;
export declare const DismissFilled: FluentIcon;
export declare const DismissRegular: FluentIcon;
export declare const DismissCircleColor: FluentIcon;
export declare const DismissCircleFilled: FluentIcon;
export declare const DismissCircleRegular: FluentIcon;
export declare const DismissSquareFilled: FluentIcon;
export declare const DismissSquareRegular: FluentIcon;
export declare const DismissSquareMultipleFilled: FluentIcon;
export declare const DismissSquareMultipleRegular: FluentIcon;
export declare const DiversityColor: FluentIcon;
export declare const DiversityFilled: FluentIcon;
export declare const DiversityRegular: FluentIcon;
export declare const DividerShortFilled: FluentIcon;
export declare const DividerShortRegular: FluentIcon;
export declare const DividerTallFilled: FluentIcon;
export declare const DividerTallRegular: FluentIcon;
export declare const DockFilled: FluentIcon;
export declare const DockRegular: FluentIcon;
export declare const DockRowFilled: FluentIcon;
export declare const DockRowRegular: FluentIcon;
export declare const DoctorFilled: FluentIcon;
export declare const DoctorRegular: FluentIcon;
export declare const Document100Filled: FluentIcon;
export declare const Document100Regular: FluentIcon;
export declare const DocumentColor: FluentIcon;
export declare const DocumentFilled: FluentIcon;
export declare const DocumentRegular: FluentIcon;
export declare const DocumentAddColor: FluentIcon;
export declare const DocumentAddFilled: FluentIcon;
export declare const DocumentAddRegular: FluentIcon;
export declare const DocumentArrowDownFilled: FluentIcon;
export declare const DocumentArrowDownRegular: FluentIcon;
export declare const DocumentArrowLeftFilled: FluentIcon;
export declare const DocumentArrowLeftRegular: FluentIcon;
export declare const DocumentArrowRightFilled: FluentIcon;
export declare const DocumentArrowRightRegular: FluentIcon;
export declare const DocumentArrowUpFilled: FluentIcon;
export declare const DocumentArrowUpRegular: FluentIcon;
export declare const DocumentBorderFilled: FluentIcon;
export declare const DocumentBorderRegular: FluentIcon;
export declare const DocumentBorderPrintFilled: FluentIcon;
export declare const DocumentBorderPrintRegular: FluentIcon;
export declare const DocumentBriefcaseFilled: FluentIcon;
export declare const DocumentBriefcaseRegular: FluentIcon;
export declare const DocumentBulletListFilled: FluentIcon;
export declare const DocumentBulletListRegular: FluentIcon;
export declare const DocumentBulletListArrowLeftFilled: FluentIcon;
export declare const DocumentBulletListArrowLeftRegular: FluentIcon;
export declare const DocumentBulletListClockFilled: FluentIcon;
export declare const DocumentBulletListClockRegular: FluentIcon;
export declare const DocumentBulletListCubeFilled: FluentIcon;
export declare const DocumentBulletListCubeRegular: FluentIcon;
export declare const DocumentBulletListMultipleFilled: FluentIcon;
export declare const DocumentBulletListMultipleRegular: FluentIcon;
export declare const DocumentBulletListOffFilled: FluentIcon;
export declare const DocumentBulletListOffRegular: FluentIcon;
export declare const DocumentCatchUpFilled: FluentIcon;
export declare const DocumentCatchUpRegular: FluentIcon;
export declare const DocumentCheckmarkFilled: FluentIcon;
export declare const DocumentCheckmarkRegular: FluentIcon;
export declare const DocumentChevronDoubleFilled: FluentIcon;
export declare const DocumentChevronDoubleRegular: FluentIcon;
export declare const DocumentCopyFilled: FluentIcon;
export declare const DocumentCopyRegular: FluentIcon;
export declare const DocumentCssFilled: FluentIcon;
export declare const DocumentCssRegular: FluentIcon;
export declare const DocumentCubeFilled: FluentIcon;
export declare const DocumentCubeRegular: FluentIcon;
export declare const DocumentDataFilled: FluentIcon;
export declare const DocumentDataRegular: FluentIcon;
export declare const DocumentDataLinkFilled: FluentIcon;
export declare const DocumentDataLinkRegular: FluentIcon;
export declare const DocumentDataLockFilled: FluentIcon;
export declare const DocumentDataLockRegular: FluentIcon;
export declare const DocumentDatabaseFilled: FluentIcon;
export declare const DocumentDatabaseRegular: FluentIcon;
export declare const DocumentDismissFilled: FluentIcon;
export declare const DocumentDismissRegular: FluentIcon;
export declare const DocumentEditColor: FluentIcon;
export declare const DocumentEditFilled: FluentIcon;
export declare const DocumentEditRegular: FluentIcon;
export declare const DocumentEndnoteFilled: FluentIcon;
export declare const DocumentEndnoteRegular: FluentIcon;
export declare const DocumentErrorFilled: FluentIcon;
export declare const DocumentErrorRegular: FluentIcon;
export declare const DocumentFitFilled: FluentIcon;
export declare const DocumentFitRegular: FluentIcon;
export declare const DocumentFlowchartFilled: FluentIcon;
export declare const DocumentFlowchartRegular: FluentIcon;
export declare const DocumentFolderColor: FluentIcon;
export declare const DocumentFolderFilled: FluentIcon;
export declare const DocumentFolderRegular: FluentIcon;
export declare const DocumentFooterFilled: FluentIcon;
export declare const DocumentFooterRegular: FluentIcon;
export declare const DocumentFooterDismissFilled: FluentIcon;
export declare const DocumentFooterDismissRegular: FluentIcon;
export declare const DocumentGlobeFilled: FluentIcon;
export declare const DocumentGlobeRegular: FluentIcon;
export declare const DocumentHeaderFilled: FluentIcon;
export declare const DocumentHeaderRegular: FluentIcon;
export declare const DocumentHeaderArrowDownFilled: FluentIcon;
export declare const DocumentHeaderArrowDownRegular: FluentIcon;
export declare const DocumentHeaderDismissFilled: FluentIcon;
export declare const DocumentHeaderDismissRegular: FluentIcon;
export declare const DocumentHeaderFooterFilled: FluentIcon;
export declare const DocumentHeaderFooterRegular: FluentIcon;
export declare const DocumentHeartFilled: FluentIcon;
export declare const DocumentHeartRegular: FluentIcon;
export declare const DocumentHeartPulseFilled: FluentIcon;
export declare const DocumentHeartPulseRegular: FluentIcon;
export declare const DocumentImageFilled: FluentIcon;
export declare const DocumentImageRegular: FluentIcon;
export declare const DocumentJavaFilled: FluentIcon;
export declare const DocumentJavaRegular: FluentIcon;
export declare const DocumentJavascriptFilled: FluentIcon;
export declare const DocumentJavascriptRegular: FluentIcon;
export declare const DocumentKeyFilled: FluentIcon;
export declare const DocumentKeyRegular: FluentIcon;
export declare const DocumentLandscapeFilled: FluentIcon;
export declare const DocumentLandscapeRegular: FluentIcon;
export declare const DocumentLandscapeDataFilled: FluentIcon;
export declare const DocumentLandscapeDataRegular: FluentIcon;
export declare const DocumentLandscapeSplitFilled: FluentIcon;
export declare const DocumentLandscapeSplitRegular: FluentIcon;
export declare const DocumentLandscapeSplitHintFilled: FluentIcon;
export declare const DocumentLandscapeSplitHintRegular: FluentIcon;
export declare const DocumentLightningFilled: FluentIcon;
export declare const DocumentLightningRegular: FluentIcon;
export declare const DocumentLinkFilled: FluentIcon;
export declare const DocumentLinkRegular: FluentIcon;
export declare const DocumentLockColor: FluentIcon;
export declare const DocumentLockFilled: FluentIcon;
export declare const DocumentLockRegular: FluentIcon;
export declare const DocumentMarginsFilled: FluentIcon;
export declare const DocumentMarginsRegular: FluentIcon;
export declare const DocumentMentionFilled: FluentIcon;
export declare const DocumentMentionRegular: FluentIcon;
export declare const DocumentMultipleFilled: FluentIcon;
export declare const DocumentMultipleRegular: FluentIcon;
export declare const DocumentMultiplePercentFilled: FluentIcon;
export declare const DocumentMultiplePercentRegular: FluentIcon;
export declare const DocumentMultipleProhibitedFilled: FluentIcon;
export declare const DocumentMultipleProhibitedRegular: FluentIcon;
export declare const DocumentMultipleSyncFilled: FluentIcon;
export declare const DocumentMultipleSyncRegular: FluentIcon;
export declare const DocumentOnePageFilled: FluentIcon;
export declare const DocumentOnePageRegular: FluentIcon;
export declare const DocumentOnePageAddFilled: FluentIcon;
export declare const DocumentOnePageAddRegular: FluentIcon;
export declare const DocumentOnePageColumnsFilled: FluentIcon;
export declare const DocumentOnePageColumnsRegular: FluentIcon;
export declare const DocumentOnePageLinkFilled: FluentIcon;
export declare const DocumentOnePageLinkRegular: FluentIcon;
export declare const DocumentOnePageMultipleFilled: FluentIcon;
export declare const DocumentOnePageMultipleRegular: FluentIcon;
export declare const DocumentOnePageMultipleSparkleFilled: FluentIcon;
export declare const DocumentOnePageMultipleSparkleRegular: FluentIcon;
export declare const DocumentOnePageSparkleFilled: FluentIcon;
export declare const DocumentOnePageSparkleRegular: FluentIcon;
export declare const DocumentPageBottomCenterFilled: FluentIcon;
export declare const DocumentPageBottomCenterRegular: FluentIcon;
export declare const DocumentPageBottomLeftFilled: FluentIcon;
export declare const DocumentPageBottomLeftRegular: FluentIcon;
export declare const DocumentPageBottomRightFilled: FluentIcon;
export declare const DocumentPageBottomRightRegular: FluentIcon;
export declare const DocumentPageBreakFilled: FluentIcon;
export declare const DocumentPageBreakRegular: FluentIcon;
export declare const DocumentPageNumberFilled: FluentIcon;
export declare const DocumentPageNumberRegular: FluentIcon;
export declare const DocumentPageTopCenterFilled: FluentIcon;
export declare const DocumentPageTopCenterRegular: FluentIcon;
export declare const DocumentPageTopLeftFilled: FluentIcon;
export declare const DocumentPageTopLeftRegular: FluentIcon;
export declare const DocumentPageTopRightFilled: FluentIcon;
export declare const DocumentPageTopRightRegular: FluentIcon;
export declare const DocumentPdfFilled: FluentIcon;
export declare const DocumentPdfRegular: FluentIcon;
export declare const DocumentPercentFilled: FluentIcon;
export declare const DocumentPercentRegular: FluentIcon;
export declare const DocumentPersonFilled: FluentIcon;
export declare const DocumentPersonRegular: FluentIcon;
export declare const DocumentPillFilled: FluentIcon;
export declare const DocumentPillRegular: FluentIcon;
export declare const DocumentPrintFilled: FluentIcon;
export declare const DocumentPrintRegular: FluentIcon;
export declare const DocumentProhibitedFilled: FluentIcon;
export declare const DocumentProhibitedRegular: FluentIcon;
export declare const DocumentQuestionMarkFilled: FluentIcon;
export declare const DocumentQuestionMarkRegular: FluentIcon;
export declare const DocumentQueueFilled: FluentIcon;
export declare const DocumentQueueRegular: FluentIcon;
export declare const DocumentQueueAddFilled: FluentIcon;
export declare const DocumentQueueAddRegular: FluentIcon;
export declare const DocumentQueueMultipleFilled: FluentIcon;
export declare const DocumentQueueMultipleRegular: FluentIcon;
export declare const DocumentRibbonFilled: FluentIcon;
export declare const DocumentRibbonRegular: FluentIcon;
export declare const DocumentSassFilled: FluentIcon;
export declare const DocumentSassRegular: FluentIcon;
export declare const DocumentSaveFilled: FluentIcon;
export declare const DocumentSaveRegular: FluentIcon;
export declare const DocumentSearchFilled: FluentIcon;
export declare const DocumentSearchRegular: FluentIcon;
export declare const DocumentSettingsFilled: FluentIcon;
export declare const DocumentSettingsRegular: FluentIcon;
export declare const DocumentSignatureFilled: FluentIcon;
export declare const DocumentSignatureRegular: FluentIcon;
export declare const DocumentSparkleFilled: FluentIcon;
export declare const DocumentSparkleRegular: FluentIcon;
export declare const DocumentSplitHintFilled: FluentIcon;
export declare const DocumentSplitHintRegular: FluentIcon;
export declare const DocumentSplitHintOffFilled: FluentIcon;
export declare const DocumentSplitHintOffRegular: FluentIcon;
export declare const DocumentSquareFilled: FluentIcon;
export declare const DocumentSquareRegular: FluentIcon;
export declare const DocumentSyncFilled: FluentIcon;
export declare const DocumentSyncRegular: FluentIcon;
export declare const DocumentTableFilled: FluentIcon;
export declare const DocumentTableRegular: FluentIcon;
export declare const DocumentTableArrowRightFilled: FluentIcon;
export declare const DocumentTableArrowRightRegular: FluentIcon;
export declare const DocumentTableCheckmarkFilled: FluentIcon;
export declare const DocumentTableCheckmarkRegular: FluentIcon;
export declare const DocumentTableCubeFilled: FluentIcon;
export declare const DocumentTableCubeRegular: FluentIcon;
export declare const DocumentTableSearchFilled: FluentIcon;
export declare const DocumentTableSearchRegular: FluentIcon;
export declare const DocumentTableTruckFilled: FluentIcon;
export declare const DocumentTableTruckRegular: FluentIcon;
export declare const DocumentTargetFilled: FluentIcon;
export declare const DocumentTargetRegular: FluentIcon;
export declare const DocumentTextColor: FluentIcon;
export declare const DocumentTextFilled: FluentIcon;
export declare const DocumentTextRegular: FluentIcon;
export declare const DocumentTextClockFilled: FluentIcon;
export declare const DocumentTextClockRegular: FluentIcon;
export declare const DocumentTextExtractFilled: FluentIcon;
export declare const DocumentTextExtractRegular: FluentIcon;
export declare const DocumentTextLinkFilled: FluentIcon;
export declare const DocumentTextLinkRegular: FluentIcon;
export declare const DocumentTextToolboxFilled: FluentIcon;
export declare const DocumentTextToolboxRegular: FluentIcon;
export declare const DocumentToolboxFilled: FluentIcon;
export declare const DocumentToolboxRegular: FluentIcon;
export declare const DocumentWidthFilled: FluentIcon;
export declare const DocumentWidthRegular: FluentIcon;
export declare const DocumentYmlFilled: FluentIcon;
export declare const DocumentYmlRegular: FluentIcon;
export declare const DoorFilled: FluentIcon;
export declare const DoorRegular: FluentIcon;
export declare const DoorArrowLeftFilled: FluentIcon;
export declare const DoorArrowLeftRegular: FluentIcon;
export declare const DoorArrowRightFilled: FluentIcon;
export declare const DoorArrowRightRegular: FluentIcon;
export declare const DoorTagFilled: FluentIcon;
export declare const DoorTagRegular: FluentIcon;
export declare const DoubleSwipeDownFilled: FluentIcon;
export declare const DoubleSwipeDownRegular: FluentIcon;
export declare const DoubleSwipeUpFilled: FluentIcon;
export declare const DoubleSwipeUpRegular: FluentIcon;
export declare const DoubleTapSwipeDownFilled: FluentIcon;
export declare const DoubleTapSwipeDownRegular: FluentIcon;
