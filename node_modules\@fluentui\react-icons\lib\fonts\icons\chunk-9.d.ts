import type { FluentFontIcon } from "../../utils/fonts/createFluentFontIcon";
export declare const TagFilled: FluentFontIcon;
export declare const TagRegular: FluentFontIcon;
export declare const TagAddFilled: FluentFontIcon;
export declare const TagAddRegular: FluentFontIcon;
export declare const TagCircleFilled: FluentFontIcon;
export declare const TagCircleRegular: FluentFontIcon;
export declare const TagDismissFilled: FluentFontIcon;
export declare const TagDismissRegular: FluentFontIcon;
export declare const TagEditFilled: FluentFontIcon;
export declare const TagEditRegular: FluentFontIcon;
export declare const TagErrorFilled: FluentFontIcon;
export declare const TagErrorRegular: FluentFontIcon;
export declare const TagLockFilled: FluentFontIcon;
export declare const TagLockRegular: FluentFontIcon;
export declare const TagLockAccentFilled: FluentFontIcon;
export declare const TagMultipleFilled: FluentFontIcon;
export declare const TagMultipleRegular: FluentFontIcon;
export declare const TagOffFilled: FluentFontIcon;
export declare const TagOffRegular: FluentFontIcon;
export declare const TagPercentFilled: FluentFontIcon;
export declare const TagPercentRegular: FluentFontIcon;
export declare const TagQuestionMarkFilled: FluentFontIcon;
export declare const TagQuestionMarkRegular: FluentFontIcon;
export declare const TagResetFilled: FluentFontIcon;
export declare const TagResetRegular: FluentFontIcon;
export declare const TagSearchFilled: FluentFontIcon;
export declare const TagSearchRegular: FluentFontIcon;
export declare const TapDoubleFilled: FluentFontIcon;
export declare const TapDoubleRegular: FluentFontIcon;
export declare const TapSingleFilled: FluentFontIcon;
export declare const TapSingleRegular: FluentFontIcon;
export declare const TargetFilled: FluentFontIcon;
export declare const TargetRegular: FluentFontIcon;
export declare const TargetAddFilled: FluentFontIcon;
export declare const TargetAddRegular: FluentFontIcon;
export declare const TargetArrowFilled: FluentFontIcon;
export declare const TargetArrowRegular: FluentFontIcon;
export declare const TargetDismissFilled: FluentFontIcon;
export declare const TargetDismissRegular: FluentFontIcon;
export declare const TargetEditFilled: FluentFontIcon;
export declare const TargetEditRegular: FluentFontIcon;
export declare const TargetSparkleFilled: FluentFontIcon;
export declare const TargetSparkleRegular: FluentFontIcon;
export declare const TaskListAddFilled: FluentFontIcon;
export declare const TaskListAddRegular: FluentFontIcon;
export declare const TaskListLtrFilled: FluentFontIcon;
export declare const TaskListLtrRegular: FluentFontIcon;
export declare const TaskListRtlFilled: FluentFontIcon;
export declare const TaskListRtlRegular: FluentFontIcon;
export declare const TaskListSquareAddFilled: FluentFontIcon;
export declare const TaskListSquareAddRegular: FluentFontIcon;
export declare const TaskListSquareDatabaseFilled: FluentFontIcon;
export declare const TaskListSquareDatabaseRegular: FluentFontIcon;
export declare const TaskListSquareLtrFilled: FluentFontIcon;
export declare const TaskListSquareLtrRegular: FluentFontIcon;
export declare const TaskListSquarePersonFilled: FluentFontIcon;
export declare const TaskListSquarePersonRegular: FluentFontIcon;
export declare const TaskListSquareRtlFilled: FluentFontIcon;
export declare const TaskListSquareRtlRegular: FluentFontIcon;
export declare const TaskListSquareSettingsFilled: FluentFontIcon;
export declare const TaskListSquareSettingsRegular: FluentFontIcon;
export declare const TaskListSquareSparkleFilled: FluentFontIcon;
export declare const TaskListSquareSparkleRegular: FluentFontIcon;
export declare const TasksAppFilled: FluentFontIcon;
export declare const TasksAppRegular: FluentFontIcon;
export declare const TeachingFilled: FluentFontIcon;
export declare const TeachingRegular: FluentFontIcon;
export declare const TeardropBottomRightFilled: FluentFontIcon;
export declare const TeardropBottomRightRegular: FluentFontIcon;
export declare const TeddyFilled: FluentFontIcon;
export declare const TeddyRegular: FluentFontIcon;
export declare const TemperatureFilled: FluentFontIcon;
export declare const TemperatureRegular: FluentFontIcon;
export declare const TemperatureDegreeCelsiusFilled: FluentFontIcon;
export declare const TemperatureDegreeCelsiusRegular: FluentFontIcon;
export declare const TemperatureDegreeFahrenheitFilled: FluentFontIcon;
export declare const TemperatureDegreeFahrenheitRegular: FluentFontIcon;
export declare const TentFilled: FluentFontIcon;
export declare const TentRegular: FluentFontIcon;
export declare const TetrisAppFilled: FluentFontIcon;
export declare const TetrisAppRegular: FluentFontIcon;
export declare const TextAddFilled: FluentFontIcon;
export declare const TextAddRegular: FluentFontIcon;
export declare const TextAddSpaceAfterFilled: FluentFontIcon;
export declare const TextAddSpaceAfterRegular: FluentFontIcon;
export declare const TextAddSpaceBeforeFilled: FluentFontIcon;
export declare const TextAddSpaceBeforeRegular: FluentFontIcon;
export declare const TextAddTFilled: FluentFontIcon;
export declare const TextAddTRegular: FluentFontIcon;
export declare const TextAlignCenterFilled: FluentFontIcon;
export declare const TextAlignCenterRegular: FluentFontIcon;
export declare const TextAlignCenterRotate270Filled: FluentFontIcon;
export declare const TextAlignCenterRotate270Regular: FluentFontIcon;
export declare const TextAlignCenterRotate90Filled: FluentFontIcon;
export declare const TextAlignCenterRotate90Regular: FluentFontIcon;
export declare const TextAlignDistributedFilled: FluentFontIcon;
export declare const TextAlignDistributedRegular: FluentFontIcon;
export declare const TextAlignDistributedEvenlyFilled: FluentFontIcon;
export declare const TextAlignDistributedEvenlyRegular: FluentFontIcon;
export declare const TextAlignDistributedVerticalFilled: FluentFontIcon;
export declare const TextAlignDistributedVerticalRegular: FluentFontIcon;
export declare const TextAlignJustifyFilled: FluentFontIcon;
export declare const TextAlignJustifyRegular: FluentFontIcon;
export declare const TextAlignJustifyLowFilled: FluentFontIcon;
export declare const TextAlignJustifyLowRegular: FluentFontIcon;
export declare const TextAlignJustifyLow90Filled: FluentFontIcon;
export declare const TextAlignJustifyLow90Regular: FluentFontIcon;
export declare const TextAlignJustifyLowRotate270Filled: FluentFontIcon;
export declare const TextAlignJustifyLowRotate270Regular: FluentFontIcon;
export declare const TextAlignJustifyLowRotate90Filled: FluentFontIcon;
export declare const TextAlignJustifyLowRotate90Regular: FluentFontIcon;
export declare const TextAlignJustifyRotate270Filled: FluentFontIcon;
export declare const TextAlignJustifyRotate270Regular: FluentFontIcon;
export declare const TextAlignJustifyRotate90Filled: FluentFontIcon;
export declare const TextAlignJustifyRotate90Regular: FluentFontIcon;
export declare const TextAlignLeftFilled: FluentFontIcon;
export declare const TextAlignLeftRegular: FluentFontIcon;
export declare const TextAlignLeftRotate270Filled: FluentFontIcon;
export declare const TextAlignLeftRotate270Regular: FluentFontIcon;
export declare const TextAlignLeftRotate90Filled: FluentFontIcon;
export declare const TextAlignLeftRotate90Regular: FluentFontIcon;
export declare const TextAlignRightFilled: FluentFontIcon;
export declare const TextAlignRightRegular: FluentFontIcon;
export declare const TextAlignRightRotate270Filled: FluentFontIcon;
export declare const TextAlignRightRotate270Regular: FluentFontIcon;
export declare const TextAlignRightRotate90Filled: FluentFontIcon;
export declare const TextAlignRightRotate90Regular: FluentFontIcon;
export declare const TextArrowDownRightColumnFilled: FluentFontIcon;
export declare const TextArrowDownRightColumnRegular: FluentFontIcon;
export declare const TextAsteriskFilled: FluentFontIcon;
export declare const TextAsteriskRegular: FluentFontIcon;
export declare const TextBaselineFilled: FluentFontIcon;
export declare const TextBaselineRegular: FluentFontIcon;
export declare const TextBoldFilled: FluentFontIcon;
export declare const TextBoldRegular: FluentFontIcon;
export declare const TextBoxSettingsFilled: FluentFontIcon;
export declare const TextBoxSettingsRegular: FluentFontIcon;
export declare const TextBulletListFilled: FluentFontIcon;
export declare const TextBulletListRegular: FluentFontIcon;
export declare const TextBulletList90Filled: FluentFontIcon;
export declare const TextBulletList90Regular: FluentFontIcon;
export declare const TextBulletListAddFilled: FluentFontIcon;
export declare const TextBulletListAddRegular: FluentFontIcon;
export declare const TextBulletListCheckmarkFilled: FluentFontIcon;
export declare const TextBulletListCheckmarkRegular: FluentFontIcon;
export declare const TextBulletListDismissFilled: FluentFontIcon;
export declare const TextBulletListDismissRegular: FluentFontIcon;
export declare const TextBulletListLtrFilled: FluentFontIcon;
export declare const TextBulletListLtrRegular: FluentFontIcon;
export declare const TextBulletListLtr90Filled: FluentFontIcon;
export declare const TextBulletListLtr90Regular: FluentFontIcon;
export declare const TextBulletListRtlFilled: FluentFontIcon;
export declare const TextBulletListRtlRegular: FluentFontIcon;
export declare const TextBulletListRtl90Filled: FluentFontIcon;
export declare const TextBulletListRtl90Regular: FluentFontIcon;
export declare const TextBulletListSquareFilled: FluentFontIcon;
export declare const TextBulletListSquareRegular: FluentFontIcon;
export declare const TextBulletListSquareClockFilled: FluentFontIcon;
export declare const TextBulletListSquareClockRegular: FluentFontIcon;
export declare const TextBulletListSquareEditFilled: FluentFontIcon;
export declare const TextBulletListSquareEditRegular: FluentFontIcon;
export declare const TextBulletListSquarePersonFilled: FluentFontIcon;
export declare const TextBulletListSquarePersonRegular: FluentFontIcon;
export declare const TextBulletListSquareSearchFilled: FluentFontIcon;
export declare const TextBulletListSquareSearchRegular: FluentFontIcon;
export declare const TextBulletListSquareSettingsFilled: FluentFontIcon;
export declare const TextBulletListSquareSettingsRegular: FluentFontIcon;
export declare const TextBulletListSquareShieldFilled: FluentFontIcon;
export declare const TextBulletListSquareShieldRegular: FluentFontIcon;
export declare const TextBulletListSquareSparkleFilled: FluentFontIcon;
export declare const TextBulletListSquareSparkleRegular: FluentFontIcon;
export declare const TextBulletListSquareToolboxFilled: FluentFontIcon;
export declare const TextBulletListSquareToolboxRegular: FluentFontIcon;
export declare const TextBulletListSquareWarningFilled: FluentFontIcon;
export declare const TextBulletListSquareWarningRegular: FluentFontIcon;
export declare const TextBulletListTreeFilled: FluentFontIcon;
export declare const TextBulletListTreeRegular: FluentFontIcon;
export declare const TextCaseLowercaseFilled: FluentFontIcon;
export declare const TextCaseLowercaseRegular: FluentFontIcon;
export declare const TextCaseTitleFilled: FluentFontIcon;
export declare const TextCaseTitleRegular: FluentFontIcon;
export declare const TextCaseUppercaseFilled: FluentFontIcon;
export declare const TextCaseUppercaseRegular: FluentFontIcon;
export declare const TextChangeCaseFilled: FluentFontIcon;
export declare const TextChangeCaseRegular: FluentFontIcon;
export declare const TextClearFormattingFilled: FluentFontIcon;
export declare const TextClearFormattingRegular: FluentFontIcon;
export declare const TextCollapseFilled: FluentFontIcon;
export declare const TextCollapseRegular: FluentFontIcon;
export declare const TextColorFilled: FluentFontIcon;
export declare const TextColorRegular: FluentFontIcon;
export declare const TextColorAccentFilled: FluentFontIcon;
export declare const TextColumnOneFilled: FluentFontIcon;
export declare const TextColumnOneRegular: FluentFontIcon;
export declare const TextColumnOneNarrowFilled: FluentFontIcon;
export declare const TextColumnOneNarrowRegular: FluentFontIcon;
export declare const TextColumnOneSemiNarrowFilled: FluentFontIcon;
export declare const TextColumnOneSemiNarrowRegular: FluentFontIcon;
export declare const TextColumnOneWideFilled: FluentFontIcon;
export declare const TextColumnOneWideRegular: FluentFontIcon;
export declare const TextColumnOneWideLightningFilled: FluentFontIcon;
export declare const TextColumnOneWideLightningRegular: FluentFontIcon;
export declare const TextColumnThreeFilled: FluentFontIcon;
export declare const TextColumnThreeRegular: FluentFontIcon;
export declare const TextColumnTwoFilled: FluentFontIcon;
export declare const TextColumnTwoRegular: FluentFontIcon;
export declare const TextColumnTwoLeftFilled: FluentFontIcon;
export declare const TextColumnTwoLeftRegular: FluentFontIcon;
export declare const TextColumnTwoRightFilled: FluentFontIcon;
export declare const TextColumnTwoRightRegular: FluentFontIcon;
export declare const TextColumnWideFilled: FluentFontIcon;
export declare const TextColumnWideRegular: FluentFontIcon;
export declare const TextContinuousFilled: FluentFontIcon;
export declare const TextContinuousRegular: FluentFontIcon;
export declare const TextDensityFilled: FluentFontIcon;
export declare const TextDensityRegular: FluentFontIcon;
export declare const TextDescriptionFilled: FluentFontIcon;
export declare const TextDescriptionRegular: FluentFontIcon;
export declare const TextDescriptionLtrFilled: FluentFontIcon;
export declare const TextDescriptionLtrRegular: FluentFontIcon;
export declare const TextDescriptionRtlFilled: FluentFontIcon;
export declare const TextDescriptionRtlRegular: FluentFontIcon;
export declare const TextDirectionHorizontalLeftFilled: FluentFontIcon;
export declare const TextDirectionHorizontalLeftRegular: FluentFontIcon;
export declare const TextDirectionHorizontalLtrFilled: FluentFontIcon;
export declare const TextDirectionHorizontalLtrRegular: FluentFontIcon;
export declare const TextDirectionHorizontalRightFilled: FluentFontIcon;
export declare const TextDirectionHorizontalRightRegular: FluentFontIcon;
export declare const TextDirectionHorizontalRtlFilled: FluentFontIcon;
export declare const TextDirectionHorizontalRtlRegular: FluentFontIcon;
export declare const TextDirectionRotate270RightFilled: FluentFontIcon;
export declare const TextDirectionRotate270RightRegular: FluentFontIcon;
export declare const TextDirectionRotate315RightFilled: FluentFontIcon;
export declare const TextDirectionRotate315RightRegular: FluentFontIcon;
export declare const TextDirectionRotate45RightFilled: FluentFontIcon;
export declare const TextDirectionRotate45RightRegular: FluentFontIcon;
export declare const TextDirectionRotate90LeftFilled: FluentFontIcon;
export declare const TextDirectionRotate90LeftRegular: FluentFontIcon;
export declare const TextDirectionRotate90LtrFilled: FluentFontIcon;
export declare const TextDirectionRotate90LtrRegular: FluentFontIcon;
export declare const TextDirectionRotate90RightFilled: FluentFontIcon;
export declare const TextDirectionRotate90RightRegular: FluentFontIcon;
export declare const TextDirectionRotate90RtlFilled: FluentFontIcon;
export declare const TextDirectionRotate90RtlRegular: FluentFontIcon;
export declare const TextDirectionVerticalFilled: FluentFontIcon;
export declare const TextDirectionVerticalRegular: FluentFontIcon;
export declare const TextEditStyleFilled: FluentFontIcon;
export declare const TextEditStyleRegular: FluentFontIcon;
export declare const TextEffectsFilled: FluentFontIcon;
export declare const TextEffectsRegular: FluentFontIcon;
export declare const TextEffectsSparkleFilled: FluentFontIcon;
export declare const TextEffectsSparkleRegular: FluentFontIcon;
export declare const TextExpandFilled: FluentFontIcon;
export declare const TextExpandRegular: FluentFontIcon;
export declare const TextFieldFilled: FluentFontIcon;
export declare const TextFieldRegular: FluentFontIcon;
export declare const TextFirstLineFilled: FluentFontIcon;
export declare const TextFirstLineRegular: FluentFontIcon;
export declare const TextFontFilled: FluentFontIcon;
export declare const TextFontRegular: FluentFontIcon;
export declare const TextFontInfoFilled: FluentFontIcon;
export declare const TextFontInfoRegular: FluentFontIcon;
export declare const TextFontSizeFilled: FluentFontIcon;
export declare const TextFontSizeRegular: FluentFontIcon;
export declare const TextFootnoteFilled: FluentFontIcon;
export declare const TextFootnoteRegular: FluentFontIcon;
export declare const TextGrammarArrowLeftFilled: FluentFontIcon;
export declare const TextGrammarArrowLeftRegular: FluentFontIcon;
export declare const TextGrammarArrowRightFilled: FluentFontIcon;
export declare const TextGrammarArrowRightRegular: FluentFontIcon;
export declare const TextGrammarCheckmarkFilled: FluentFontIcon;
export declare const TextGrammarCheckmarkRegular: FluentFontIcon;
export declare const TextGrammarDismissFilled: FluentFontIcon;
export declare const TextGrammarDismissRegular: FluentFontIcon;
export declare const TextGrammarErrorFilled: FluentFontIcon;
export declare const TextGrammarErrorRegular: FluentFontIcon;
export declare const TextGrammarLightningFilled: FluentFontIcon;
export declare const TextGrammarLightningRegular: FluentFontIcon;
export declare const TextGrammarSettingsFilled: FluentFontIcon;
export declare const TextGrammarSettingsRegular: FluentFontIcon;
export declare const TextGrammarWandFilled: FluentFontIcon;
export declare const TextGrammarWandRegular: FluentFontIcon;
export declare const TextHangingFilled: FluentFontIcon;
export declare const TextHangingRegular: FluentFontIcon;
export declare const TextHeader1Filled: FluentFontIcon;
export declare const TextHeader1Regular: FluentFontIcon;
export declare const TextHeader1LinesFilled: FluentFontIcon;
export declare const TextHeader1LinesRegular: FluentFontIcon;
export declare const TextHeader1LinesCaretFilled: FluentFontIcon;
export declare const TextHeader1LinesCaretRegular: FluentFontIcon;
export declare const TextHeader2Filled: FluentFontIcon;
export declare const TextHeader2Regular: FluentFontIcon;
export declare const TextHeader2LinesFilled: FluentFontIcon;
export declare const TextHeader2LinesRegular: FluentFontIcon;
export declare const TextHeader2LinesCaretFilled: FluentFontIcon;
export declare const TextHeader2LinesCaretRegular: FluentFontIcon;
export declare const TextHeader3Filled: FluentFontIcon;
export declare const TextHeader3Regular: FluentFontIcon;
export declare const TextHeader3LinesFilled: FluentFontIcon;
export declare const TextHeader3LinesRegular: FluentFontIcon;
export declare const TextHeader3LinesCaretFilled: FluentFontIcon;
export declare const TextHeader3LinesCaretRegular: FluentFontIcon;
export declare const TextHeader4Filled: FluentFontIcon;
export declare const TextHeader4Regular: FluentFontIcon;
export declare const TextHeader4LinesCaretFilled: FluentFontIcon;
export declare const TextHeader4LinesCaretRegular: FluentFontIcon;
export declare const TextHeader5Filled: FluentFontIcon;
export declare const TextHeader5Regular: FluentFontIcon;
export declare const TextHeader6Filled: FluentFontIcon;
export declare const TextHeader6Regular: FluentFontIcon;
export declare const TextIndentDecreaseFilled: FluentFontIcon;
export declare const TextIndentDecreaseRegular: FluentFontIcon;
export declare const TextIndentDecreaseLtrFilled: FluentFontIcon;
export declare const TextIndentDecreaseLtrRegular: FluentFontIcon;
export declare const TextIndentDecreaseLtr90Filled: FluentFontIcon;
export declare const TextIndentDecreaseLtr90Regular: FluentFontIcon;
export declare const TextIndentDecreaseLtrRotate270Filled: FluentFontIcon;
export declare const TextIndentDecreaseLtrRotate270Regular: FluentFontIcon;
export declare const TextIndentDecreaseRotate270Filled: FluentFontIcon;
export declare const TextIndentDecreaseRotate270Regular: FluentFontIcon;
export declare const TextIndentDecreaseRotate90Filled: FluentFontIcon;
export declare const TextIndentDecreaseRotate90Regular: FluentFontIcon;
export declare const TextIndentDecreaseRtlFilled: FluentFontIcon;
export declare const TextIndentDecreaseRtlRegular: FluentFontIcon;
export declare const TextIndentDecreaseRtl90Filled: FluentFontIcon;
export declare const TextIndentDecreaseRtl90Regular: FluentFontIcon;
export declare const TextIndentDecreaseRtlRotate270Filled: FluentFontIcon;
export declare const TextIndentDecreaseRtlRotate270Regular: FluentFontIcon;
export declare const TextIndentIncreaseFilled: FluentFontIcon;
export declare const TextIndentIncreaseRegular: FluentFontIcon;
export declare const TextIndentIncreaseLtrFilled: FluentFontIcon;
export declare const TextIndentIncreaseLtrRegular: FluentFontIcon;
export declare const TextIndentIncreaseLtr90Filled: FluentFontIcon;
export declare const TextIndentIncreaseLtr90Regular: FluentFontIcon;
export declare const TextIndentIncreaseLtrRotate270Filled: FluentFontIcon;
export declare const TextIndentIncreaseLtrRotate270Regular: FluentFontIcon;
export declare const TextIndentIncreaseRotate270Filled: FluentFontIcon;
export declare const TextIndentIncreaseRotate270Regular: FluentFontIcon;
export declare const TextIndentIncreaseRotate90Filled: FluentFontIcon;
export declare const TextIndentIncreaseRotate90Regular: FluentFontIcon;
export declare const TextIndentIncreaseRtlFilled: FluentFontIcon;
export declare const TextIndentIncreaseRtlRegular: FluentFontIcon;
export declare const TextIndentIncreaseRtl90Filled: FluentFontIcon;
export declare const TextIndentIncreaseRtl90Regular: FluentFontIcon;
export declare const TextIndentIncreaseRtlRotate270Filled: FluentFontIcon;
export declare const TextIndentIncreaseRtlRotate270Regular: FluentFontIcon;
export declare const TextItalicFilled: FluentFontIcon;
export declare const TextItalicRegular: FluentFontIcon;
export declare const TextLineSpacingFilled: FluentFontIcon;
export declare const TextLineSpacingRegular: FluentFontIcon;
export declare const TextListAbcLowercaseLtrFilled: FluentFontIcon;
export declare const TextListAbcLowercaseLtrRegular: FluentFontIcon;
export declare const TextListAbcUppercaseLtrFilled: FluentFontIcon;
export declare const TextListAbcUppercaseLtrRegular: FluentFontIcon;
export declare const TextListRomanNumeralLowercaseFilled: FluentFontIcon;
export declare const TextListRomanNumeralLowercaseRegular: FluentFontIcon;
export declare const TextListRomanNumeralUppercaseFilled: FluentFontIcon;
export declare const TextListRomanNumeralUppercaseRegular: FluentFontIcon;
export declare const TextMoreFilled: FluentFontIcon;
export declare const TextMoreRegular: FluentFontIcon;
export declare const TextNumberFormatFilled: FluentFontIcon;
export declare const TextNumberFormatRegular: FluentFontIcon;
export declare const TextNumberListLtrFilled: FluentFontIcon;
export declare const TextNumberListLtrRegular: FluentFontIcon;
export declare const TextNumberListLtr90Filled: FluentFontIcon;
export declare const TextNumberListLtr90Regular: FluentFontIcon;
export declare const TextNumberListLtrRotate270Filled: FluentFontIcon;
export declare const TextNumberListLtrRotate270Regular: FluentFontIcon;
export declare const TextNumberListRotate270Filled: FluentFontIcon;
export declare const TextNumberListRotate270Regular: FluentFontIcon;
export declare const TextNumberListRotate90Filled: FluentFontIcon;
export declare const TextNumberListRotate90Regular: FluentFontIcon;
export declare const TextNumberListRtlFilled: FluentFontIcon;
export declare const TextNumberListRtlRegular: FluentFontIcon;
export declare const TextNumberListRtl90Filled: FluentFontIcon;
export declare const TextNumberListRtl90Regular: FluentFontIcon;
export declare const TextNumberListRtlRotate270Filled: FluentFontIcon;
export declare const TextNumberListRtlRotate270Regular: FluentFontIcon;
export declare const TextParagraphFilled: FluentFontIcon;
export declare const TextParagraphRegular: FluentFontIcon;
export declare const TextParagraphDirectionFilled: FluentFontIcon;
export declare const TextParagraphDirectionRegular: FluentFontIcon;
export declare const TextParagraphDirectionLeftFilled: FluentFontIcon;
export declare const TextParagraphDirectionLeftRegular: FluentFontIcon;
export declare const TextParagraphDirectionRightFilled: FluentFontIcon;
export declare const TextParagraphDirectionRightRegular: FluentFontIcon;
export declare const TextPercentFilled: FluentFontIcon;
export declare const TextPercentRegular: FluentFontIcon;
export declare const TextPeriodAsteriskFilled: FluentFontIcon;
export declare const TextPeriodAsteriskRegular: FluentFontIcon;
export declare const TextPositionBehindFilled: FluentFontIcon;
export declare const TextPositionBehindRegular: FluentFontIcon;
export declare const TextPositionFrontFilled: FluentFontIcon;
export declare const TextPositionFrontRegular: FluentFontIcon;
export declare const TextPositionLineFilled: FluentFontIcon;
export declare const TextPositionLineRegular: FluentFontIcon;
export declare const TextPositionSquareFilled: FluentFontIcon;
export declare const TextPositionSquareRegular: FluentFontIcon;
export declare const TextPositionSquareLeftFilled: FluentFontIcon;
export declare const TextPositionSquareLeftRegular: FluentFontIcon;
export declare const TextPositionSquareRightFilled: FluentFontIcon;
export declare const TextPositionSquareRightRegular: FluentFontIcon;
export declare const TextPositionThroughFilled: FluentFontIcon;
export declare const TextPositionThroughRegular: FluentFontIcon;
export declare const TextPositionTightFilled: FluentFontIcon;
export declare const TextPositionTightRegular: FluentFontIcon;
export declare const TextPositionTopBottomFilled: FluentFontIcon;
export declare const TextPositionTopBottomRegular: FluentFontIcon;
export declare const TextProofingToolsFilled: FluentFontIcon;
export declare const TextProofingToolsRegular: FluentFontIcon;
export declare const TextQuoteFilled: FluentFontIcon;
export declare const TextQuoteRegular: FluentFontIcon;
export declare const TextQuoteOpeningFilled: FluentFontIcon;
export declare const TextQuoteOpeningRegular: FluentFontIcon;
export declare const TextSortAscendingFilled: FluentFontIcon;
export declare const TextSortAscendingRegular: FluentFontIcon;
export declare const TextSortDescendingFilled: FluentFontIcon;
export declare const TextSortDescendingRegular: FluentFontIcon;
export declare const TextStrikethroughFilled: FluentFontIcon;
export declare const TextStrikethroughRegular: FluentFontIcon;
export declare const TextSubscriptFilled: FluentFontIcon;
export declare const TextSubscriptRegular: FluentFontIcon;
export declare const TextSuperscriptFilled: FluentFontIcon;
export declare const TextSuperscriptRegular: FluentFontIcon;
export declare const TextTFilled: FluentFontIcon;
export declare const TextTRegular: FluentFontIcon;
export declare const TextUnderlineFilled: FluentFontIcon;
export declare const TextUnderlineRegular: FluentFontIcon;
export declare const TextUnderlineCharacterUFilled: FluentFontIcon;
export declare const TextUnderlineCharacterURegular: FluentFontIcon;
export declare const TextUnderlineDoubleFilled: FluentFontIcon;
export declare const TextUnderlineDoubleRegular: FluentFontIcon;
export declare const TextWholeWordFilled: FluentFontIcon;
export declare const TextWholeWordRegular: FluentFontIcon;
export declare const TextWordCountFilled: FluentFontIcon;
export declare const TextWordCountRegular: FluentFontIcon;
export declare const TextWrapFilled: FluentFontIcon;
export declare const TextWrapRegular: FluentFontIcon;
export declare const TextWrapOffFilled: FluentFontIcon;
export declare const TextWrapOffRegular: FluentFontIcon;
export declare const TextboxFilled: FluentFontIcon;
export declare const TextboxRegular: FluentFontIcon;
export declare const TextboxAlignBottomFilled: FluentFontIcon;
export declare const TextboxAlignBottomRegular: FluentFontIcon;
export declare const TextboxAlignBottomCenterFilled: FluentFontIcon;
export declare const TextboxAlignBottomCenterRegular: FluentFontIcon;
export declare const TextboxAlignBottomLeftFilled: FluentFontIcon;
export declare const TextboxAlignBottomLeftRegular: FluentFontIcon;
export declare const TextboxAlignBottomRightFilled: FluentFontIcon;
export declare const TextboxAlignBottomRightRegular: FluentFontIcon;
export declare const TextboxAlignBottomRotate90Filled: FluentFontIcon;
export declare const TextboxAlignBottomRotate90Regular: FluentFontIcon;
export declare const TextboxAlignCenterFilled: FluentFontIcon;
export declare const TextboxAlignCenterRegular: FluentFontIcon;
export declare const TextboxAlignMiddleFilled: FluentFontIcon;
export declare const TextboxAlignMiddleRegular: FluentFontIcon;
export declare const TextboxAlignMiddleLeftFilled: FluentFontIcon;
export declare const TextboxAlignMiddleLeftRegular: FluentFontIcon;
export declare const TextboxAlignMiddleRightFilled: FluentFontIcon;
export declare const TextboxAlignMiddleRightRegular: FluentFontIcon;
export declare const TextboxAlignMiddleRotate90Filled: FluentFontIcon;
export declare const TextboxAlignMiddleRotate90Regular: FluentFontIcon;
export declare const TextboxAlignTopFilled: FluentFontIcon;
export declare const TextboxAlignTopRegular: FluentFontIcon;
export declare const TextboxAlignTopCenterFilled: FluentFontIcon;
export declare const TextboxAlignTopCenterRegular: FluentFontIcon;
export declare const TextboxAlignTopLeftFilled: FluentFontIcon;
export declare const TextboxAlignTopLeftRegular: FluentFontIcon;
export declare const TextboxAlignTopRightFilled: FluentFontIcon;
export declare const TextboxAlignTopRightRegular: FluentFontIcon;
export declare const TextboxAlignTopRotate90Filled: FluentFontIcon;
export declare const TextboxAlignTopRotate90Regular: FluentFontIcon;
export declare const TextboxCheckmarkFilled: FluentFontIcon;
export declare const TextboxCheckmarkRegular: FluentFontIcon;
export declare const TextboxMoreFilled: FluentFontIcon;
export declare const TextboxMoreRegular: FluentFontIcon;
export declare const TextboxRotate90Filled: FluentFontIcon;
export declare const TextboxRotate90Regular: FluentFontIcon;
export declare const TextboxSettingsFilled: FluentFontIcon;
export declare const TextboxSettingsRegular: FluentFontIcon;
export declare const ThinkingFilled: FluentFontIcon;
export declare const ThinkingRegular: FluentFontIcon;
export declare const ThumbDislikeFilled: FluentFontIcon;
export declare const ThumbDislikeRegular: FluentFontIcon;
export declare const ThumbLikeFilled: FluentFontIcon;
export declare const ThumbLikeRegular: FluentFontIcon;
export declare const ThumbLikeDislikeFilled: FluentFontIcon;
export declare const ThumbLikeDislikeRegular: FluentFontIcon;
export declare const TicketDiagonalFilled: FluentFontIcon;
export declare const TicketDiagonalRegular: FluentFontIcon;
export declare const TicketHorizontalFilled: FluentFontIcon;
export declare const TicketHorizontalRegular: FluentFontIcon;
export declare const TimeAndWeatherFilled: FluentFontIcon;
export declare const TimeAndWeatherRegular: FluentFontIcon;
export declare const TimePickerFilled: FluentFontIcon;
export declare const TimePickerRegular: FluentFontIcon;
export declare const TimelineFilled: FluentFontIcon;
export declare const TimelineRegular: FluentFontIcon;
export declare const Timer10Filled: FluentFontIcon;
export declare const Timer10Regular: FluentFontIcon;
