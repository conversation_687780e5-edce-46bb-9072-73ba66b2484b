import type { FluentFontIcon } from "../../utils/fonts/createFluentFontIcon";
export declare const MoneyHandRegular: FluentFontIcon;
export declare const MoneyOffFilled: FluentFontIcon;
export declare const MoneyOffRegular: FluentFontIcon;
export declare const MoneySettingsFilled: FluentFontIcon;
export declare const MoneySettingsRegular: FluentFontIcon;
export declare const MoreCircleFilled: FluentFontIcon;
export declare const MoreCircleRegular: FluentFontIcon;
export declare const MoreHorizontalFilled: FluentFontIcon;
export declare const MoreHorizontalRegular: FluentFontIcon;
export declare const MoreVerticalFilled: FluentFontIcon;
export declare const MoreVerticalRegular: FluentFontIcon;
export declare const MountainLocationBottomFilled: FluentFontIcon;
export declare const MountainLocationBottomRegular: FluentFontIcon;
export declare const MountainLocationTopFilled: FluentFontIcon;
export declare const MountainLocationTopRegular: FluentFontIcon;
export declare const MountainTrailFilled: FluentFontIcon;
export declare const MountainTrailRegular: FluentFontIcon;
export declare const MoviesAndTvFilled: FluentFontIcon;
export declare const MoviesAndTvRegular: FluentFontIcon;
export declare const Multiplier12XFilled: FluentFontIcon;
export declare const Multiplier12XRegular: FluentFontIcon;
export declare const Multiplier15XFilled: FluentFontIcon;
export declare const Multiplier15XRegular: FluentFontIcon;
export declare const Multiplier18XFilled: FluentFontIcon;
export declare const Multiplier18XRegular: FluentFontIcon;
export declare const Multiplier1XFilled: FluentFontIcon;
export declare const Multiplier1XRegular: FluentFontIcon;
export declare const Multiplier2XFilled: FluentFontIcon;
export declare const Multiplier2XRegular: FluentFontIcon;
export declare const Multiplier5XFilled: FluentFontIcon;
export declare const Multiplier5XRegular: FluentFontIcon;
export declare const MultiselectLtrFilled: FluentFontIcon;
export declare const MultiselectLtrRegular: FluentFontIcon;
export declare const MultiselectRtlFilled: FluentFontIcon;
export declare const MultiselectRtlRegular: FluentFontIcon;
export declare const MusicNote1Filled: FluentFontIcon;
export declare const MusicNote1Regular: FluentFontIcon;
export declare const MusicNote2Filled: FluentFontIcon;
export declare const MusicNote2Regular: FluentFontIcon;
export declare const MusicNote2PlayFilled: FluentFontIcon;
export declare const MusicNote2PlayRegular: FluentFontIcon;
export declare const MusicNoteOff1Filled: FluentFontIcon;
export declare const MusicNoteOff1Regular: FluentFontIcon;
export declare const MusicNoteOff2Filled: FluentFontIcon;
export declare const MusicNoteOff2Regular: FluentFontIcon;
export declare const MyLocationFilled: FluentFontIcon;
export declare const MyLocationRegular: FluentFontIcon;
export declare const NavigationFilled: FluentFontIcon;
export declare const NavigationRegular: FluentFontIcon;
export declare const NavigationBriefcaseFilled: FluentFontIcon;
export declare const NavigationBriefcaseRegular: FluentFontIcon;
export declare const NavigationLocationTargetFilled: FluentFontIcon;
export declare const NavigationLocationTargetRegular: FluentFontIcon;
export declare const NavigationPersonFilled: FluentFontIcon;
export declare const NavigationPersonRegular: FluentFontIcon;
export declare const NavigationPlayFilled: FluentFontIcon;
export declare const NavigationPlayRegular: FluentFontIcon;
export declare const NavigationUnreadFilled: FluentFontIcon;
export declare const NavigationUnreadRegular: FluentFontIcon;
export declare const NetworkCheckFilled: FluentFontIcon;
export declare const NetworkCheckRegular: FluentFontIcon;
export declare const NewFilled: FluentFontIcon;
export declare const NewRegular: FluentFontIcon;
export declare const NewsFilled: FluentFontIcon;
export declare const NewsRegular: FluentFontIcon;
export declare const NextFilled: FluentFontIcon;
export declare const NextRegular: FluentFontIcon;
export declare const NextFrameFilled: FluentFontIcon;
export declare const NextFrameRegular: FluentFontIcon;
export declare const NoteFilled: FluentFontIcon;
export declare const NoteRegular: FluentFontIcon;
export declare const NoteAddFilled: FluentFontIcon;
export declare const NoteAddRegular: FluentFontIcon;
export declare const NoteEditFilled: FluentFontIcon;
export declare const NoteEditRegular: FluentFontIcon;
export declare const NotePinFilled: FluentFontIcon;
export declare const NotePinRegular: FluentFontIcon;
export declare const NotebookFilled: FluentFontIcon;
export declare const NotebookRegular: FluentFontIcon;
export declare const NotebookAddFilled: FluentFontIcon;
export declare const NotebookAddRegular: FluentFontIcon;
export declare const NotebookArrowCurveDownFilled: FluentFontIcon;
export declare const NotebookArrowCurveDownRegular: FluentFontIcon;
export declare const NotebookErrorFilled: FluentFontIcon;
export declare const NotebookErrorRegular: FluentFontIcon;
export declare const NotebookEyeFilled: FluentFontIcon;
export declare const NotebookEyeRegular: FluentFontIcon;
export declare const NotebookLightningFilled: FluentFontIcon;
export declare const NotebookLightningRegular: FluentFontIcon;
export declare const NotebookQuestionMarkFilled: FluentFontIcon;
export declare const NotebookQuestionMarkRegular: FluentFontIcon;
export declare const NotebookSectionFilled: FluentFontIcon;
export declare const NotebookSectionRegular: FluentFontIcon;
export declare const NotebookSectionArrowRightFilled: FluentFontIcon;
export declare const NotebookSectionArrowRightRegular: FluentFontIcon;
export declare const NotebookSubsectionFilled: FluentFontIcon;
export declare const NotebookSubsectionRegular: FluentFontIcon;
export declare const NotebookSyncFilled: FluentFontIcon;
export declare const NotebookSyncRegular: FluentFontIcon;
export declare const NotepadFilled: FluentFontIcon;
export declare const NotepadRegular: FluentFontIcon;
export declare const NotepadEditFilled: FluentFontIcon;
export declare const NotepadEditRegular: FluentFontIcon;
export declare const NotepadPersonFilled: FluentFontIcon;
export declare const NotepadPersonRegular: FluentFontIcon;
export declare const NotepadPersonOffFilled: FluentFontIcon;
export declare const NotepadPersonOffRegular: FluentFontIcon;
export declare const NotepadSparkleFilled: FluentFontIcon;
export declare const NotepadSparkleRegular: FluentFontIcon;
export declare const NumberCircle0Filled: FluentFontIcon;
export declare const NumberCircle0Regular: FluentFontIcon;
export declare const NumberCircle1Filled: FluentFontIcon;
export declare const NumberCircle1Regular: FluentFontIcon;
export declare const NumberCircle2Filled: FluentFontIcon;
export declare const NumberCircle2Regular: FluentFontIcon;
export declare const NumberCircle3Filled: FluentFontIcon;
export declare const NumberCircle3Regular: FluentFontIcon;
export declare const NumberCircle4Filled: FluentFontIcon;
export declare const NumberCircle4Regular: FluentFontIcon;
export declare const NumberCircle5Filled: FluentFontIcon;
export declare const NumberCircle5Regular: FluentFontIcon;
export declare const NumberCircle6Filled: FluentFontIcon;
export declare const NumberCircle6Regular: FluentFontIcon;
export declare const NumberCircle7Filled: FluentFontIcon;
export declare const NumberCircle7Regular: FluentFontIcon;
export declare const NumberCircle8Filled: FluentFontIcon;
export declare const NumberCircle8Regular: FluentFontIcon;
export declare const NumberCircle9Filled: FluentFontIcon;
export declare const NumberCircle9Regular: FluentFontIcon;
export declare const NumberRowFilled: FluentFontIcon;
export declare const NumberRowRegular: FluentFontIcon;
export declare const NumberSymbolFilled: FluentFontIcon;
export declare const NumberSymbolRegular: FluentFontIcon;
export declare const NumberSymbolDismissFilled: FluentFontIcon;
export declare const NumberSymbolDismissRegular: FluentFontIcon;
export declare const NumberSymbolSquareFilled: FluentFontIcon;
export declare const NumberSymbolSquareRegular: FluentFontIcon;
export declare const OpenFilled: FluentFontIcon;
export declare const OpenRegular: FluentFontIcon;
export declare const OpenFolderFilled: FluentFontIcon;
export declare const OpenFolderRegular: FluentFontIcon;
export declare const OpenOffFilled: FluentFontIcon;
export declare const OpenOffRegular: FluentFontIcon;
export declare const OptionsFilled: FluentFontIcon;
export declare const OptionsRegular: FluentFontIcon;
export declare const OrganizationFilled: FluentFontIcon;
export declare const OrganizationRegular: FluentFontIcon;
export declare const OrganizationHorizontalFilled: FluentFontIcon;
export declare const OrganizationHorizontalRegular: FluentFontIcon;
export declare const OrientationFilled: FluentFontIcon;
export declare const OrientationRegular: FluentFontIcon;
export declare const OvalFilled: FluentFontIcon;
export declare const OvalRegular: FluentFontIcon;
export declare const OvenFilled: FluentFontIcon;
export declare const OvenRegular: FluentFontIcon;
export declare const PaddingDownFilled: FluentFontIcon;
export declare const PaddingDownRegular: FluentFontIcon;
export declare const PaddingLeftFilled: FluentFontIcon;
export declare const PaddingLeftRegular: FluentFontIcon;
export declare const PaddingRightFilled: FluentFontIcon;
export declare const PaddingRightRegular: FluentFontIcon;
export declare const PaddingTopFilled: FluentFontIcon;
export declare const PaddingTopRegular: FluentFontIcon;
export declare const PageFitFilled: FluentFontIcon;
export declare const PageFitRegular: FluentFontIcon;
export declare const PaintBrushFilled: FluentFontIcon;
export declare const PaintBrushRegular: FluentFontIcon;
export declare const PaintBrushArrowDownFilled: FluentFontIcon;
export declare const PaintBrushArrowDownRegular: FluentFontIcon;
export declare const PaintBrushArrowUpFilled: FluentFontIcon;
export declare const PaintBrushArrowUpRegular: FluentFontIcon;
export declare const PaintBrushSparkleFilled: FluentFontIcon;
export declare const PaintBrushSparkleRegular: FluentFontIcon;
export declare const PaintBrushSubtractFilled: FluentFontIcon;
export declare const PaintBrushSubtractRegular: FluentFontIcon;
export declare const PaintBucketFilled: FluentFontIcon;
export declare const PaintBucketRegular: FluentFontIcon;
export declare const PaintBucketBrushFilled: FluentFontIcon;
export declare const PaintBucketBrushRegular: FluentFontIcon;
export declare const PairFilled: FluentFontIcon;
export declare const PairRegular: FluentFontIcon;
export declare const PanelBottomFilled: FluentFontIcon;
export declare const PanelBottomRegular: FluentFontIcon;
export declare const PanelBottomContractFilled: FluentFontIcon;
export declare const PanelBottomContractRegular: FluentFontIcon;
export declare const PanelBottomExpandFilled: FluentFontIcon;
export declare const PanelBottomExpandRegular: FluentFontIcon;
export declare const PanelLeftFilled: FluentFontIcon;
export declare const PanelLeftRegular: FluentFontIcon;
export declare const PanelLeftAddFilled: FluentFontIcon;
export declare const PanelLeftAddRegular: FluentFontIcon;
export declare const PanelLeftContractFilled: FluentFontIcon;
export declare const PanelLeftContractRegular: FluentFontIcon;
export declare const PanelLeftExpandFilled: FluentFontIcon;
export declare const PanelLeftExpandRegular: FluentFontIcon;
export declare const PanelLeftFocusRightFilled: FluentFontIcon;
export declare const PanelLeftHeaderFilled: FluentFontIcon;
export declare const PanelLeftHeaderRegular: FluentFontIcon;
export declare const PanelLeftHeaderAddFilled: FluentFontIcon;
export declare const PanelLeftHeaderAddRegular: FluentFontIcon;
export declare const PanelLeftHeaderKeyFilled: FluentFontIcon;
export declare const PanelLeftHeaderKeyRegular: FluentFontIcon;
export declare const PanelLeftKeyFilled: FluentFontIcon;
export declare const PanelLeftKeyRegular: FluentFontIcon;
export declare const PanelLeftTextFilled: FluentFontIcon;
export declare const PanelLeftTextRegular: FluentFontIcon;
export declare const PanelLeftTextAddFilled: FluentFontIcon;
export declare const PanelLeftTextAddRegular: FluentFontIcon;
export declare const PanelLeftTextDismissFilled: FluentFontIcon;
export declare const PanelLeftTextDismissRegular: FluentFontIcon;
export declare const PanelRightFilled: FluentFontIcon;
export declare const PanelRightRegular: FluentFontIcon;
export declare const PanelRightAddFilled: FluentFontIcon;
export declare const PanelRightAddRegular: FluentFontIcon;
export declare const PanelRightContractFilled: FluentFontIcon;
export declare const PanelRightContractRegular: FluentFontIcon;
export declare const PanelRightCursorFilled: FluentFontIcon;
export declare const PanelRightCursorRegular: FluentFontIcon;
export declare const PanelRightExpandFilled: FluentFontIcon;
export declare const PanelRightExpandRegular: FluentFontIcon;
export declare const PanelRightGalleryFilled: FluentFontIcon;
export declare const PanelRightGalleryRegular: FluentFontIcon;
export declare const PanelSeparateWindowFilled: FluentFontIcon;
export declare const PanelSeparateWindowRegular: FluentFontIcon;
export declare const PanelTopContractFilled: FluentFontIcon;
export declare const PanelTopContractRegular: FluentFontIcon;
export declare const PanelTopExpandFilled: FluentFontIcon;
export declare const PanelTopExpandRegular: FluentFontIcon;
export declare const PanelTopGalleryFilled: FluentFontIcon;
export declare const PanelTopGalleryRegular: FluentFontIcon;
export declare const PasswordFilled: FluentFontIcon;
export declare const PasswordRegular: FluentFontIcon;
export declare const PasswordClockFilled: FluentFontIcon;
export declare const PasswordClockRegular: FluentFontIcon;
export declare const PatchFilled: FluentFontIcon;
export declare const PatchRegular: FluentFontIcon;
export declare const PatientFilled: FluentFontIcon;
export declare const PatientRegular: FluentFontIcon;
export declare const PauseFilled: FluentFontIcon;
export declare const PauseRegular: FluentFontIcon;
export declare const PauseCircleFilled: FluentFontIcon;
export declare const PauseCircleRegular: FluentFontIcon;
export declare const PauseOffFilled: FluentFontIcon;
export declare const PauseOffRegular: FluentFontIcon;
export declare const PauseSettingsFilled: FluentFontIcon;
export declare const PauseSettingsRegular: FluentFontIcon;
export declare const PaymentFilled: FluentFontIcon;
export declare const PaymentRegular: FluentFontIcon;
export declare const PaymentWirelessFilled: FluentFontIcon;
export declare const PaymentWirelessRegular: FluentFontIcon;
export declare const PenFilled: FluentFontIcon;
export declare const PenRegular: FluentFontIcon;
export declare const PenDismissFilled: FluentFontIcon;
export declare const PenDismissRegular: FluentFontIcon;
export declare const PenOffFilled: FluentFontIcon;
export declare const PenOffRegular: FluentFontIcon;
export declare const PenProhibitedFilled: FluentFontIcon;
export declare const PenProhibitedRegular: FluentFontIcon;
export declare const PenSparkleFilled: FluentFontIcon;
export declare const PenSparkleRegular: FluentFontIcon;
export declare const PenSyncFilled: FluentFontIcon;
export declare const PenSyncRegular: FluentFontIcon;
export declare const PentagonFilled: FluentFontIcon;
export declare const PentagonRegular: FluentFontIcon;
export declare const PeopleFilled: FluentFontIcon;
export declare const PeopleRegular: FluentFontIcon;
export declare const PeopleAddFilled: FluentFontIcon;
export declare const PeopleAddRegular: FluentFontIcon;
export declare const PeopleAudienceFilled: FluentFontIcon;
export declare const PeopleAudienceRegular: FluentFontIcon;
export declare const PeopleCallFilled: FluentFontIcon;
export declare const PeopleCallRegular: FluentFontIcon;
export declare const PeopleChatFilled: FluentFontIcon;
export declare const PeopleChatRegular: FluentFontIcon;
export declare const PeopleCheckmarkFilled: FluentFontIcon;
export declare const PeopleCheckmarkRegular: FluentFontIcon;
export declare const PeopleCommunicationFilled: FluentFontIcon;
export declare const PeopleCommunicationRegular: FluentFontIcon;
export declare const PeopleCommunityFilled: FluentFontIcon;
export declare const PeopleCommunityRegular: FluentFontIcon;
export declare const PeopleCommunityAddFilled: FluentFontIcon;
export declare const PeopleCommunityAddRegular: FluentFontIcon;
export declare const PeopleEditFilled: FluentFontIcon;
export declare const PeopleEditRegular: FluentFontIcon;
export declare const PeopleErrorFilled: FluentFontIcon;
export declare const PeopleErrorRegular: FluentFontIcon;
export declare const PeopleEyeFilled: FluentFontIcon;
export declare const PeopleEyeRegular: FluentFontIcon;
export declare const PeopleInterwovenFilled: FluentFontIcon;
export declare const PeopleInterwovenRegular: FluentFontIcon;
export declare const PeopleLinkFilled: FluentFontIcon;
export declare const PeopleLinkRegular: FluentFontIcon;
export declare const PeopleListFilled: FluentFontIcon;
export declare const PeopleListRegular: FluentFontIcon;
export declare const PeopleLockFilled: FluentFontIcon;
export declare const PeopleLockRegular: FluentFontIcon;
export declare const PeopleMoneyFilled: FluentFontIcon;
export declare const PeopleMoneyRegular: FluentFontIcon;
export declare const PeopleProhibitedFilled: FluentFontIcon;
export declare const PeopleProhibitedRegular: FluentFontIcon;
export declare const PeopleQueueFilled: FluentFontIcon;
export declare const PeopleQueueRegular: FluentFontIcon;
export declare const PeopleSearchFilled: FluentFontIcon;
export declare const PeopleSearchRegular: FluentFontIcon;
export declare const PeopleSettingsFilled: FluentFontIcon;
export declare const PeopleSettingsRegular: FluentFontIcon;
export declare const PeopleStarFilled: FluentFontIcon;
export declare const PeopleStarRegular: FluentFontIcon;
export declare const PeopleSubtractFilled: FluentFontIcon;
export declare const PeopleSubtractRegular: FluentFontIcon;
export declare const PeopleSwapFilled: FluentFontIcon;
export declare const PeopleSwapRegular: FluentFontIcon;
export declare const PeopleSyncFilled: FluentFontIcon;
export declare const PeopleSyncRegular: FluentFontIcon;
export declare const PeopleTeamFilled: FluentFontIcon;
export declare const PeopleTeamRegular: FluentFontIcon;
export declare const PeopleTeamAddFilled: FluentFontIcon;
export declare const PeopleTeamAddRegular: FluentFontIcon;
export declare const PeopleTeamDeleteFilled: FluentFontIcon;
export declare const PeopleTeamDeleteRegular: FluentFontIcon;
export declare const PeopleTeamToolboxFilled: FluentFontIcon;
export declare const PeopleTeamToolboxRegular: FluentFontIcon;
export declare const PeopleToolboxFilled: FluentFontIcon;
export declare const PeopleToolboxRegular: FluentFontIcon;
export declare const PersonFilled: FluentFontIcon;
export declare const PersonRegular: FluentFontIcon;
export declare const Person5Filled: FluentFontIcon;
export declare const Person5Regular: FluentFontIcon;
export declare const Person6Filled: FluentFontIcon;
export declare const Person6Regular: FluentFontIcon;
export declare const PersonAccountsFilled: FluentFontIcon;
export declare const PersonAccountsRegular: FluentFontIcon;
export declare const PersonAddFilled: FluentFontIcon;
export declare const PersonAddRegular: FluentFontIcon;
export declare const PersonAlertFilled: FluentFontIcon;
export declare const PersonAlertRegular: FluentFontIcon;
export declare const PersonAlertOffFilled: FluentFontIcon;
export declare const PersonAlertOffRegular: FluentFontIcon;
export declare const PersonArrowBackFilled: FluentFontIcon;
export declare const PersonArrowBackRegular: FluentFontIcon;
export declare const PersonArrowLeftFilled: FluentFontIcon;
export declare const PersonArrowLeftRegular: FluentFontIcon;
export declare const PersonArrowRightFilled: FluentFontIcon;
export declare const PersonArrowRightRegular: FluentFontIcon;
export declare const PersonAvailableFilled: FluentFontIcon;
export declare const PersonAvailableRegular: FluentFontIcon;
export declare const PersonBoardFilled: FluentFontIcon;
export declare const PersonBoardRegular: FluentFontIcon;
export declare const PersonBoardAddFilled: FluentFontIcon;
export declare const PersonBoardAddRegular: FluentFontIcon;
export declare const PersonBriefcaseFilled: FluentFontIcon;
export declare const PersonBriefcaseRegular: FluentFontIcon;
export declare const PersonCallFilled: FluentFontIcon;
export declare const PersonCallRegular: FluentFontIcon;
export declare const PersonChatFilled: FluentFontIcon;
export declare const PersonChatRegular: FluentFontIcon;
export declare const PersonCircleFilled: FluentFontIcon;
export declare const PersonCircleRegular: FluentFontIcon;
export declare const PersonClockFilled: FluentFontIcon;
export declare const PersonClockRegular: FluentFontIcon;
export declare const PersonDeleteFilled: FluentFontIcon;
export declare const PersonDeleteRegular: FluentFontIcon;
export declare const PersonDesktopFilled: FluentFontIcon;
export declare const PersonDesktopRegular: FluentFontIcon;
export declare const PersonEditFilled: FluentFontIcon;
export declare const PersonEditRegular: FluentFontIcon;
export declare const PersonErrorFilled: FluentFontIcon;
export declare const PersonErrorRegular: FluentFontIcon;
export declare const PersonFeedbackFilled: FluentFontIcon;
export declare const PersonFeedbackRegular: FluentFontIcon;
export declare const PersonGuestFilled: FluentFontIcon;
export declare const PersonGuestRegular: FluentFontIcon;
export declare const PersonHeadHintFilled: FluentFontIcon;
export declare const PersonHeadHintRegular: FluentFontIcon;
export declare const PersonHeartFilled: FluentFontIcon;
export declare const PersonHeartRegular: FluentFontIcon;
export declare const PersonHomeFilled: FluentFontIcon;
export declare const PersonHomeRegular: FluentFontIcon;
export declare const PersonInfoFilled: FluentFontIcon;
export declare const PersonInfoRegular: FluentFontIcon;
export declare const PersonKeyFilled: FluentFontIcon;
export declare const PersonKeyRegular: FluentFontIcon;
export declare const PersonLightbulbFilled: FluentFontIcon;
export declare const PersonLightbulbRegular: FluentFontIcon;
export declare const PersonLightningFilled: FluentFontIcon;
export declare const PersonLightningRegular: FluentFontIcon;
export declare const PersonLinkFilled: FluentFontIcon;
export declare const PersonLinkRegular: FluentFontIcon;
export declare const PersonLockFilled: FluentFontIcon;
export declare const PersonLockRegular: FluentFontIcon;
export declare const PersonMailFilled: FluentFontIcon;
export declare const PersonMailRegular: FluentFontIcon;
export declare const PersonMoneyFilled: FluentFontIcon;
export declare const PersonMoneyRegular: FluentFontIcon;
export declare const PersonNoteFilled: FluentFontIcon;
export declare const PersonNoteRegular: FluentFontIcon;
export declare const PersonPasskeyFilled: FluentFontIcon;
export declare const PersonPasskeyRegular: FluentFontIcon;
export declare const PersonPillFilled: FluentFontIcon;
export declare const PersonPillRegular: FluentFontIcon;
export declare const PersonProhibitedFilled: FluentFontIcon;
export declare const PersonProhibitedRegular: FluentFontIcon;
export declare const PersonQuestionMarkFilled: FluentFontIcon;
export declare const PersonQuestionMarkRegular: FluentFontIcon;
export declare const PersonRibbonFilled: FluentFontIcon;
export declare const PersonRibbonRegular: FluentFontIcon;
export declare const PersonRunningFilled: FluentFontIcon;
export declare const PersonRunningRegular: FluentFontIcon;
export declare const PersonSearchFilled: FluentFontIcon;
export declare const PersonSearchRegular: FluentFontIcon;
export declare const PersonSettingsFilled: FluentFontIcon;
export declare const PersonSettingsRegular: FluentFontIcon;
export declare const PersonShieldFilled: FluentFontIcon;
export declare const PersonShieldRegular: FluentFontIcon;
export declare const PersonSoundSpatialFilled: FluentFontIcon;
export declare const PersonSoundSpatialRegular: FluentFontIcon;
export declare const PersonSquareFilled: FluentFontIcon;
export declare const PersonSquareRegular: FluentFontIcon;
export declare const PersonSquareAddFilled: FluentFontIcon;
export declare const PersonSquareAddRegular: FluentFontIcon;
export declare const PersonSquareCheckmarkFilled: FluentFontIcon;
export declare const PersonSquareCheckmarkRegular: FluentFontIcon;
export declare const PersonStarFilled: FluentFontIcon;
export declare const PersonStarRegular: FluentFontIcon;
export declare const PersonStarburstFilled: FluentFontIcon;
export declare const PersonStarburstRegular: FluentFontIcon;
export declare const PersonSubtractFilled: FluentFontIcon;
export declare const PersonSubtractRegular: FluentFontIcon;
export declare const PersonSupportFilled: FluentFontIcon;
export declare const PersonSupportRegular: FluentFontIcon;
export declare const PersonSwapFilled: FluentFontIcon;
export declare const PersonSwapRegular: FluentFontIcon;
export declare const PersonSyncFilled: FluentFontIcon;
export declare const PersonSyncRegular: FluentFontIcon;
export declare const PersonTagFilled: FluentFontIcon;
export declare const PersonTagRegular: FluentFontIcon;
export declare const PersonTentativeFilled: FluentFontIcon;
export declare const PersonTentativeRegular: FluentFontIcon;
export declare const PersonVoiceFilled: FluentFontIcon;
export declare const PersonVoiceRegular: FluentFontIcon;
export declare const PersonWalkingFilled: FluentFontIcon;
export declare const PersonWalkingRegular: FluentFontIcon;
export declare const PersonWarningFilled: FluentFontIcon;
export declare const PersonWarningRegular: FluentFontIcon;
export declare const PersonWrenchFilled: FluentFontIcon;
export declare const PersonWrenchRegular: FluentFontIcon;
export declare const PhoneFilled: FluentFontIcon;
export declare const PhoneRegular: FluentFontIcon;
export declare const PhoneAddFilled: FluentFontIcon;
export declare const PhoneAddRegular: FluentFontIcon;
export declare const PhoneArrowRightFilled: FluentFontIcon;
export declare const PhoneArrowRightRegular: FluentFontIcon;
export declare const PhoneChatFilled: FluentFontIcon;
export declare const PhoneChatRegular: FluentFontIcon;
export declare const PhoneCheckmarkFilled: FluentFontIcon;
export declare const PhoneCheckmarkRegular: FluentFontIcon;
export declare const PhoneDesktopFilled: FluentFontIcon;
export declare const PhoneDesktopRegular: FluentFontIcon;
export declare const PhoneDesktopAddFilled: FluentFontIcon;
export declare const PhoneDesktopAddRegular: FluentFontIcon;
export declare const PhoneDismissFilled: FluentFontIcon;
export declare const PhoneDismissRegular: FluentFontIcon;
export declare const PhoneEditFilled: FluentFontIcon;
export declare const PhoneEditRegular: FluentFontIcon;
export declare const PhoneEraserFilled: FluentFontIcon;
export declare const PhoneEraserRegular: FluentFontIcon;
export declare const PhoneFooterArrowDownFilled: FluentFontIcon;
export declare const PhoneFooterArrowDownRegular: FluentFontIcon;
export declare const PhoneHeaderArrowUpFilled: FluentFontIcon;
export declare const PhoneHeaderArrowUpRegular: FluentFontIcon;
export declare const PhoneKeyFilled: FluentFontIcon;
export declare const PhoneKeyRegular: FluentFontIcon;
export declare const PhoneLaptopFilled: FluentFontIcon;
export declare const PhoneLaptopRegular: FluentFontIcon;
export declare const PhoneLinkSetupFilled: FluentFontIcon;
export declare const PhoneLinkSetupRegular: FluentFontIcon;
export declare const PhoneLockFilled: FluentFontIcon;
export declare const PhoneLockRegular: FluentFontIcon;
export declare const PhonePageHeaderFilled: FluentFontIcon;
export declare const PhonePageHeaderRegular: FluentFontIcon;
export declare const PhonePaginationFilled: FluentFontIcon;
export declare const PhonePaginationRegular: FluentFontIcon;
export declare const PhoneScreenTimeFilled: FluentFontIcon;
export declare const PhoneScreenTimeRegular: FluentFontIcon;
export declare const PhoneShakeFilled: FluentFontIcon;
export declare const PhoneShakeRegular: FluentFontIcon;
export declare const PhoneSpanInFilled: FluentFontIcon;
export declare const PhoneSpanInRegular: FluentFontIcon;
export declare const PhoneSpanOutFilled: FluentFontIcon;
export declare const PhoneSpanOutRegular: FluentFontIcon;
export declare const PhoneSpeakerFilled: FluentFontIcon;
export declare const PhoneSpeakerRegular: FluentFontIcon;
export declare const PhoneStatusBarFilled: FluentFontIcon;
export declare const PhoneStatusBarRegular: FluentFontIcon;
export declare const PhoneTabletFilled: FluentFontIcon;
export declare const PhoneTabletRegular: FluentFontIcon;
export declare const PhoneUpdateFilled: FluentFontIcon;
export declare const PhoneUpdateRegular: FluentFontIcon;
export declare const PhoneUpdateCheckmarkFilled: FluentFontIcon;
export declare const PhoneUpdateCheckmarkRegular: FluentFontIcon;
